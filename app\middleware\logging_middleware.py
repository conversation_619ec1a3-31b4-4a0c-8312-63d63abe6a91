"""
Logging middleware for Flask application.
Provides request/response logging and performance monitoring.
"""
import time
from typing import Optional
from flask import Flask, request, g
from werkzeug.serving import W<PERSON>GIRequestHandler

from src.utils.logger import get_logger, log_request

logger = get_logger(__name__)


def register_logging_middleware(app: Flask) -> None:
    """
    Register logging middleware for the Flask application.
    
    Args:
        app: Flask application instance
    """
    logger.info("Registering logging middleware...")
    
    @app.before_request
    def before_request():
        """Log request start and setup timing."""
        g.start_time = time.time()
        
        # Store request start time for error responses
        request.environ['REQUEST_START_TIME'] = g.start_time
        
        # Log incoming request (excluding health checks to reduce noise)
        if not request.path.endswith('/health'):
            # Get user ID from session if available
            user_id = None
            if hasattr(g, 'user') and g.user:
                user_id = getattr(g.user, 'id', None)
            
            # Get request ID if available
            req_id = getattr(g, 'request_id', None)
            
            # Log request start with context
            log_request(
                req_id=req_id or 'unknown',
                method=request.method,
                path=request.path,
                user_id=user_id
            )
    
    @app.after_request
    def after_request(response):
        """Log request completion and performance metrics."""
        # Calculate request duration
        duration = time.time() - getattr(g, 'start_time', time.time())
        
        # Log response (excluding health checks to reduce noise)
        if not request.path.endswith('/health'):
            # Get user ID and request ID for context
            user_id = None
            if hasattr(g, 'user') and g.user:
                user_id = getattr(g.user, 'id', None)
            
            req_id = getattr(g, 'request_id', None)
            
            # Log request completion with performance metrics
            logger.info(
                f"Request completed: {request.method} {request.path} - Status: {response.status_code} - Duration: {duration:.3f}s",
                extra={
                    'user_id': user_id,
                    'req_id': req_id,
                    'duration': duration,
                    'status_code': response.status_code
                }
            )
        
        # Add performance headers
        response.headers['X-Response-Time'] = f"{duration:.3f}s"
        
        return response
    
    @app.teardown_request
    def teardown_request(exception=None):
        """Handle request teardown and log any exceptions."""
        if exception:
            duration = time.time() - getattr(g, 'start_time', time.time())
            
            # Get user ID and request ID for context
            user_id = None
            if hasattr(g, 'user') and g.user:
                user_id = getattr(g.user, 'id', None)
            
            req_id = getattr(g, 'request_id', None)
            
            # Log exception with context
            logger.error(
                f"Request exception: {request.method} {request.path} - Error: {str(exception)} - Duration: {duration:.3f}s",
                extra={
                    'user_id': user_id,
                    'req_id': req_id,
                    'duration': duration,
                    'exception': str(exception)
                }
            )
    
    logger.info("Logging middleware registered successfully")


def log_request_start() -> None:
    """Log the start of a request."""
    log_data = {
        "event": "request_start",
        "method": request.method,
        "path": request.path,
        "endpoint": request.endpoint,
        "remote_addr": get_client_ip(),
        "user_agent": request.headers.get('User-Agent', 'Unknown'),
        "content_type": request.content_type,
        "content_length": request.content_length
    }
    
    # Add query parameters (excluding sensitive data)
    if request.args:
        safe_args = {k: v for k, v in request.args.items() 
                    if k.lower() not in ['password', 'token', 'secret', 'key']}
        if safe_args:
            log_data["query_params"] = safe_args
    
    # Add session info if available
    if hasattr(request, 'session') and request.session:
        log_data["session_id"] = request.session.get('user_id', 'anonymous')
    
    logger.info("Request started", extra=log_data)


def log_request_completion(response, duration: float) -> None:
    """
    Log the completion of a request.
    
    Args:
        response: Flask response object
        duration: Request duration in seconds
    """
    log_data = {
        "event": "request_complete",
        "method": request.method,
        "path": request.path,
        "endpoint": request.endpoint,
        "status_code": response.status_code,
        "duration_ms": round(duration * 1000, 2),
        "response_size": response.content_length,
        "remote_addr": get_client_ip()
    }
    
    # Determine log level based on status code and duration
    if response.status_code >= 500:
        log_level = "error"
    elif response.status_code >= 400:
        log_level = "warning"
    elif duration > 5.0:  # Slow requests
        log_level = "warning"
        log_data["slow_request"] = True
    else:
        log_level = "info"
    
    # Log at appropriate level
    getattr(logger, log_level)("Request completed", extra=log_data)


def log_request_exception(exception, duration: float) -> None:
    """
    Log request exceptions.
    
    Args:
        exception: Exception that occurred
        duration: Request duration in seconds
    """
    log_data = {
        "event": "request_exception",
        "method": request.method,
        "path": request.path,
        "endpoint": request.endpoint,
        "exception_type": type(exception).__name__,
        "exception_message": str(exception),
        "duration_ms": round(duration * 1000, 2),
        "remote_addr": get_client_ip()
    }
    
    logger.error("Request exception occurred", extra=log_data, exc_info=True)


def get_client_ip() -> str:
    """
    Get the real client IP address, considering proxies.
    
    Returns:
        Client IP address as string
    """
    # Check for forwarded headers (common in production behind proxies)
    forwarded_ips = [
        request.headers.get('X-Forwarded-For'),
        request.headers.get('X-Real-IP'),
        request.headers.get('CF-Connecting-IP'),  # Cloudflare
        request.headers.get('X-Client-IP')
    ]
    
    for ip_header in forwarded_ips:
        if ip_header:
            # X-Forwarded-For can contain multiple IPs, take the first one
            ip = ip_header.split(',')[0].strip()
            if ip and ip != 'unknown':
                return ip
    
    # Fallback to direct connection IP
    return request.remote_addr or 'unknown'


def log_performance_metrics(endpoint: str, duration: float, **kwargs) -> None:
    """
    Log performance metrics for specific operations.
    
    Args:
        endpoint: Endpoint or operation name
        duration: Operation duration in seconds
        **kwargs: Additional metrics to log
    """
    log_data = {
        "event": "performance_metric",
        "endpoint": endpoint,
        "duration_ms": round(duration * 1000, 2),
        **kwargs
    }
    
    # Determine if this is a slow operation
    if duration > 2.0:
        log_data["slow_operation"] = True
        logger.warning("Slow operation detected", extra=log_data)
    else:
        logger.info("Performance metric", extra=log_data)


class RequestTimer:
    """Context manager for timing operations within requests."""
    
    def __init__(self, operation_name: str):
        """
        Initialize request timer.
        
        Args:
            operation_name: Name of the operation being timed
        """
        self.operation_name = operation_name
        self.start_time: Optional[float] = None
    
    def __enter__(self):
        """Start timing."""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """End timing and log results."""
        if self.start_time:
            duration = time.time() - self.start_time
            
            # Log with exception info if an error occurred
            if exc_type:
                log_performance_metrics(
                    self.operation_name,
                    duration,
                    error=True,
                    exception_type=exc_type.__name__ if exc_type else None
                )
            else:
                log_performance_metrics(self.operation_name, duration)


# Custom request handler to reduce logging noise from werkzeug
class QuietRequestHandler(WSGIRequestHandler):
    """Custom request handler with reduced logging."""
    
    def log_request(self, code='-', size='-'):
        """Override to reduce werkzeug request logging."""
        # Only log errors and important requests
        if isinstance(code, int) and code >= 400:
            super().log_request(code, size)
        # Skip logging for successful requests (we handle this in our middleware)
