"""
Flask application factory for creating and configuring the HR Assistant Chatbot app.
Implements clean separation of concerns and production-ready configuration.
"""
import os
from datetime import datetime
from flask import Flask, jsonify, request, g
from flask_cors import CORS
import uuid

from src.utils.logger import get_logger, log_request
from app.middleware.error_handler import register_error_handlers
from app.middleware.logging_middleware import register_logging_middleware

logger = get_logger(__name__)


def create_app(config_name: str = 'default') -> Flask:
    """
    Create and configure Flask application using factory pattern.
    
    Args:
        config_name: Configuration environment name
        
    Returns:
        Configured Flask application instance
    """
    logger.info("Creating Flask application...")
    
    # Create Flask app
    app = Flask(__name__, static_folder="../../react-frontend/dist")
    
    # Configure app
    _configure_app(app, config_name)
    
    # Setup CORS
    _setup_cors(app)
    
    # Register middleware
    _register_middleware(app)
    
    # Register blueprints/routes
    _register_routes(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Add request context logging
    _add_request_logging(app)
    
    logger.info("Flask application created successfully")
    return app


def _configure_app(app: Flask, config_name: str) -> None:
    """Configure Flask application settings."""
    logger.info(f"Configuring app for environment: {config_name}")
    
    # Basic Flask configuration
    app.config['TEMPLATES_AUTO_RELOAD'] = True
    app.jinja_env.cache = {}
    
    # Session configuration for 2FA and authentication
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # Use 'Lax' for local development
    app.config['SESSION_COOKIE_SECURE'] = False     # False for local HTTP development
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    
    # Set secret key for session management
    app.secret_key = os.environ.get('FLASK_SECRET_KEY', os.urandom(24))
    
    # Environment-specific configuration
    if config_name == 'production':
        app.config['SESSION_COOKIE_SECURE'] = True
        app.config['SESSION_COOKIE_SAMESITE'] = 'None'
    elif config_name == 'testing':
        app.config['TESTING'] = True
    
    logger.info("App configuration completed")


def _setup_cors(app: Flask) -> None:
    """Setup CORS configuration for cross-origin requests."""
    logger.info("Setting up CORS...")
    
    # CORS configuration for development and production
    allowed_origins = [
        "http://localhost:3000",
        "http://127.0.0.1:3000", 
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:3002",  # Admin dashboard
        "http://127.0.0.1:3002"
    ]
    
    # Add production origins from environment
    prod_origins = os.environ.get('ALLOWED_ORIGINS', '').split(',')
    allowed_origins.extend([origin.strip() for origin in prod_origins if origin.strip()])
    
    CORS(
        app,
        origins=allowed_origins,
        supports_credentials=True,
        allow_headers=["*"],
        methods=["GET", "POST", "OPTIONS", "PUT", "DELETE"]
    )
    
    logger.info("CORS configuration completed")


def _register_middleware(app: Flask) -> None:
    """Register application middleware."""
    logger.info("Registering middleware...")
    
    # Register logging middleware
    register_logging_middleware(app)
    
    logger.info("Middleware registration completed")


def _add_request_logging(app: Flask) -> None:
    """Add request logging with unique request IDs."""
    logger.info("Adding request logging...")
    
    @app.before_request
    def before_request():
        """Generate request ID and log request start."""
        # Generate unique request ID
        g.request_id = str(uuid.uuid4())[:8]
        
        # Get user ID from session if available
        user_id = None
        if hasattr(g, 'user') and g.user:
            user_id = getattr(g.user, 'id', None)
        
        # Log request start
        log_request(
            req_id=g.request_id,
            method=request.method,
            path=request.path,
            user_id=user_id
        )
    
    logger.info("Request logging added successfully")


def _register_routes(app: Flask) -> None:
    """Register application routes and blueprints."""
    logger.info("Registering routes...")
    
    # Import and register route blueprints
    from app.api.auth_routes import auth_bp
    from app.api.chat_routes import chat_bp
    from app.api.documents_routes import documents_bp
    from app.api.admin_routes import admin_bp
    from app.api.system_routes import system_bp
    from app.api.frontend_routes import frontend_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(chat_bp, url_prefix='/api/chat')
    app.register_blueprint(documents_bp, url_prefix='/api/documents')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(system_bp, url_prefix='/api/system')

    # Register new v1 API routes for admin dashboard integration
    from app.api.v1.analytics_routes import analytics_v1_bp
    from app.api.v1.metrics_routes import metrics_v1_bp
    from app.api.v1.chat_analytics_routes import chat_analytics_v1_bp
    from app.api.v1.training_routes import training_v1_bp
    from app.api.v1.compliance_routes import compliance_v1_bp
    from app.api.v1.sessions_routes import sessions_v1_bp

    app.register_blueprint(analytics_v1_bp, url_prefix='/api/v1/analytics')
    app.register_blueprint(metrics_v1_bp, url_prefix='/api/v1/metrics')
    app.register_blueprint(chat_analytics_v1_bp, url_prefix='/api/v1')
    app.register_blueprint(training_v1_bp, url_prefix='/api/v1/training')
    app.register_blueprint(compliance_v1_bp, url_prefix='/api/v1/compliance')
    app.register_blueprint(sessions_v1_bp, url_prefix='/api/v1/sessions')

    # Register frontend blueprint LAST to avoid catch-all route conflicts
    app.register_blueprint(frontend_bp, url_prefix='/')
    
    # Health check endpoint
    @app.route("/api/health", methods=["GET"])
    def health_check():
        """Health check endpoint for monitoring."""
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "environment": os.getenv('FLASK_ENV', 'development')
        })
    
    logger.info("Route registration completed")


# Error handlers are now registered in error_handler.py
# This function is deprecated and should not be used


# Configuration classes
class Config:
    """Base configuration class."""
    SECRET_KEY = os.environ.get('FLASK_SECRET_KEY') or 'dev-secret-key'


class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True


class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False


class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    DEBUG = True


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
