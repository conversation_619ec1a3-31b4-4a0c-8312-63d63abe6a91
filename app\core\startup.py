"""
Application startup and initialization module.
Handles environment validation, service initialization, and graceful startup/shutdown.
"""
import os
import sys
import signal
import threading
from typing import Optional
from datetime import datetime

from src.utils.logger import get_logger, log_error
from app.services.service_manager import ServiceManager
from src.monitoring import initialize_apm

logger = get_logger(__name__)

# Global flag to prevent multiple initializations
_startup_completed = False
_startup_lock = threading.Lock()


def validate_environment() -> None:
    """
    Validate required environment variables and configuration.
    Raises ValueError if critical configuration is missing.
    """
    logger.info("Validating environment configuration...")
    
    required_vars = {
        'GROQ_API_KEY': 'GROQ API key for LLM functionality',
        'FLASK_SECRET_KEY': 'Flask secret key for session management'
    }
    
    missing_vars = []
    for var_name, description in required_vars.items():
        if not os.getenv(var_name):
            missing_vars.append(f"{var_name} ({description})")
    
    if missing_vars:
        error_msg = f"Missing required environment variables:\n" + "\n".join(f"  - {var}" for var in missing_vars)
        logger.error(error_msg)
        raise ValueError(error_msg)
    
    logger.info("Environment validation completed successfully")


def initialize_services() -> ServiceManager:
    """
    Initialize all application services using the service manager.
    Returns the configured service manager instance.
    """
    logger.info("Initializing application services...")
    
    try:
        # Get service manager instance (singleton)
        service_manager = ServiceManager.get_instance()
        
        # Initialize core services
        logger.info("Initializing core services...")
        
        # TEMPORARY EMERGENCY FIX: Intent classifier disabled to prevent hanging
        logger.warning("EMERGENCY: Intent classifier initialization disabled to prevent hanging during startup")
        service_manager.get_app_state()["intent_classifier"] = None
        
        # ORIGINAL CODE (commented out until we fix the hanging issue):
        """
        # Initialize intent classifier early (can be slow)
        # Check if intent classifier is disabled via environment variable
        if os.getenv('DISABLE_INTENT_CLASSIFIER', 'false').lower() == 'true':
            logger.info("Intent classifier disabled via environment variable - using fallback only")
            service_manager.get_app_state()["intent_classifier"] = None
        else:
            try:
                intent_classifier = service_manager.get_intent_classifier()
                if intent_classifier:
                    service_manager.get_app_state()["intent_classifier"] = intent_classifier
                    logger.info("Intent classifier initialized and cached")
                else:
                    logger.info("Intent classifier not available - will use fallback classification")
                    service_manager.get_app_state()["intent_classifier"] = None
            except Exception as e:
                logger.warning(f"Intent classifier initialization failed: {e} - using fallback")
                service_manager.get_app_state()["intent_classifier"] = None
        """
        
        # Initialize chain builder (depends on context builder)
        service_manager.get_chain_builder()
        
        # Initialize history manager
        service_manager.get_history_manager()
        
        # Initialize email service if configured
        email_service = service_manager.get_email_service()
        if email_service:
            logger.info("Email service initialized")
        else:
            logger.info("Email service not configured - escalation emails disabled")
        
        # Initialize training pipeline
        try:
            pipeline = service_manager.get_training_pipeline()
            service_manager.get_app_state()["pipeline"] = pipeline
            logger.info("Training pipeline initialized")
        except Exception as e:
            logger.warning(f"Training pipeline initialization failed: {e}")
            service_manager.get_app_state()["pipeline"] = None
        
        logger.info("Service initialization completed successfully")
        return service_manager
        
    except Exception as e:
        log_error(e, "service initialization")
        raise


def initialize_monitoring() -> Optional[object]:
    """
    Initialize application performance monitoring.
    Returns APM instance if successful, None otherwise.
    """
    logger.info("Initializing application monitoring...")
    
    try:
        apm = initialize_apm()
        if apm:
            logger.info("Application monitoring initialized successfully")
        else:
            logger.warning("Application monitoring initialization failed - continuing without monitoring")
        return apm
    except Exception as e:
        log_error(e, "monitoring initialization")
        logger.warning("Application monitoring initialization failed - continuing without monitoring")
        return None


def setup_signal_handlers() -> None:
    """Setup signal handlers for graceful shutdown."""
    logger.info("Setting up signal handlers...")
    
    def handle_shutdown_signal(sig, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received shutdown signal {sig}, initiating graceful shutdown...")
        # Signal the main thread to shutdown
        os._exit(0)
    
    # Register signal handlers
    signal.signal(signal.SIGTERM, handle_shutdown_signal)
    signal.signal(signal.SIGINT, handle_shutdown_signal)
    
    logger.info("Signal handlers configured successfully")


def startup_application() -> tuple[object, ServiceManager]:
    """
    Complete application startup sequence.
    Returns tuple of (apm_instance, service_manager).
    """
    global _startup_completed
    
    with _startup_lock:
        if _startup_completed:
            logger.warning("Startup already completed, returning existing instances")
            # Return existing instances if startup was already done
            service_manager = ServiceManager.get_instance()
            # Note: APM might not be available here, but that's okay for this case
            return None, service_manager
            
        logger.info("Starting application startup sequence...")
        
        try:
            # Setup signal handlers
            setup_signal_handlers()
            
            # Validate environment
            validate_environment()
            
            # Initialize monitoring
            apm = initialize_monitoring()
            
            # Initialize services
            service_manager = initialize_services()
            
            # Health check
            health_status = health_check_services(service_manager)
            if health_status['overall_status'] == 'healthy':
                logger.info("Application startup completed successfully - all services healthy")
            else:
                logger.warning(f"Application startup completed with warnings - {health_status['overall_status']}")
            
            _startup_completed = True
            return apm, service_manager
            
        except Exception as e:
            log_error(e, "application startup")
            logger.error("Application startup failed - shutting down")
            raise


def health_check_services(service_manager: ServiceManager) -> dict:
    """
    Perform health check on all application services.
    Returns health status dictionary.
    """
    logger.info("Performing service health check...")
    
    health_status = {
        'overall_status': 'healthy',
        'services': {},
        'timestamp': None
    }
    
    try:
        # Check core services
        services_to_check = [
            ('intent_classifier', lambda: service_manager.get_intent_classifier()),
            ('chain_builder', lambda: service_manager.get_chain_builder()),
            ('history_manager', lambda: service_manager.get_history_manager()),
            ('vector_store', lambda: service_manager.get_vector_store()),
        ]
        
        for service_name, check_func in services_to_check:
            try:
                service = check_func()
                if service:
                    health_status['services'][service_name] = {
                        'status': 'healthy',
                        'message': 'Service operational'
                    }
                else:
                    health_status['services'][service_name] = {
                        'status': 'degraded',
                        'message': 'Service not available'
                    }
                    health_status['overall_status'] = 'degraded'
            except Exception as e:
                health_status['services'][service_name] = {
                    'status': 'unhealthy',
                    'message': f'Service error: {str(e)}'
                }
                health_status['overall_status'] = 'unhealthy'
        
        # Check database connectivity
        try:
            # Basic database check
            db_status = service_manager.get_app_state().get('database_status', 'unknown')
            health_status['services']['database'] = {
                'status': 'healthy' if db_status == 'connected' else 'degraded',
                'message': f'Database status: {db_status}'
            }
        except Exception as e:
            health_status['services']['database'] = {
                'status': 'unhealthy',
                'message': f'Database error: {str(e)}'
            }
            health_status['overall_status'] = 'unhealthy'
        
        health_status['timestamp'] = datetime.now().isoformat()
        
        logger.info(f"Health check completed - Overall status: {health_status['overall_status']}")
        return health_status
        
    except Exception as e:
        log_error(e, "health check")
        health_status['overall_status'] = 'error'
        health_status['error'] = str(e)
        return health_status


def shutdown_application(service_manager: ServiceManager) -> None:
    """
    Gracefully shutdown the application and cleanup resources.
    """
    logger.info("Initiating application shutdown...")
    
    try:
        # Cleanup services
        if service_manager:
            service_manager.cleanup()
            logger.info("Service cleanup completed")
        
        # Additional cleanup tasks can be added here
        
        logger.info("Application shutdown completed successfully")
        
    except Exception as e:
        log_error(e, "application shutdown")
        logger.error("Error during application shutdown")
    finally:
        logger.info("Application shutdown sequence finished")
