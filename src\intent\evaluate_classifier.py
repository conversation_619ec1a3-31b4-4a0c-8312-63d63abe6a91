"""
Comprehensive evaluation for intent classifier with Phase 2 enhancements.
"""

import os
import sys
import json
import pickle
import warnings
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support, confusion_matrix,
    classification_report, roc_auc_score, roc_curve, precision_recall_curve,
    average_precision_score, cohen_kappa_score, matthews_corrcoef
)
from sklearn.calibration import calibration_curve
import torch
import torch.nn.functional as F
from scipy.stats import entropy
from scipy.spatial.distance import jensenshannon

# Try to import optional dependencies
try:
    from lime.lime_text import LimeTextExplainer
    from shap import Explainer
    IntentExplainer = True
    ExplainabilityManager = True
except ImportError:
    IntentExplainer = None
    ExplainabilityManager = None

from ..utils.logger import get_logger

logger = get_logger(__name__)


class IntentEvaluator:
    """
    Comprehensive evaluator for intent classification models.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics = config.get('metrics', ['accuracy', 'precision', 'recall', 'f1'])
        self.top_k_accuracy = config.get('top_k_accuracy', [1, 3, 5])
        self.confidence_threshold = config.get('confidence_threshold', 0.5)
        
        # Phase 2: Enhanced evaluation settings
        self.advanced_metrics = config.get('advanced_metrics', {})
        self.calibration_config = config.get('calibration', {})
        
    def calculate_metrics(self, true_labels: List[int], predictions: List[int], 
                         probabilities: List[List[float]], class_names: Optional[List[str]] = None) -> Dict[str, float]:
        """
        Calculate comprehensive evaluation metrics.
        
        Args:
            true_labels: True labels
            predictions: Predicted labels
            probabilities: Prediction probabilities
            class_names: Names of classes
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        
        # Basic metrics
        if 'accuracy' in self.metrics:
            metrics['accuracy'] = accuracy_score(true_labels, predictions)
        
        if any(metric in self.metrics for metric in ['precision', 'recall', 'f1']):
            precision, recall, f1, _ = precision_recall_fscore_support(
                true_labels, predictions, average='weighted'
            )
            metrics['precision'] = precision
            metrics['recall'] = recall
            metrics['f1'] = f1
        
        # Per-class metrics
        if class_names:
            per_class_metrics = self._calculate_per_class_metrics(
                true_labels, predictions, probabilities, class_names
            )
            metrics.update(per_class_metrics)
        
        # Top-k accuracy
        if self.top_k_accuracy:
            top_k_metrics = self._calculate_top_k_accuracy(
                true_labels, probabilities, self.top_k_accuracy
            )
            metrics.update(top_k_metrics)
        
        # Confidence analysis
        confidence_metrics = self._analyze_confidence(true_labels, predictions, probabilities)
        metrics.update(confidence_metrics)
        
        # Entropy analysis
        entropy_metrics = self._analyze_entropy(probabilities)
        metrics.update(entropy_metrics)
        
        # Phase 2: Advanced metrics
        if self.advanced_metrics.get('jensen_shannon_divergence', False):
            js_divergence = self._calculate_js_divergence(true_labels, probabilities, class_names)
            metrics['jensen_shannon_divergence'] = js_divergence
        
        if self.advanced_metrics.get('expected_calibration_error', False):
            ece = self._calculate_expected_calibration_error(true_labels, predictions, probabilities)
            metrics['expected_calibration_error'] = ece
        
        if self.advanced_metrics.get('negative_log_likelihood', False):
            nll = self._calculate_negative_log_likelihood(true_labels, probabilities)
            metrics['negative_log_likelihood'] = nll
        
        return metrics
    
    def _calculate_per_class_metrics(self, true_labels: List[int], predictions: List[int],
                                   probabilities: List[List[float]], class_names: List[str]) -> Dict[str, float]:
        """Calculate per-class metrics."""
        per_class_metrics = {}
        
        # Per-class precision, recall, F1
        precision, recall, f1, support = precision_recall_fscore_support(
            true_labels, predictions, average=None
        )
        
        for i, class_name in enumerate(class_names):
            per_class_metrics[f'precision_{class_name}'] = precision[i]
            per_class_metrics[f'recall_{class_name}'] = recall[i]
            per_class_metrics[f'f1_{class_name}'] = f1[i]
            per_class_metrics[f'support_{class_name}'] = support[i]
        
        return per_class_metrics
    
    def _calculate_top_k_accuracy(self, true_labels: List[int], probabilities: List[List[float]], 
                                k_values: List[int]) -> Dict[str, float]:
        """Calculate top-k accuracy."""
        top_k_metrics = {}
        probabilities_array = np.array(probabilities)
        
        for k in k_values:
            if k <= probabilities_array.shape[1]:
                # Get top-k predictions
                top_k_indices = np.argsort(probabilities_array, axis=1)[:, -k:]
                
                # Check if true label is in top-k
                correct = 0
                for i, true_label in enumerate(true_labels):
                    if true_label in top_k_indices[i]:
                        correct += 1
                
                top_k_accuracy = correct / len(true_labels)
                top_k_metrics[f'top_{k}_accuracy'] = top_k_accuracy
        
        return top_k_metrics
    
    def _analyze_confidence(self, true_labels: List[int], predictions: List[int],
                          probabilities: List[List[float]]) -> Dict[str, float]:
        """Analyze prediction confidence."""
        confidence_metrics = {}
        
        # Get confidence scores (max probability)
        confidence_scores = np.max(probabilities, axis=1)
        
        # Overall confidence statistics
        confidence_metrics['mean_confidence'] = np.mean(confidence_scores)
        confidence_metrics['std_confidence'] = np.std(confidence_scores)
        confidence_metrics['min_confidence'] = np.min(confidence_scores)
        confidence_metrics['max_confidence'] = np.max(confidence_scores)
        
        # Confidence by correctness
        correct_mask = np.array(true_labels) == np.array(predictions)
        correct_confidences = confidence_scores[correct_mask]
        incorrect_confidences = confidence_scores[~correct_mask]
        
        if len(correct_confidences) > 0:
            confidence_metrics['mean_confidence_correct'] = np.mean(correct_confidences)
        if len(incorrect_confidences) > 0:
            confidence_metrics['mean_confidence_incorrect'] = np.mean(incorrect_confidences)
        
        # Confidence threshold analysis
        high_confidence_mask = confidence_scores >= self.confidence_threshold
        if np.sum(high_confidence_mask) > 0:
            high_confidence_accuracy = np.mean(correct_mask[high_confidence_mask])
            confidence_metrics['high_confidence_accuracy'] = high_confidence_accuracy
            confidence_metrics['high_confidence_ratio'] = np.mean(high_confidence_mask)
        
        return confidence_metrics
    
    def _analyze_entropy(self, probabilities: List[List[float]]) -> Dict[str, float]:
        """Analyze prediction entropy."""
        entropy_metrics = {}
        
        # Calculate entropy
        probabilities_array = np.array(probabilities)
        entropy = -np.sum(probabilities_array * np.log(probabilities_array + 1e-10), axis=1)
        
        entropy_metrics['mean_entropy'] = np.mean(entropy)
        entropy_metrics['std_entropy'] = np.std(entropy)
        entropy_metrics['min_entropy'] = np.min(entropy)
        entropy_metrics['max_entropy'] = np.max(entropy)
        
        return entropy_metrics
    
    def _calculate_js_divergence(self, true_labels: List[int], probabilities: List[List[float]], 
                                class_names: Optional[List[str]] = None) -> float:
        """Calculate Jensen-Shannon divergence between predicted and true distributions."""
        if not class_names:
            return 0.0
        
        # Convert to numpy arrays
        true_labels = np.array(true_labels)
        probabilities = np.array(probabilities)
        
        # Calculate true distribution
        true_dist = np.zeros(len(class_names))
        for i, class_name in enumerate(class_names):
            true_dist[i] = np.mean(true_labels == i)
        
        # Calculate predicted distribution (mean of predicted probabilities)
        pred_dist = np.mean(probabilities, axis=0)
        
        # Normalize distributions
        true_dist = true_dist / np.sum(true_dist)
        pred_dist = pred_dist / np.sum(pred_dist)
        
        # Calculate Jensen-Shannon divergence
        js_div = jensenshannon(true_dist, pred_dist)
        
        return float(js_div)
    
    def _calculate_expected_calibration_error(self, true_labels: List[int], predictions: List[int], 
                                            probabilities: List[List[float]], num_bins: int = 15) -> float:
        """Calculate Expected Calibration Error (ECE)."""
        true_labels = np.array(true_labels)
        predictions = np.array(predictions)
        probabilities = np.array(probabilities)
        
        # Get confidence scores for predicted classes
        confidences = np.max(probabilities, axis=1)
        
        # Create bins
        bin_boundaries = np.linspace(0, 1, num_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        ece = 0.0
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            # Find samples in this bin
            in_bin = np.logical_and(confidences > bin_lower, confidences <= bin_upper)
            bin_size = np.sum(in_bin)
            
            if bin_size > 0:
                # Calculate accuracy and confidence in this bin
                bin_accuracy = np.sum(np.logical_and(in_bin, predictions == true_labels)) / bin_size
                bin_confidence = np.mean(confidences[in_bin])
                
                # Add to ECE
                ece += bin_size * np.abs(bin_accuracy - bin_confidence)
        
        return float(ece / len(true_labels))
    
    def _calculate_negative_log_likelihood(self, true_labels: List[int], 
                                         probabilities: List[List[float]]) -> float:
        """Calculate Negative Log-Likelihood (NLL)."""
        true_labels = np.array(true_labels)
        probabilities = np.array(probabilities)
        
        # Get probabilities for true labels
        true_probs = probabilities[np.arange(len(true_labels)), true_labels]
        
        # Avoid log(0)
        true_probs = np.clip(true_probs, 1e-15, 1.0)
        
        # Calculate NLL
        nll = -np.mean(np.log(true_probs))
        
        return float(nll)
    
    def generate_confusion_matrix(self, true_labels: List[int], predictions: List[int],
                                class_names: Optional[List[str]] = None, save_path: Optional[str] = None) -> np.ndarray:
        """Generate and optionally save confusion matrix."""
        cm = confusion_matrix(true_labels, predictions)
        
        if save_path:
            self._plot_confusion_matrix(cm, class_names, save_path)
        
        return cm
    
    def _plot_confusion_matrix(self, cm: np.ndarray, class_names: Optional[List[str]], save_path: str):
        """Plot confusion matrix."""
        plt.figure(figsize=(12, 10))
        
        if class_names:
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=class_names, yticklabels=class_names)
        else:
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('True')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    def find_confused_intents(self, true_labels: List[int], predictions: List[int],
                            class_names: Optional[List[str]] = None, top_n: int = 10) -> List[Tuple[str, str, int]]:
        """Find the most confused intent pairs."""
        cm = confusion_matrix(true_labels, predictions)
        
        # Get off-diagonal elements (confusions)
        confused_pairs = []
        for i in range(len(cm)):
            for j in range(len(cm)):
                if i != j and cm[i, j] > 0:
                    true_class = class_names[i] if class_names else f"Class_{i}"
                    pred_class = class_names[j] if class_names else f"Class_{j}"
                    confused_pairs.append((true_class, pred_class, cm[i, j]))
        
        # Sort by confusion count
        confused_pairs.sort(key=lambda x: x[2], reverse=True)
        
        return confused_pairs[:top_n]
    
    def generate_intent_heatmap(self, true_labels: List[int], predictions: List[int], 
                               probabilities: List[List[float]], class_names: List[str], 
                               save_path: Optional[str] = None) -> pd.DataFrame:
        """Generate intent confusion heatmap for detailed analysis."""
        if not class_names:
            return pd.DataFrame()
        
        # Create confusion matrix
        cm = confusion_matrix(true_labels, predictions, labels=range(len(class_names)))
        
        # Convert to DataFrame for better visualization
        heatmap_df = pd.DataFrame(cm, index=class_names, columns=class_names)
        
        # Add row and column sums
        heatmap_df['Total'] = heatmap_df.sum(axis=1)
        heatmap_df.loc['Total'] = heatmap_df.sum(axis=0)
        
        # Save if path provided
        if save_path:
            heatmap_df.to_csv(save_path)
            logger.info(f"Intent heatmap saved to {save_path}")
        
        return heatmap_df
    
    def analyze_confused_pairs(self, true_labels: List[int], predictions: List[int], 
                              probabilities: List[List[float]], class_names: List[str], 
                              texts: Optional[List[str]] = None, top_n: int = 20) -> Dict[str, Any]:
        """Detailed analysis of confused intent pairs."""
        if not class_names:
            return {}
        
        # Find confused pairs
        confused_pairs = self.find_confused_intents(true_labels, predictions, class_names, top_n)
        
        # Analyze confidence patterns for confused pairs
        confidence_analysis = {}
        for true_intent, pred_intent, count in confused_pairs:
            # Find samples with this confusion
            mask = (np.array(true_labels) == class_names.index(true_intent)) & \
                   (np.array(predictions) == class_names.index(pred_intent))
            
            if np.sum(mask) > 0:
                # Get confidence scores for these samples
                sample_probs = np.array(probabilities)[mask]
                true_conf = sample_probs[:, class_names.index(true_intent)]
                pred_conf = sample_probs[:, class_names.index(pred_intent)]
                
                confidence_analysis[f"{true_intent}->{pred_intent}"] = {
                    'count': int(count),
                    'true_confidence_mean': float(np.mean(true_conf)),
                    'true_confidence_std': float(np.std(true_conf)),
                    'pred_confidence_mean': float(np.mean(pred_conf)),
                    'pred_confidence_std': float(np.std(pred_conf)),
                    'confidence_gap': float(np.mean(pred_conf) - np.mean(true_conf))
                }
        
        # Add sample texts if available
        if texts:
            text_samples = {}
            for true_intent, pred_intent, _ in confused_pairs[:5]:  # Top 5 pairs
                mask = (np.array(true_labels) == class_names.index(true_intent)) & \
                       (np.array(predictions) == class_names.index(pred_intent))
                
                if np.sum(mask) > 0:
                    sample_texts = np.array(texts)[mask][:3]  # First 3 samples
                    text_samples[f"{true_intent}->{pred_intent}"] = sample_texts.tolist()
            
            confidence_analysis['sample_texts'] = text_samples
        
        return confidence_analysis
    
    def generate_classification_report(self, true_labels: List[int], predictions: List[int],
                                     class_names: Optional[List[str]] = None) -> str:
        """Generate detailed classification report."""
        return classification_report(true_labels, predictions, target_names=class_names)
    
    def analyze_error_patterns(self, true_labels: List[int], predictions: List[int],
                             texts: List[str], class_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """Analyze error patterns in predictions."""
        error_analysis = {
            'error_examples': [],
            'error_by_class': defaultdict(list),
            'error_patterns': defaultdict(int)
        }
        
        for i, (true_label, pred_label, text) in enumerate(zip(true_labels, predictions, texts)):
            if true_label != pred_label:
                true_class = class_names[true_label] if class_names else f"Class_{true_label}"
                pred_class = class_names[pred_label] if class_names else f"Class_{pred_label}"
                
                error_example = {
                    'text': text,
                    'true_class': true_class,
                    'predicted_class': pred_class,
                    'index': i
                }
                error_analysis['error_examples'].append(error_example)
                error_analysis['error_by_class'][true_class].append(error_example)
                
                # Analyze error patterns
                error_key = f"{true_class} -> {pred_class}"
                error_analysis['error_patterns'][error_key] += 1
        
        return error_analysis


class ConfidenceCalibrator:
    """
    Confidence calibration using temperature scaling.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.temperature = 1.0
        self.is_calibrated = False
    
    def calibrate(self, logits: torch.Tensor, labels: torch.Tensor) -> float:
        """
        Calibrate model confidence using temperature scaling.
        
        Args:
            logits: Model logits
            labels: True labels
            
        Returns:
            Optimal temperature
        """
        # Initialize temperature parameter
        temperature = torch.nn.Parameter(torch.ones(1) * 1.5)
        optimizer = torch.optim.LBFGS([temperature], lr=0.01, max_iter=50)
        
        def eval():
            optimizer.zero_grad()
            loss = F.cross_entropy(logits / temperature, labels)
            loss.backward()
            return loss
        
        optimizer.step(eval)
        
        self.temperature = temperature.item()
        self.is_calibrated = True
        
        logger.info(f"Calibration temperature: {self.temperature:.4f}")
        return self.temperature
    
    def calibrate_probabilities(self, logits: torch.Tensor) -> torch.Tensor:
        """Apply temperature scaling to logits."""
        if not self.is_calibrated:
            logger.warning("Model not calibrated. Using temperature=1.0")
            self.temperature = 1.0
        
        return F.softmax(logits / self.temperature, dim=1)
    
    def evaluate_calibration(self, probabilities: torch.Tensor, labels: torch.Tensor,
                           num_bins: int = 10) -> Dict[str, float]:
        """Evaluate calibration quality."""
        # Calculate calibration curve
        confidences = torch.max(probabilities, dim=1)[0]
        accuracies = (torch.argmax(probabilities, dim=1) == labels).float()
        
        # Bin the data
        bin_boundaries = torch.linspace(0, 1, num_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        calibration_error = 0.0
        reliability_diag = []
        
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            # Find samples in this bin
            in_bin = (confidences > bin_lower) & (confidences <= bin_upper)
            bin_size = in_bin.sum().item()
            
            if bin_size > 0:
                bin_accuracy = accuracies[in_bin].mean().item()
                bin_confidence = confidences[in_bin].mean().item()
                
                calibration_error += (bin_accuracy - bin_confidence) ** 2
                reliability_diag.append((bin_confidence, bin_accuracy))
        
        # Calculate ECE (Expected Calibration Error)
        ece = calibration_error / num_bins
        
        # Calculate reliability diagram
        reliability_diag = np.array(reliability_diag)
        
        return {
            'ece': ece,
            'reliability_diag': reliability_diag
        }


class IntentEvaluatorRunner:
    """
    Runner for comprehensive intent evaluation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.evaluator = IntentEvaluator(config['evaluation'])
        self.calibrator = ConfidenceCalibrator(config['evaluation']['calibration'])
    
    def run_evaluation(self, model, test_loader, class_names: Optional[List[str]] = None,
                      save_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Run comprehensive evaluation.
        
        Args:
            model: Trained model
            test_loader: Test data loader
            class_names: Names of classes
            save_dir: Directory to save results
            
        Returns:
            Dictionary containing all evaluation results
        """
        model.eval()
        
        all_true_labels = []
        all_predictions = []
        all_probabilities = []
        all_logits = []
        all_texts = []
        
        with torch.no_grad():
            for batch in test_loader:
                input_ids = batch['input_ids'].to(model.device)
                attention_mask = batch['attention_mask'].to(model.device)
                labels = batch['labels'].to(model.device)
                
                outputs = model(input_ids, attention_mask)
                logits = outputs['logits']
                probabilities = F.softmax(logits, dim=1)
                predictions = torch.argmax(logits, dim=1)
                
                all_true_labels.extend(labels.cpu().numpy())
                all_predictions.extend(predictions.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
                all_logits.append(logits.cpu())
        
        all_logits = torch.cat(all_logits, dim=0)
        
        # Calculate metrics
        metrics = self.evaluator.calculate_metrics(
            all_true_labels, all_predictions, all_probabilities, class_names
        )
        
        # Generate confusion matrix
        cm = self.evaluator.generate_confusion_matrix(
            all_true_labels, all_predictions, class_names
        )
        
        # Find confused intents
        confused_intents = self.evaluator.find_confused_intents(
            all_true_labels, all_predictions, class_names
        )
        
        # Generate classification report
        classification_rep = self.evaluator.generate_classification_report(
            all_true_labels, all_predictions, class_names
        )
        
        # Phase 2: Enhanced evaluation features
        advanced_results = {}
        if class_names and self.config['evaluation'].get('advanced_metrics', {}):
            # Generate intent heatmap
            if self.config['evaluation']['advanced_metrics'].get('intent_heatmap', False):
                heatmap_path = f"{save_dir}/intent_heatmap.csv" if save_dir else None
                heatmap_df = self.evaluator.generate_intent_heatmap(
                    all_true_labels, all_predictions, all_probabilities, class_names, heatmap_path
                )
                advanced_results['intent_heatmap'] = heatmap_df.to_dict() if not heatmap_df.empty else {}
            
            # Analyze confused pairs
            if self.config['evaluation']['advanced_metrics'].get('confused_pairs_analysis', False):
                confused_analysis = self.evaluator.analyze_confused_pairs(
                    all_true_labels, all_predictions, all_probabilities, class_names, all_texts
                )
                advanced_results['confused_pairs_analysis'] = confused_analysis
        
        # Phase 2: Explainability analysis
        explainability_results = {}
        if self.config.get('explainability', {}).get('enabled', False) and class_names:
            explainability_config = self.config.get('explainability', {})
            intent_explainer = IntentExplainer(explainability_config)
            explainability_manager = ExplainabilityManager(explainability_config)
            
            # Get sample texts for explainability analysis
            sample_texts = []
            for batch in test_loader:
                # Decode input_ids to get texts (simplified)
                batch_texts = [f"Sample text {i}" for i in range(len(batch['input_ids']))]
                sample_texts.extend(batch_texts)
                if len(sample_texts) >= 100:  # Limit to 100 samples for explainability
                    break
            
            # Analyze predictions with explainability
            explainability_analysis = explainability_manager.analyze_predictions(
                model, sample_texts, class_names
            )
            explainability_results = explainability_analysis
        
        # Calibrate confidence if enabled
        calibration_results = {}
        if self.config['evaluation']['calibration']['enabled']:
            # Split data for calibration
            split_idx = int(len(all_true_labels) * 0.8)
            cal_logits = all_logits[:split_idx]
            cal_labels = torch.tensor(all_true_labels[:split_idx])
            eval_logits = all_logits[split_idx:]
            eval_labels = torch.tensor(all_true_labels[split_idx:])
            
            # Calibrate
            temperature = self.calibrator.calibrate(cal_logits, cal_labels)
            calibrated_probs = self.calibrator.calibrate_probabilities(eval_logits)
            
            # Evaluate calibration
            cal_metrics = self.calibrator.evaluate_calibration(calibrated_probs, eval_labels)
            calibration_results = {
                'temperature': temperature,
                'ece': cal_metrics['ece'],
                'reliability_diag': cal_metrics['reliability_diag']
            }
        
        # Compile results
        results = {
            'metrics': metrics,
            'confusion_matrix': cm.tolist(),
            'confused_intents': confused_intents,
            'classification_report': classification_rep,
            'calibration': calibration_results,
            'advanced_results': advanced_results,  # Phase 2: Enhanced evaluation results
            'explainability_results': explainability_results  # Phase 2: Explainability results
        }
        
        # Save results if directory provided
        if save_dir:
            self._save_results(results, save_dir, class_names)
        
        return results
    
    def _save_results(self, results: Dict[str, Any], save_dir: str, class_names: Optional[List[str]]):
        """Save evaluation results."""
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save metrics
        with open(save_path / 'metrics.json', 'w') as f:
            json.dump(results['metrics'], f, indent=2)
        
        # Save confusion matrix plot
        if class_names:
            self.evaluator._plot_confusion_matrix(
                np.array(results['confusion_matrix']), class_names,
                str(save_path / 'confusion_matrix.png')
            )
        
        # Save classification report
        with open(save_path / 'classification_report.txt', 'w') as f:
            f.write(results['classification_report'])
        
        # Save confused intents
        confused_df = pd.DataFrame(
            results['confused_intents'],
            columns=['True_Intent', 'Predicted_Intent', 'Count']
        )
        confused_df.to_csv(save_path / 'confused_intents.csv', index=False)
        
        # Save calibration results
        if results['calibration']:
            with open(save_path / 'calibration.json', 'w') as f:
                json.dump(results['calibration'], f, indent=2)
        
        # Phase 2: Save advanced results
        if results.get('advanced_results'):
            # Save confused pairs analysis
            if 'confused_pairs_analysis' in results['advanced_results']:
                with open(save_path / 'confused_pairs.json', 'w') as f:
                    json.dump(results['advanced_results']['confused_pairs_analysis'], f, indent=2)
            
            # Save intent heatmap (already saved as CSV above)
            if 'intent_heatmap' in results['advanced_results']:
                with open(save_path / 'intent_heatmap_analysis.json', 'w') as f:
                    json.dump(results['advanced_results']['intent_heatmap'], f, indent=2)
        
        # Phase 2: Save explainability results
        if results.get('explainability_results'):
            with open(save_path / 'explainability_analysis.json', 'w') as f:
                json.dump(results['explainability_results'], f, indent=2)
        
        logger.info(f"Evaluation results saved to {save_path}") 