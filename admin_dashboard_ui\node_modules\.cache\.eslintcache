[{"C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\index.tsx": "1", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\App.tsx": "2", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useThemeStore.ts": "3", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\context\\PermissionsProvider.tsx": "4", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\AdminUsersRoles.tsx": "5", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\Layout.tsx": "6", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\login\\index.tsx": "7", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\DeviceIntelligence.tsx": "8", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\compliance\\Sensitive.tsx": "9", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\users\\Admins.tsx": "10", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\compliance\\GDPR.tsx": "11", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\compliance\\Deletion.tsx": "12", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\live\\Ongoing.tsx": "13", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\live\\Queue.tsx": "14", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\training\\NERIntent.tsx": "15", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\training\\Misunderstood.tsx": "16", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\ai\\PolicyDrift.tsx": "17", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\ai\\WeeklyDigest.tsx": "18", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\feedback\\Trends.tsx": "19", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\feedback\\Escalated.tsx": "20", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\ChatLogs.tsx": "21", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\Insights.tsx": "22", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\Overview.tsx": "23", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\UserProfile.tsx": "24", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useSidebarStore.ts": "25", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\Sidebar.tsx": "26", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\usePermissions.ts": "27", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\modals\\EditRoleModal.tsx": "28", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\button.tsx": "29", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\modals\\AddUserModal.tsx": "30", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\tabs.tsx": "31", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\modals\\DeleteUserDialog.tsx": "32", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ProtectedRoute.tsx": "33", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\card.tsx": "34", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\services\\api.ts": "35", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\skeleton.tsx": "36", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\select.tsx": "37", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\toggle.tsx": "38", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\input.tsx": "39", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\slider.tsx": "40", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\LiveIndicator.tsx": "41", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\feedback\\components\\FeedbackTrendsChart.tsx": "42", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ExportButton.tsx": "43", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\GlobalDateFilter.tsx": "44", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopIntentsBarChart.tsx": "45", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopicTrendsLineChart.tsx": "46", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopQuestionsTable.tsx": "47", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopicTrendsChart.tsx": "48", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\SentimentPieChart.tsx": "49", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopIntentsChart.tsx": "50", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\ChatsPerDayChart.tsx": "51", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useAuthStore.ts": "52", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\dropdown-menu.tsx": "53", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\accordion.tsx": "54", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\apiConfig.ts": "55", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\tooltip.tsx": "56", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useAuth.ts": "57", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\lib\\utils.ts": "58", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\date-range-picker.tsx": "59", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\register\\index.tsx": "60", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\providers\\QueryProvider.tsx": "61", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\i18n\\index.ts": "62", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\index.tsx": "63", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\dashboard\\MetricsOverview.tsx": "64", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useQueries.ts": "65", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\LoadingSkeleton.tsx": "66", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\settings\\SystemSettings.tsx": "67", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\MotionDiv.tsx": "68", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\utils\\api.ts": "69", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\feedback\\Resolution.tsx": "70", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\users\\Roles.tsx": "71", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\checkbox.tsx": "72", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\textarea.tsx": "73", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\badge.tsx": "74", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\dialog.tsx": "75", "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\2FASetupModal.tsx": "76"}, {"size": 481, "mtime": 1752486980360, "results": "77", "hashOfConfig": "78"}, {"size": 10389, "mtime": 1754915046275, "results": "79", "hashOfConfig": "78"}, {"size": 10586, "mtime": 1752487047963, "results": "80", "hashOfConfig": "78"}, {"size": 1963, "mtime": 1752564476816, "results": "81", "hashOfConfig": "78"}, {"size": 6198, "mtime": 1752591767811, "results": "82", "hashOfConfig": "78"}, {"size": 2106, "mtime": 1754916750326, "results": "83", "hashOfConfig": "78"}, {"size": 7950, "mtime": 1754915046275, "results": "84", "hashOfConfig": "78"}, {"size": 21685, "mtime": 1752496554614, "results": "85", "hashOfConfig": "78"}, {"size": 6604, "mtime": 1751696683206, "results": "86", "hashOfConfig": "78"}, {"size": 7735, "mtime": 1752486681363, "results": "87", "hashOfConfig": "88"}, {"size": 5920, "mtime": 1751696676513, "results": "89", "hashOfConfig": "78"}, {"size": 6273, "mtime": 1751696669679, "results": "90", "hashOfConfig": "78"}, {"size": 367, "mtime": 1751641403483, "results": "91", "hashOfConfig": "78"}, {"size": 7753, "mtime": 1752576278572, "results": "92", "hashOfConfig": "78"}, {"size": 10903, "mtime": 1751696701914, "results": "93", "hashOfConfig": "78"}, {"size": 5629, "mtime": 1751696511348, "results": "94", "hashOfConfig": "78"}, {"size": 6998, "mtime": 1752598458495, "results": "95", "hashOfConfig": "78"}, {"size": 2904, "mtime": 1754414784592, "results": "96", "hashOfConfig": "78"}, {"size": 1390, "mtime": 1751696399698, "results": "97", "hashOfConfig": "78"}, {"size": 12319, "mtime": 1754916748023, "results": "98", "hashOfConfig": "78"}, {"size": 2520, "mtime": 1751817488984, "results": "99", "hashOfConfig": "78"}, {"size": 11252, "mtime": 1753614801659, "results": "100", "hashOfConfig": "78"}, {"size": 5453, "mtime": 1752496380868, "results": "101", "hashOfConfig": "88"}, {"size": 4155, "mtime": 1754821115925, "results": "102", "hashOfConfig": "78"}, {"size": 417, "mtime": 1751693855723, "results": "103", "hashOfConfig": "78"}, {"size": 7771, "mtime": 1753614801628, "results": "104", "hashOfConfig": "78"}, {"size": 757, "mtime": 1751964613082, "results": "105", "hashOfConfig": "78"}, {"size": 2972, "mtime": 1752589839381, "results": "106", "hashOfConfig": "78"}, {"size": 3195, "mtime": 1754414788495, "results": "107", "hashOfConfig": "78"}, {"size": 3625, "mtime": 1752591422887, "results": "108", "hashOfConfig": "78"}, {"size": 3013, "mtime": 1754414475729, "results": "109", "hashOfConfig": "78"}, {"size": 1493, "mtime": 1752594015097, "results": "110", "hashOfConfig": "78"}, {"size": 517, "mtime": 1752564536297, "results": "111", "hashOfConfig": "78"}, {"size": 3823, "mtime": 1754414776279, "results": "112", "hashOfConfig": "78"}, {"size": 16372, "mtime": 1754972454181, "results": "113", "hashOfConfig": "78"}, {"size": 764, "mtime": 1752669978718, "results": "114", "hashOfConfig": "78"}, {"size": 1995, "mtime": 1754411461223, "results": "115", "hashOfConfig": "78"}, {"size": 676, "mtime": 1751648879239, "results": "116", "hashOfConfig": "78"}, {"size": 216, "mtime": 1751648874190, "results": "117", "hashOfConfig": "78"}, {"size": 607, "mtime": 1751648868755, "results": "118", "hashOfConfig": "78"}, {"size": 683, "mtime": 1751695868119, "results": "119", "hashOfConfig": "78"}, {"size": 1707, "mtime": 1751645825621, "results": "120", "hashOfConfig": "78"}, {"size": 3563, "mtime": 1754915046290, "results": "121", "hashOfConfig": "78"}, {"size": 3405, "mtime": 1751696281070, "results": "122", "hashOfConfig": "88"}, {"size": 3949, "mtime": 1754414481011, "results": "123", "hashOfConfig": "78"}, {"size": 3916, "mtime": 1754414477992, "results": "124", "hashOfConfig": "78"}, {"size": 1534, "mtime": 1752668595487, "results": "125", "hashOfConfig": "78"}, {"size": 1293, "mtime": 1752067285687, "results": "126", "hashOfConfig": "88"}, {"size": 4239, "mtime": 1754414484137, "results": "127", "hashOfConfig": "78"}, {"size": 2470, "mtime": 1752067284647, "results": "128", "hashOfConfig": "88"}, {"size": 3523, "mtime": 1752067283621, "results": "129", "hashOfConfig": "88"}, {"size": 8882, "mtime": 1752565953789, "results": "130", "hashOfConfig": "78"}, {"size": 7407, "mtime": 1754458347326, "results": "131", "hashOfConfig": "78"}, {"size": 1976, "mtime": 1753614801628, "results": "132", "hashOfConfig": "78"}, {"size": 1754, "mtime": 1754930202494, "results": "133", "hashOfConfig": "78"}, {"size": 1170, "mtime": 1751695716401, "results": "134", "hashOfConfig": "78"}, {"size": 489, "mtime": 1752847219814, "results": "135", "hashOfConfig": "78"}, {"size": 171, "mtime": 1751695761918, "results": "136", "hashOfConfig": "78"}, {"size": 1839, "mtime": 1751696259922, "results": "137", "hashOfConfig": "88"}, {"size": 4044, "mtime": 1752498734193, "results": "138", "hashOfConfig": "78"}, {"size": 5856, "mtime": 1752488523432, "results": "139", "hashOfConfig": "78"}, {"size": 8261, "mtime": 1752482635071, "results": "140", "hashOfConfig": "78"}, {"size": 1221, "mtime": 1752574399363, "results": "141", "hashOfConfig": "88"}, {"size": 10715, "mtime": 1753614801632, "results": "142", "hashOfConfig": "88"}, {"size": 11666, "mtime": 1752482477514, "results": "143", "hashOfConfig": "88"}, {"size": 9692, "mtime": 1754414782687, "results": "144", "hashOfConfig": "88"}, {"size": 8988, "mtime": 1754821115940, "results": "145", "hashOfConfig": "78"}, {"size": 538, "mtime": 1754414791235, "results": "146", "hashOfConfig": "88"}, {"size": 390, "mtime": 1754411464745, "results": "147", "hashOfConfig": "78"}, {"size": 11209, "mtime": 1754378714554, "results": "148", "hashOfConfig": "78"}, {"size": 11700, "mtime": 1754411469779, "results": "149", "hashOfConfig": "88"}, {"size": 1083, "mtime": 1754378722389, "results": "150", "hashOfConfig": "88"}, {"size": 795, "mtime": 1754378724923, "results": "151", "hashOfConfig": "78"}, {"size": 1163, "mtime": 1754378726979, "results": "152", "hashOfConfig": "78"}, {"size": 1699, "mtime": 1754414474622, "results": "153", "hashOfConfig": "78"}, {"size": 7225, "mtime": 1754914933000, "results": "154", "hashOfConfig": "78"}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cxl62g", {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1rt34to", {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 32, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\index.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\App.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useThemeStore.ts", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\context\\PermissionsProvider.tsx", ["383", "384"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\AdminUsersRoles.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\Layout.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\login\\index.tsx", ["385", "386"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\DeviceIntelligence.tsx", ["387", "388", "389", "390"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\compliance\\Sensitive.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\users\\Admins.tsx", ["391"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\compliance\\GDPR.tsx", ["392", "393"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\compliance\\Deletion.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\live\\Ongoing.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\live\\Queue.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\training\\NERIntent.tsx", ["394"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\training\\Misunderstood.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\ai\\PolicyDrift.tsx", ["395"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\ai\\WeeklyDigest.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\feedback\\Trends.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\feedback\\Escalated.tsx", ["396", "397", "398"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\ChatLogs.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\Insights.tsx", ["399", "400", "401"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\Overview.tsx", ["402", "403", "404", "405"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\UserProfile.tsx", ["406", "407"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useSidebarStore.ts", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\Sidebar.tsx", ["408", "409", "410", "411", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423", "424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438", "439"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\usePermissions.ts", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\modals\\EditRoleModal.tsx", ["440"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\button.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\modals\\AddUserModal.tsx", ["441"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\modals\\DeleteUserDialog.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\card.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\services\\api.ts", ["442"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\select.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\toggle.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\input.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\slider.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\LiveIndicator.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\feedback\\components\\FeedbackTrendsChart.tsx", ["443", "444"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ExportButton.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\GlobalDateFilter.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopIntentsBarChart.tsx", ["445"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopicTrendsLineChart.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopQuestionsTable.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopicTrendsChart.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\SentimentPieChart.tsx", ["446", "447"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\TopIntentsChart.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\analytics\\components\\ChatsPerDayChart.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useAuthStore.ts", ["448", "449"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\apiConfig.ts", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useAuth.ts", ["450", "451", "452", "453", "454", "455"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\lib\\utils.ts", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\date-range-picker.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\register\\index.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\providers\\QueryProvider.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\i18n\\index.ts", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\index.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\dashboard\\MetricsOverview.tsx", ["456", "457", "458"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\hooks\\useQueries.ts", ["459", "460", "461", "462"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\LoadingSkeleton.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\settings\\SystemSettings.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\MotionDiv.tsx", ["463"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\utils\\api.ts", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\feedback\\Resolution.tsx", ["464", "465", "466"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\pages\\dashboard\\users\\Roles.tsx", ["467", "468", "469", "470", "471", "472", "473", "474", "475"], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\badge.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Generative AI Projects\\Multi-Model RAG Chatbot-react\\admin_dashboard_ui\\src\\components\\2FASetupModal.tsx", [], [], {"ruleId": "476", "severity": 1, "message": "477", "line": 1, "column": 32, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 42}, {"ruleId": "476", "severity": 1, "message": "480", "line": 2, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 2, "endColumn": 15}, {"ruleId": "476", "severity": 1, "message": "481", "line": 1, "column": 27, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 36}, {"ruleId": "476", "severity": 1, "message": "482", "line": 14, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 14, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "483", "line": 10, "column": 18, "nodeType": "478", "messageId": "479", "endLine": 10, "endColumn": 31}, {"ruleId": "476", "severity": 1, "message": "484", "line": 10, "column": 33, "nodeType": "478", "messageId": "479", "endLine": 10, "endColumn": 40}, {"ruleId": "476", "severity": 1, "message": "485", "line": 257, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 257, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "486", "line": 257, "column": 19, "nodeType": "478", "messageId": "479", "endLine": 257, "endColumn": 29}, {"ruleId": "476", "severity": 1, "message": "480", "line": 8, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 15}, {"ruleId": "476", "severity": 1, "message": "487", "line": 4, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 4, "endColumn": 15}, {"ruleId": "476", "severity": 1, "message": "488", "line": 5, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 25}, {"ruleId": "476", "severity": 1, "message": "488", "line": 5, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 25}, {"ruleId": "476", "severity": 1, "message": "489", "line": 7, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 7, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "487", "line": 5, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 15}, {"ruleId": "476", "severity": 1, "message": "490", "line": 9, "column": 60, "nodeType": "478", "messageId": "479", "endLine": 9, "endColumn": 73}, {"ruleId": "476", "severity": 1, "message": "483", "line": 11, "column": 38, "nodeType": "478", "messageId": "479", "endLine": 11, "endColumn": 51}, {"ruleId": "476", "severity": 1, "message": "489", "line": 2, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 2, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "491", "line": 9, "column": 36, "nodeType": "478", "messageId": "479", "endLine": 9, "endColumn": 41}, {"ruleId": "476", "severity": 1, "message": "492", "line": 10, "column": 8, "nodeType": "478", "messageId": "479", "endLine": 10, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "493", "line": 11, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 11, "endColumn": 22}, {"ruleId": "476", "severity": 1, "message": "494", "line": 12, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 12, "endColumn": 23}, {"ruleId": "476", "severity": 1, "message": "495", "line": 13, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 13, "endColumn": 24}, {"ruleId": "476", "severity": 1, "message": "496", "line": 14, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 14, "endColumn": 24}, {"ruleId": "476", "severity": 1, "message": "497", "line": 17, "column": 17, "nodeType": "478", "messageId": "479", "endLine": 17, "endColumn": 24}, {"ruleId": "476", "severity": 1, "message": "498", "line": 25, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 25, "endColumn": 21}, {"ruleId": "476", "severity": 1, "message": "489", "line": 3, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 3, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "499", "line": 6, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 6, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "500", "line": 7, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 7, "endColumn": 14}, {"ruleId": "476", "severity": 1, "message": "501", "line": 7, "column": 16, "nodeType": "478", "messageId": "479", "endLine": 7, "endColumn": 27}, {"ruleId": "476", "severity": 1, "message": "502", "line": 7, "column": 29, "nodeType": "478", "messageId": "479", "endLine": 7, "endColumn": 41}, {"ruleId": "476", "severity": 1, "message": "503", "line": 9, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 9, "endColumn": 12}, {"ruleId": "476", "severity": 1, "message": "504", "line": 10, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 10, "endColumn": 19}, {"ruleId": "476", "severity": 1, "message": "505", "line": 11, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 11, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "506", "line": 12, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 12, "endColumn": 19}, {"ruleId": "476", "severity": 1, "message": "507", "line": 15, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 15, "endColumn": 10}, {"ruleId": "476", "severity": 1, "message": "508", "line": 16, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 16, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "509", "line": 17, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 17, "endColumn": 18}, {"ruleId": "476", "severity": 1, "message": "510", "line": 18, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 18, "endColumn": 17}, {"ruleId": "476", "severity": 1, "message": "511", "line": 32, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 32, "endColumn": 13}, {"ruleId": "476", "severity": 1, "message": "483", "line": 33, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 33, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "512", "line": 34, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 34, "endColumn": 8}, {"ruleId": "476", "severity": 1, "message": "513", "line": 35, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 35, "endColumn": 9}, {"ruleId": "476", "severity": 1, "message": "514", "line": 36, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 36, "endColumn": 11}, {"ruleId": "476", "severity": 1, "message": "515", "line": 37, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 37, "endColumn": 9}, {"ruleId": "476", "severity": 1, "message": "516", "line": 38, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 38, "endColumn": 6}, {"ruleId": "476", "severity": 1, "message": "517", "line": 39, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 39, "endColumn": 12}, {"ruleId": "476", "severity": 1, "message": "518", "line": 40, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 40, "endColumn": 10}, {"ruleId": "476", "severity": 1, "message": "519", "line": 41, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 41, "endColumn": 7}, {"ruleId": "476", "severity": 1, "message": "520", "line": 42, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 42, "endColumn": 6}, {"ruleId": "476", "severity": 1, "message": "521", "line": 43, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 43, "endColumn": 9}, {"ruleId": "476", "severity": 1, "message": "522", "line": 44, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 44, "endColumn": 11}, {"ruleId": "476", "severity": 1, "message": "523", "line": 46, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 46, "endColumn": 14}, {"ruleId": "476", "severity": 1, "message": "524", "line": 47, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 47, "endColumn": 10}, {"ruleId": "476", "severity": 1, "message": "525", "line": 48, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 48, "endColumn": 14}, {"ruleId": "476", "severity": 1, "message": "526", "line": 211, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 211, "endColumn": 20}, {"ruleId": "476", "severity": 1, "message": "527", "line": 212, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 212, "endColumn": 21}, {"ruleId": "476", "severity": 1, "message": "528", "line": 213, "column": 9, "nodeType": "478", "messageId": "479", "endLine": 213, "endColumn": 20}, {"ruleId": "476", "severity": 1, "message": "529", "line": 22, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 22, "endColumn": 18}, {"ruleId": "476", "severity": 1, "message": "530", "line": 3, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 3, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "531", "line": 1, "column": 44, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 70}, {"ruleId": "476", "severity": 1, "message": "532", "line": 2, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 2, "endColumn": 19}, {"ruleId": "476", "severity": 1, "message": "533", "line": 2, "column": 21, "nodeType": "478", "messageId": "479", "endLine": 2, "endColumn": 25}, {"ruleId": "476", "severity": 1, "message": "534", "line": 2, "column": 84, "nodeType": "478", "messageId": "479", "endLine": 2, "endColumn": 90}, {"ruleId": "476", "severity": 1, "message": "534", "line": 2, "column": 61, "nodeType": "478", "messageId": "479", "endLine": 2, "endColumn": 67}, {"ruleId": "476", "severity": 1, "message": "535", "line": 5, "column": 7, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 13}, {"ruleId": "476", "severity": 1, "message": "480", "line": 4, "column": 27, "nodeType": "478", "messageId": "479", "endLine": 4, "endColumn": 32}, {"ruleId": "476", "severity": 1, "message": "536", "line": 291, "column": 26, "nodeType": "478", "messageId": "479", "endLine": 291, "endColumn": 32}, {"ruleId": "476", "severity": 1, "message": "537", "line": 1, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 18}, {"ruleId": "476", "severity": 1, "message": "481", "line": 1, "column": 20, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 29}, {"ruleId": "476", "severity": 1, "message": "538", "line": 1, "column": 31, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 42}, {"ruleId": "476", "severity": 1, "message": "539", "line": 2, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 2, "endColumn": 19}, {"ruleId": "476", "severity": 1, "message": "480", "line": 3, "column": 16, "nodeType": "478", "messageId": "479", "endLine": 3, "endColumn": 21}, {"ruleId": "476", "severity": 1, "message": "540", "line": 6, "column": 11, "nodeType": "478", "messageId": "479", "endLine": 6, "endColumn": 20}, {"ruleId": "476", "severity": 1, "message": "489", "line": 2, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 2, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "541", "line": 17, "column": 24, "nodeType": "478", "messageId": "479", "endLine": 17, "endColumn": 37}, {"ruleId": "476", "severity": 1, "message": "542", "line": 58, "column": 11, "nodeType": "478", "messageId": "479", "endLine": 58, "endColumn": 12}, {"ruleId": "476", "severity": 1, "message": "543", "line": 1, "column": 66, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 84}, {"ruleId": "476", "severity": 1, "message": "544", "line": 5, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 5, "endColumn": 10}, {"ruleId": "476", "severity": 1, "message": "545", "line": 14, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 14, "endColumn": 12}, {"ruleId": "476", "severity": 1, "message": "546", "line": 25, "column": 3, "nodeType": "478", "messageId": "479", "endLine": 25, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "547", "line": 1, "column": 13, "nodeType": "478", "messageId": "479", "endLine": 1, "endColumn": 28}, {"ruleId": "476", "severity": 1, "message": "523", "line": 8, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 21}, {"ruleId": "476", "severity": 1, "message": "483", "line": 8, "column": 30, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 43}, {"ruleId": "476", "severity": 1, "message": "548", "line": 8, "column": 45, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 58}, {"ruleId": "476", "severity": 1, "message": "530", "line": 6, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 6, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "549", "line": 6, "column": 18, "nodeType": "478", "messageId": "479", "endLine": 6, "endColumn": 31}, {"ruleId": "476", "severity": 1, "message": "550", "line": 6, "column": 33, "nodeType": "478", "messageId": "479", "endLine": 6, "endColumn": 43}, {"ruleId": "476", "severity": 1, "message": "551", "line": 6, "column": 45, "nodeType": "478", "messageId": "479", "endLine": 6, "endColumn": 58}, {"ruleId": "476", "severity": 1, "message": "552", "line": 6, "column": 60, "nodeType": "478", "messageId": "479", "endLine": 6, "endColumn": 71}, {"ruleId": "476", "severity": 1, "message": "553", "line": 8, "column": 10, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 16}, {"ruleId": "476", "severity": 1, "message": "554", "line": 8, "column": 18, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 23}, {"ruleId": "476", "severity": 1, "message": "555", "line": 8, "column": 25, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 33}, {"ruleId": "476", "severity": 1, "message": "516", "line": 8, "column": 35, "nodeType": "478", "messageId": "479", "endLine": 8, "endColumn": 38}, "@typescript-eslint/no-unused-vars", "'useContext' is defined but never used.", "Identifier", "unusedVar", "'Roles' is defined but never used.", "'useEffect' is defined but never used.", "'has2FA' is assigned a value but never used.", "'AlertTriangle' is defined but never used.", "'Monitor' is defined but never used.", "'hovered' is assigned a value but never used.", "'setHovered' is assigned a value but never used.", "'Input' is defined but never used.", "'DateRangePicker' is defined but never used.", "'motion' is defined but never used.", "'DialogTrigger' is defined but never used.", "'Minus' is defined but never used.", "'MotionDiv' is defined but never used.", "'TopIntentsChart' is assigned a value but never used.", "'TopicTrendsChart' is assigned a value but never used.", "'Sentiment<PERSON>ie<PERSON>hart' is assigned a value but never used.", "'TopQuestionsTable' is assigned a value but never used.", "'setRole' is assigned a value but never used.", "'getRoleColor' is assigned a value but never used.", "'Button' is defined but never used.", "'Menu' is defined but never used.", "'ChevronLeft' is defined but never used.", "'ChevronRight' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionContent' is defined but never used.", "'AccordionItem' is defined but never used.", "'AccordionTrigger' is defined but never used.", "'Tooltip' is defined but never used.", "'TooltipContent' is defined but never used.", "'TooltipProvider' is defined but never used.", "'TooltipTrigger' is defined but never used.", "'TrendingUp' is defined but never used.", "'Clock' is defined but never used.", "'MapPin' is defined but never used.", "'FileText' is defined but never used.", "'Trash2' is defined but never used.", "'Eye' is defined but never used.", "'UserCheck' is defined but never used.", "'Palette' is defined but never used.", "'Mail' is defined but never used.", "'Zap' is defined but never used.", "'Target' is defined but never used.", "'Activity' is defined but never used.", "'CheckCircle' is defined but never used.", "'XCircle' is defined but never used.", "'AlertCircle' is defined but never used.", "'displayName' is assigned a value but never used.", "'displayEmail' is assigned a value but never used.", "'displayRole' is assigned a value but never used.", "'roleName' is assigned a value but never used.", "'Select' is defined but never used.", "'InternalAxiosRequestConfig' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Legend' is defined but never used.", "'COLORS' is assigned a value but never used.", "'action' is assigned a value but never used.", "'useState' is defined but never used.", "'useCallback' is defined but never used.", "'jwtDecode' is defined but never used.", "'AuthState' is defined but never used.", "'SkeletonChart' is defined but never used.", "'t' is assigned a value but never used.", "'UseMutationOptions' is defined but never used.", "'authApi' is defined but never used.", "'exportApi' is defined but never used.", "'ExportRequest' is defined but never used.", "'HTMLMotionProps' is defined but never used.", "'MessageSquare' is defined but never used.", "'SelectContent' is defined but never used.", "'SelectItem' is defined but never used.", "'SelectTrigger' is defined but never used.", "'SelectValue' is defined but never used.", "'Shield' is defined but never used.", "'Users' is defined but never used.", "'Settings' is defined but never used."]