import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { authApi } from '@/services/api';
import { useAuthStore } from '../../hooks/useAuthStore';

export default function VerifyOtpPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const setToken = useAuthStore((state) => state.setToken);
  const [totpCode, setTotpCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const email = (location.state as any)?.email || '';

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!totpCode.trim()) {
      setError('Please enter the 2FA code');
      return;
    }
    
    setLoading(true);
    setError('');
    try {
      const res = await authApi.verify2FA(email, totpCode);
      if (res.data.success && res.data.token) {
        setToken(res.data.token);
        navigate('/dashboard');
      } else {
        setError(res.data.message || 'Invalid 2FA code');
      }
    } catch (err: any) {
      setError(err?.response?.data?.detail || 'Invalid 2FA code');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <form onSubmit={handleVerify} className="bg-white p-8 rounded shadow-md w-full max-w-md">
        <h2 className="text-2xl font-bold mb-6">Verify 2FA Code</h2>
        <p className="text-gray-600 mb-4">Enter the 6-digit code from your authenticator app</p>
        <input
          type="text"
          placeholder="000000"
          value={totpCode}
          onChange={e => setTotpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
          required
          maxLength={6}
          className="w-full px-4 py-2 border rounded mb-4 text-center text-2xl font-mono tracking-widest"
        />
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition"
          disabled={loading}
        >
          {loading ? 'Verifying...' : 'Verify 2FA Code'}
        </button>
        {error && <div className="text-red-500 mt-2">{error}</div>}
      </form>
    </div>
  );
} 