"""
OCR processor for extracting text from images and PDFs.
Uses Tesseract OCR engine for text extraction.
"""
import os
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

# Try to import optional dependencies
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    cv2 = None

try:
    import pytesseract
    PYTESSERACT_AVAILABLE = True
except ImportError:
    PYTESSERACT_AVAILABLE = False
    pytesseract = None

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    Image = None

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    np = None

try:
    from doctr.io import DocumentFile
    from doctr.models import ocr_predictor
    DOCTR_AVAILABLE = True
except ImportError:
    DOCTR_AVAILABLE = False
    DocumentFile = None
    ocr_predictor = None

from ..utils.logger import get_logger

logger = get_logger(__name__)

class OCRProcessor:
    def __init__(self):
        if DOCTR_AVAILABLE:
            try:
                self.ocr_predictor = ocr_predictor(pretrained=True)
            except Exception as e:
                logger.warning(f"Failed to initialize DocTR OCR: {e}")
                self.ocr_predictor = None
        else:
            self.ocr_predictor = None
            logger.warning("DocTR not available. OCR functionality will be limited.")

    def extract_text_with_layout(self, image_or_path: Union[str, Path, 'Image.Image']) -> Dict[str, Any]:
        if not DOCTR_AVAILABLE or not self.ocr_predictor:
            logger.error("DocTR OCR not available")
            return {
                'blocks': [{'type': 'error', 'text': 'DocTR OCR not available'}],
                'tables': [],
                'raw': None
            }
            
        if isinstance(image_or_path, (str, Path)):
            doc = DocumentFile.from_images(str(image_or_path))
        elif PIL_AVAILABLE and isinstance(image_or_path, Image.Image):
            doc = DocumentFile.from_images(image_or_path)
        else:
            raise ValueError("Unsupported image input type for OCR.")
        result = {
            'blocks': [],
            'tables': [],
            'raw': None
        }
        try:
            doctr_result = self.ocr_predictor(doc)
            exported = doctr_result.export()
            result['raw'] = exported
            for page in exported.get('pages', []):
                for block in page.get('blocks', []):
                    if 'lines' in block:
                        for line in block['lines']:
                            text = ' '.join([w['value'] for w in line.get('words', [])])
                            if text:
                                result['blocks'].append({'type': 'text', 'text': text})
                    if 'table' in block:
                        result['tables'].append(block['table'])
        except Exception as e:
            logger.error(f"DocTR OCR extraction failed: {e}")
            result['blocks'].append({'type': 'error', 'text': str(e)})
        return result

def test_doctr_ocr(sample_image_path: str):
    processor = OCRProcessor()
    result = processor.extract_text_with_layout(sample_image_path)
    print("\n--- Extracted Text Blocks ---")
    for block in result['blocks']:
        print(block.get('text', ''))
    if result['tables']:
        print("\n--- Extracted Tables ---")
        for table in result['tables']:
            print(table)
    print("\n--- Raw Output ---")
    print(result['raw'])

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python ocr_processor.py <image_path>")
    else:
        test_doctr_ocr(sys.argv[1]) 