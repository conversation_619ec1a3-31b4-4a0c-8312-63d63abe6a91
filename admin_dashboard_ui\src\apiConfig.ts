// Admin Dashboard API Configuration
export const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || "http://localhost:5052";

// API Endpoints Configuration
export const API_ENDPOINTS = {
  // Analytics
  ANALYTICS_LIVE: '/api/chat-analytics/live',
  ANALYTICS_CHATS_PER_DAY: '/api/analytics/chats-per-day',
  ANALYTICS_TOP_INTENTS: '/api/analytics/top-intents',

  // Metrics
  METRICS_CHATBOT: '/api/metrics/chatbot',
  METRICS_PERFORMANCE: '/api/metrics/performance',
  METRICS_ENGAGEMENT: '/api/metrics/engagement',
  METRICS_SENTIMENT: '/api/metrics/sentiment',

  // Chat Data
  CHAT_LOGS: '/api/chatlogs',
  CHAT_TRENDS: '/api/chat-trends',

  // Training & AI
  TRAINING_MISUNDERSTOOD: '/api/training/misunderstood-queries',
  TRAINING_NER_INTENT: '/api/training/ner-intent-trainer',
  AI_WEEKLY_DIGEST: '/api/training/weekly-digest',
  AI_POLICY_DRIFT: '/api/training/policy-drift',

  // Escalations & Feedback
  ESCALATIONS_PENDING: '/api/escalations/pending',
  FEEDBACK_TRENDS: '/api/feedback/trends',

  // Compliance
  COMPLIANCE_GDPR: '/api/compliance/gdpr',
  COMPLIANCE_DATA_DELETION: '/api/compliance/data-deletion',
  COMPLIANCE_GDPR_REQUESTS: '/api/compliance/gdpr-requests',

  // Sessions & Device Intelligence
  SESSIONS_LIVE: '/api/sessions/live',
  SESSIONS_HISTORICAL: '/api/sessions/historical',
  SESSIONS_GEO: '/api/sessions/geo-locations',

  // Admin & Users
  ADMIN_USERS: '/admin/users',
  ADMIN_SESSIONS: '/sessions',
  AUDIT_LOGS: '/audit/logs'
};

// Request timeout configuration
export const API_TIMEOUT = 30000; // 30 seconds

// Default request headers
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};