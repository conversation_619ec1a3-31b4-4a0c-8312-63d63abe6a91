"""
Async task manager for handling heavy operations in the RAG chatbot.
Provides concurrent processing for embedding generation, vector search, and other CPU/GPU intensive tasks.
"""

import asyncio
import concurrent.futures
import threading
import time
from typing import Any, Callable, Dict, List, Optional, Union
from functools import wraps
import queue
import os

from .logger import get_logger
from .device_manager import get_optimal_device, is_gpu_available

logger = get_logger(__name__)

class AsyncTaskManager:
    """
    Manages concurrent execution of heavy tasks with proper resource management.
    Supports both CPU and GPU operations with optimal thread/process allocation.
    """
    
    _instance: Optional['AsyncTaskManager'] = None
    _initialized = False
    _lock = threading.Lock()
    
    def __new__(cls) -> 'AsyncTaskManager':
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self._setup_executors()
        self._task_queue = queue.Queue()
        self._active_tasks: Dict[str, concurrent.futures.Future] = {}
        self._task_results: Dict[str, Any] = {}
        
        logger.info("AsyncTaskManager initialized")
    
    @classmethod
    def get_instance(cls) -> 'AsyncTaskManager':
        """Get the singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def _setup_executors(self):
        """Setup thread and process executors based on device capabilities."""
        # Determine optimal thread count
        cpu_count = os.cpu_count() or 4
        
        if is_gpu_available():
            # GPU operations benefit from more threads for data preprocessing
            self._thread_pool_size = min(cpu_count * 2, 16)
            self._process_pool_size = max(1, cpu_count // 2)
        else:
            # CPU-only: use more processes for true parallelism
            self._thread_pool_size = min(cpu_count, 8)
            self._process_pool_size = max(1, cpu_count - 1)
        
        # Create executors
        self._thread_executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self._thread_pool_size,
            thread_name_prefix="RAG-Thread"
        )
        
        self._process_executor = concurrent.futures.ProcessPoolExecutor(
            max_workers=self._process_pool_size
        )
        
        logger.info(f"Executors configured - Threads: {self._thread_pool_size}, Processes: {self._process_pool_size}")
    
    async def run_cpu_task(self, func: Callable, *args, **kwargs) -> Any:
        """
        Run a CPU-intensive task in a thread pool.
        Use for I/O operations, text processing, etc.
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._thread_executor, 
            self._wrap_function(func), 
            *args, **kwargs
        )
    
    async def run_gpu_task(self, func: Callable, *args, **kwargs) -> Any:
        """
        Run a GPU-intensive task in a thread pool.
        Use for model inference, embedding generation, etc.
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._thread_executor, 
            self._wrap_function(func), 
            *args, **kwargs
        )
    
    async def run_parallel_tasks(self, tasks: List[Callable], *args, **kwargs) -> List[Any]:
        """
        Run multiple tasks in parallel.
        Returns results in the same order as input tasks.
        """
        if not tasks:
            return []
        
        # Group tasks by type for optimal execution
        cpu_tasks = []
        gpu_tasks = []
        
        for task in tasks:
            if self._is_gpu_task(task):
                gpu_tasks.append(task)
            else:
                cpu_tasks.append(task)
        
        # Execute in parallel
        results = []
        
        if cpu_tasks:
            cpu_results = await asyncio.gather(*[
                self.run_cpu_task(task, *args, **kwargs) for task in cpu_tasks
            ])
            results.extend(cpu_results)
        
        if gpu_tasks:
            gpu_results = await asyncio.gather(*[
                self.run_gpu_task(task, *args, **kwargs) for task in gpu_tasks
            ])
            results.extend(gpu_results)
        
        return results
    
    def _is_gpu_task(self, func: Callable) -> bool:
        """Determine if a task is GPU-intensive based on function name or attributes."""
        func_name = func.__name__.lower()
        gpu_keywords = ['embedding', 'inference', 'model', 'generate', 'encode']
        return any(keyword in func_name for keyword in gpu_keywords)
    
    def _wrap_function(self, func: Callable) -> Callable:
        """Wrap function with error handling and logging."""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            task_id = f"{func.__name__}_{int(start_time * 1000)}"
            
            try:
                logger.debug(f"Starting task: {task_id}")
                result = func(*args, **kwargs)
                elapsed = time.time() - start_time
                logger.debug(f"Task {task_id} completed in {elapsed:.2f}s")
                return result
                
            except Exception as e:
                elapsed = time.time() - start_time
                logger.error(f"Task {task_id} failed after {elapsed:.2f}s: {e}")
                raise
        
        return wrapper
    
    async def batch_embedding_generation(self, texts: List[str], batch_size: Optional[int] = None) -> List[Any]:
        """
        Generate embeddings for multiple texts in optimized batches.
        Automatically determines optimal batch size based on device capabilities.
        """
        if not texts:
            return []
        
        if batch_size is None:
            # Get optimal batch size from device manager
            from .device_manager import get_optimal_device
            device = get_optimal_device()
            if device.type == 'cuda':
                batch_size = 16  # GPU can handle larger batches
            else:
                batch_size = 4   # CPU: smaller batches
        
        # Split into batches
        batches = [texts[i:i + batch_size] for i in range(0, len(texts), batch_size)]
        
        # Process batches in parallel
        results = []
        for batch in batches:
            batch_result = await self.run_gpu_task(
                self._process_embedding_batch, batch
            )
            results.extend(batch_result)
        
        return results
    
    def _process_embedding_batch(self, texts: List[str]) -> List[Any]:
        """Process a batch of texts for embedding generation."""
        # This would typically call the actual embedding model
        # For now, return placeholder results
        return [f"embedding_{text[:10]}" for text in texts]
    
    def get_executor_stats(self) -> Dict[str, Any]:
        """Get statistics about executor usage."""
        return {
            'thread_pool_size': self._thread_pool_size,
            'process_pool_size': self._process_pool_size,
            'active_tasks': len(self._active_tasks),
            'queued_tasks': self._task_queue.qsize(),
            'device_type': 'GPU' if is_gpu_available() else 'CPU'
        }
    
    def cleanup(self):
        """Clean up resources."""
        logger.info("Cleaning up AsyncTaskManager...")
        
        if hasattr(self, '_thread_executor'):
            self._thread_executor.shutdown(wait=True)
        
        if hasattr(self, '_process_executor'):
            self._process_executor.shutdown(wait=True)
        
        logger.info("AsyncTaskManager cleanup completed")


# Global instance
task_manager = AsyncTaskManager.get_instance()


# Convenience functions
async def run_cpu_task(func: Callable, *args, **kwargs) -> Any:
    """Run a CPU task using the global task manager."""
    return await task_manager.run_cpu_task(func, *args, **kwargs)


async def run_gpu_task(func: Callable, *args, **kwargs) -> Any:
    """Run a GPU task using the global task manager."""
    return await task_manager.run_gpu_task(func, *args, **kwargs)


async def run_parallel_tasks(tasks: List[Callable], *args, **kwargs) -> List[Any]:
    """Run multiple tasks in parallel using the global task manager."""
    return await task_manager.run_parallel_tasks(tasks, *args, **kwargs)


def async_task(func: Callable) -> Callable:
    """Decorator to make a function run asynchronously."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            # Determine if it's a GPU or CPU task
            if task_manager._is_gpu_task(func):
                return await task_manager.run_gpu_task(func, *args, **kwargs)
            else:
                return await task_manager.run_cpu_task(func, *args, **kwargs)
    
    return wrapper
