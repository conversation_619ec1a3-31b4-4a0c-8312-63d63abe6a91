"""
Chat analytics API routes for admin dashboard.
Provides enhanced chat logs and conversation analytics.
"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from typing import Dict, Any, List, Optional

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from src.database.conversation_store import ConversationStore
from src.database.user_db import ConversationModel
from src.database.session_db import SessionModel

logger = get_logger(__name__)

# Create blueprint
chat_analytics_v1_bp = Blueprint('chat_analytics_v1', __name__)

@chat_analytics_v1_bp.route('/test', methods=['GET'])
def test_endpoint():
    """Test endpoint to verify route registration."""
    return jsonify({"status": "success", "message": "Chat analytics routes are working"})

def get_date_range(time_range: str = "7d") -> tuple:
    """Get start and end dates based on time range parameter."""
    end_date = datetime.now()
    
    if time_range == "1d":
        start_date = end_date - timedelta(days=1)
    elif time_range == "7d":
        start_date = end_date - timedelta(days=7)
    elif time_range == "30d":
        start_date = end_date - timedelta(days=30)
    elif time_range == "90d":
        start_date = end_date - timedelta(days=90)
    else:
        start_date = end_date - timedelta(days=7)
    
    return start_date, end_date

@chat_analytics_v1_bp.route('/chat-analytics/live', methods=['GET'])
def get_live_chat_analytics():
    """
    Get live chat analytics data for the dashboard.
    This is the main endpoint that the admin dashboard calls for real-time data.
    """
    try:
        time_range = request.args.get('time_range', '7d')
        start_date, end_date = get_date_range(time_range)
        
        # Initialize data stores
        conversation_store = ConversationStore()
        session_model = SessionModel()
        
        # Get conversation data
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=10000
        )
        
        # Get session data
        sessions = session_model.get_recent_sessions(limit=1000)
        
        # Calculate core metrics
        total_queries = len(conversations)
        unique_devices = len(set(conv.get('device_id', '') for conv in conversations if conv.get('device_id')))
        
        # Calculate active users (users with activity in last 24 hours)
        active_users_24h = 0
        cutoff_time = datetime.now() - timedelta(hours=24)
        for s in sessions:
            if s.get('last_activity'):
                try:
                    last_activity_str = s['last_activity']
                    if isinstance(last_activity_str, str) and 'T' in last_activity_str:
                        # ISO format
                        last_activity = datetime.fromisoformat(last_activity_str.replace('Z', '+00:00'))
                    else:
                        # Unix timestamp
                        last_activity = datetime.fromtimestamp(float(last_activity_str))

                    if last_activity > cutoff_time:
                        active_users_24h += 1
                except (ValueError, TypeError):
                    pass
        
        # Calculate daily chat distribution
        daily_chats = {}
        for conv in conversations:
            if conv.get('query_timestamp'):
                try:
                    # Handle both ISO format and timestamp format
                    timestamp_str = conv['query_timestamp']
                    if isinstance(timestamp_str, str) and 'T' in timestamp_str:
                        # ISO format
                        date_key = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).strftime('%Y-%m-%d')
                    else:
                        # Unix timestamp
                        date_key = datetime.fromtimestamp(float(timestamp_str)).strftime('%Y-%m-%d')
                    daily_chats[date_key] = daily_chats.get(date_key, 0) + 1
                except (ValueError, TypeError):
                    pass
        
        # Prepare chats per day array (last 7 days)
        chats_per_day = []
        for i in range(7):
            date_key = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
            chats_per_day.append(daily_chats.get(date_key, 0))
        chats_per_day.reverse()  # Oldest to newest
        
        # Calculate response times
        response_times = []
        for conv in conversations:
            if conv.get('response_time_seconds'):
                try:
                    duration = float(conv['response_time_seconds'])
                    if duration > 0:
                        response_times.append(duration)
                except (ValueError, TypeError):
                    pass
            elif conv.get('query_timestamp') and conv.get('response_timestamp'):
                try:
                    # Handle both ISO format and timestamp format
                    query_ts = conv['query_timestamp']
                    response_ts = conv['response_timestamp']

                    if isinstance(query_ts, str) and 'T' in query_ts:
                        # ISO format
                        query_time = datetime.fromisoformat(query_ts.replace('Z', '+00:00')).timestamp()
                        response_time = datetime.fromisoformat(response_ts.replace('Z', '+00:00')).timestamp()
                    else:
                        # Unix timestamp
                        query_time = float(query_ts)
                        response_time = float(response_ts)

                    duration = response_time - query_time
                    if duration > 0:
                        response_times.append(duration)
                except (ValueError, TypeError):
                    pass
        
        avg_response_time = sum(response_times) / len(response_times) if response_times else 2.5
        
        # Intent analysis
        intents = {}
        for conv in conversations:
            intent = conv.get('intent', 'unknown')
            intents[intent] = intents.get(intent, 0) + 1
        
        top_intents = [
            {
                "intent": intent,
                "count": count,
                "confidence_avg": 0.85,
                "success_rate": 0.92
            }
            for intent, count in sorted(intents.items(), key=lambda x: x[1], reverse=True)[:5]
        ]
        
        # Mock sentiment analysis
        import random
        sentiment_scores = [random.uniform(0.3, 0.9) for _ in range(min(100, len(conversations)))]
        avg_sentiment = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.5
        
        # Prepare analytics response
        analytics_data = {
            "total_queries": total_queries,
            "active_users": active_users_24h,
            "unique_devices": unique_devices,
            "avg_sentiment": round(avg_sentiment, 2),
            "unique_questions": len(set(conv.get('user_query', '') for conv in conversations)),
            "chats_per_day": chats_per_day,
            "avg_chat_duration": round(avg_response_time, 2),
            "avg_response_time": round(avg_response_time, 2),
            "resolution_rate": 0.94,
            "escalation_rate": 0.06,
            "user_satisfaction": 4.2,
            "top_intents": top_intents,
            "response_times": [
                {
                    "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                    "avg_response_time": round(avg_response_time + (i * 0.1), 2),
                    "p95_response_time": round(avg_response_time * 1.5, 2),
                    "p99_response_time": round(avg_response_time * 2.0, 2)
                }
                for i in range(24)
            ][::-1],
            "error_rate": 0.02,
            "uptime_percentage": 99.8,
            "timestamp": datetime.now().isoformat(),
            "time_range": time_range,
            "status": "success"
        }
        
        logger.info(f"Generated live chat analytics: {total_queries} queries, {active_users_24h} active users")
        return jsonify(analytics_data)
        
    except Exception as e:
        import traceback
        logger.error(f"Error generating live chat analytics: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise APIError(
            message="Failed to retrieve live chat analytics",
            status_code=500,
            details=str(e)
        )

@chat_analytics_v1_bp.route('/chatlogs', methods=['GET'])
def get_enhanced_chat_logs():
    """
    Get enhanced chat logs with filtering and pagination.
    Replaces the basic chatlogs endpoint with more detailed data.
    """
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        device_id = request.args.get('device_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        search_query = request.args.get('search')
        intent_filter = request.args.get('intent')
        
        # Calculate offset
        offset = (page - 1) * page_size
        
        # Get conversation data
        conversation_store = ConversationStore()
        conversations = conversation_store.get_all_conversations(
            device_id=device_id,
            start_date=start_date,
            end_date=end_date,
            limit=page_size,
            offset=offset
        )
        
        # Apply additional filters
        filtered_conversations = []
        for conv in conversations:
            # Search filter
            if search_query:
                query_text = conv.get('user_query', '').lower()
                response_text = conv.get('assistant_response', '').lower()
                if search_query.lower() not in query_text and search_query.lower() not in response_text:
                    continue
            
            # Intent filter
            if intent_filter and conv.get('intent') != intent_filter:
                continue
            
            # Format conversation data
            formatted_conv = {
                "id": conv.get('id', ''),
                "chat_id": conv.get('chat_id', ''),
                "device_id": conv.get('device_id', ''),
                "user_query": conv.get('user_query', ''),
                "assistant_response": conv.get('assistant_response', ''),
                "language": conv.get('language', 'en'),
                "intent": conv.get('intent', 'unknown'),
                "timestamp": conv.get('query_timestamp', ''),
                "response_timestamp": conv.get('response_timestamp', ''),
                "response_time": 0,
                "sentiment": "neutral",  # Mock sentiment
                "confidence": 0.85,      # Mock confidence
                "escalated": False,      # Mock escalation status
                "resolved": True         # Mock resolution status
            }
            
            # Calculate response time
            if conv.get('query_timestamp') and conv.get('response_timestamp'):
                try:
                    query_time = float(conv['query_timestamp'])
                    response_time = float(conv['response_timestamp'])
                    formatted_conv["response_time"] = round(response_time - query_time, 2)
                except (ValueError, TypeError):
                    pass
            
            # Format timestamp for display
            if conv.get('query_timestamp'):
                try:
                    timestamp = datetime.fromtimestamp(float(conv['query_timestamp']))
                    formatted_conv["timestamp"] = timestamp.isoformat()
                    formatted_conv["formatted_timestamp"] = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                except (ValueError, TypeError):
                    pass
            
            filtered_conversations.append(formatted_conv)
        
        # Get total count for pagination (approximate)
        total_conversations = len(conversation_store.get_all_conversations(
            device_id=device_id,
            start_date=start_date,
            end_date=end_date,
            limit=10000
        ))
        
        total_pages = (total_conversations + page_size - 1) // page_size
        
        response_data = {
            "data": filtered_conversations,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_items": total_conversations,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            },
            "filters": {
                "device_id": device_id,
                "start_date": start_date,
                "end_date": end_date,
                "search": search_query,
                "intent": intent_filter
            },
            "summary": {
                "total_conversations": total_conversations,
                "filtered_count": len(filtered_conversations),
                "unique_devices": len(set(conv["device_id"] for conv in filtered_conversations)),
                "avg_response_time": round(sum(conv["response_time"] for conv in filtered_conversations) / len(filtered_conversations), 2) if filtered_conversations else 0
            },
            "generated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Retrieved {len(filtered_conversations)} chat logs (page {page}/{total_pages})")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error retrieving enhanced chat logs: {e}")
        raise APIError(
            message="Failed to retrieve chat logs",
            status_code=500,
            details=str(e)
        )

@chat_analytics_v1_bp.route('/chat-trends', methods=['GET'])
def get_chat_trends():
    """Get chat trend analysis over time."""
    try:
        time_range = request.args.get('time_range', '7d')
        start_date, end_date = get_date_range(time_range)
        
        conversation_store = ConversationStore()
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=10000
        )
        
        # Analyze trends by day
        daily_trends = {}
        for conv in conversations:
            if conv.get('query_timestamp'):
                try:
                    date_key = datetime.fromtimestamp(float(conv['query_timestamp'])).strftime('%Y-%m-%d')
                    if date_key not in daily_trends:
                        daily_trends[date_key] = {
                            "date": date_key,
                            "total_chats": 0,
                            "unique_users": set(),
                            "intents": {},
                            "avg_response_time": []
                        }
                    
                    daily_trends[date_key]["total_chats"] += 1
                    daily_trends[date_key]["unique_users"].add(conv.get('device_id', ''))
                    
                    intent = conv.get('intent', 'unknown')
                    daily_trends[date_key]["intents"][intent] = daily_trends[date_key]["intents"].get(intent, 0) + 1
                    
                    # Mock response time
                    import random
                    daily_trends[date_key]["avg_response_time"].append(random.uniform(1.0, 5.0))
                    
                except (ValueError, TypeError):
                    pass
        
        # Format trends data
        trends_data = []
        for date_key in sorted(daily_trends.keys()):
            data = daily_trends[date_key]
            trends_data.append({
                "date": date_key,
                "total_chats": data["total_chats"],
                "unique_users": len(data["unique_users"]),
                "top_intent": max(data["intents"].items(), key=lambda x: x[1])[0] if data["intents"] else "unknown",
                "avg_response_time": round(sum(data["avg_response_time"]) / len(data["avg_response_time"]), 2) if data["avg_response_time"] else 0,
                "growth_rate": 0  # Will be calculated below
            })
        
        # Calculate growth rates
        for i in range(1, len(trends_data)):
            prev_chats = trends_data[i-1]["total_chats"]
            curr_chats = trends_data[i]["total_chats"]
            if prev_chats > 0:
                growth_rate = ((curr_chats - prev_chats) / prev_chats) * 100
                trends_data[i]["growth_rate"] = round(growth_rate, 1)
        
        response_data = {
            "trends": trends_data,
            "summary": {
                "total_period_chats": sum(t["total_chats"] for t in trends_data),
                "avg_daily_chats": round(sum(t["total_chats"] for t in trends_data) / len(trends_data), 1) if trends_data else 0,
                "peak_day": max(trends_data, key=lambda x: x["total_chats"]) if trends_data else None,
                "overall_growth": trends_data[-1]["growth_rate"] if len(trends_data) > 1 else 0
            },
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting chat trends: {e}")
        raise APIError(
            message="Failed to retrieve chat trends",
            status_code=500,
            details=str(e)
        )

@chat_analytics_v1_bp.route('/escalations/pending', methods=['GET'])
def get_pending_escalations():
    """Get pending escalations that need attention."""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        priority_filter = request.args.get('priority')  # high, medium, low

        # Mock escalation data (in production, this would come from escalation database)
        escalations = []

        for i in range(50):  # Generate 50 mock escalations
            priority = random.choice(['high', 'medium', 'low'])

            if priority_filter and priority != priority_filter:
                continue

            escalations.append({
                "id": f"esc_{random.randint(1000, 9999)}",
                "chat_id": f"chat_{random.randint(1000, 9999)}",
                "user_id": f"user_{random.randint(100, 999)}",
                "subject": random.choice([
                    "Payroll discrepancy inquiry",
                    "Leave request approval issue",
                    "Benefits enrollment problem",
                    "Policy clarification needed",
                    "Technical system issue"
                ]),
                "description": "User requires human assistance with complex query that the chatbot couldn't resolve adequately.",
                "priority": priority,
                "status": "pending",
                "created_at": (datetime.now() - timedelta(hours=random.randint(1, 72))).isoformat(),
                "assigned_to": None,
                "category": random.choice(['payroll', 'benefits', 'leave', 'policy', 'technical']),
                "user_sentiment": random.choice(['frustrated', 'neutral', 'urgent']),
                "original_query": f"Sample user query about {random.choice(['leave', 'payroll', 'benefits'])}...",
                "chatbot_response": "I understand your concern. Let me connect you with a human representative.",
                "escalation_reason": random.choice([
                    "Complex policy interpretation needed",
                    "Sensitive personal information involved",
                    "System limitation encountered",
                    "User explicitly requested human help"
                ]),
                "estimated_resolution_time": f"{random.randint(1, 24)} hours"
            })

        # Sort by priority and creation time
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        escalations.sort(key=lambda x: (priority_order[x['priority']], x['created_at']), reverse=True)

        # Apply pagination
        total_escalations = len(escalations)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_escalations = escalations[start_idx:end_idx]

        response_data = {
            "escalations": paginated_escalations,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total_escalations,
                "total_pages": (total_escalations + page_size - 1) // page_size
            },
            "summary": {
                "total_pending": total_escalations,
                "high_priority": len([e for e in escalations if e['priority'] == 'high']),
                "medium_priority": len([e for e in escalations if e['priority'] == 'medium']),
                "low_priority": len([e for e in escalations if e['priority'] == 'low']),
                "avg_wait_time_hours": random.randint(2, 12),
                "categories": {
                    category: len([e for e in escalations if e['category'] == category])
                    for category in set(e['category'] for e in escalations)
                }
            },
            "filters": {
                "priority": priority_filter
            },
            "generated_at": datetime.now().isoformat()
        }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error getting pending escalations: {e}")
        raise APIError(
            message="Failed to retrieve pending escalations",
            status_code=500,
            details=str(e)
        )

@chat_analytics_v1_bp.route('/feedback/trends', methods=['GET'])
def get_feedback_trends():
    """Get feedback trends and analysis."""
    try:
        time_range = request.args.get('time_range', '30d')
        start_date, end_date = get_date_range(time_range)

        # Mock feedback data analysis
        feedback_trends = []

        # Generate daily feedback data
        for i in range(30):  # Last 30 days
            date = datetime.now() - timedelta(days=i)

            feedback_trends.append({
                "date": date.strftime('%Y-%m-%d'),
                "total_feedback": random.randint(10, 50),
                "positive_feedback": random.randint(20, 35),
                "negative_feedback": random.randint(5, 15),
                "neutral_feedback": random.randint(5, 20),
                "avg_rating": round(random.uniform(3.5, 4.8), 1),
                "response_rate": round(random.uniform(60, 85), 1),
                "top_complaint": random.choice([
                    "Slow response time",
                    "Inaccurate information",
                    "Couldn't understand query",
                    "Limited functionality"
                ]),
                "top_praise": random.choice([
                    "Quick and helpful",
                    "Accurate information",
                    "Easy to use",
                    "Available 24/7"
                ])
            })

        feedback_trends.reverse()  # Oldest to newest

        # Calculate summary statistics
        total_feedback = sum(f['total_feedback'] for f in feedback_trends)
        total_positive = sum(f['positive_feedback'] for f in feedback_trends)
        total_negative = sum(f['negative_feedback'] for f in feedback_trends)
        avg_rating = sum(f['avg_rating'] for f in feedback_trends) / len(feedback_trends)

        # Identify trends
        recent_ratings = [f['avg_rating'] for f in feedback_trends[-7:]]  # Last 7 days
        previous_ratings = [f['avg_rating'] for f in feedback_trends[-14:-7]]  # Previous 7 days

        rating_trend = "improving" if sum(recent_ratings) > sum(previous_ratings) else "declining"

        response_data = {
            "trends": feedback_trends,
            "summary": {
                "total_feedback_responses": total_feedback,
                "overall_satisfaction": round(avg_rating, 1),
                "positive_feedback_percentage": round((total_positive / total_feedback) * 100, 1) if total_feedback > 0 else 0,
                "negative_feedback_percentage": round((total_negative / total_feedback) * 100, 1) if total_feedback > 0 else 0,
                "rating_trend": rating_trend,
                "response_rate": round(random.uniform(70, 85), 1)
            },
            "insights": {
                "most_common_complaints": [
                    {"complaint": "Response accuracy", "count": random.randint(20, 50)},
                    {"complaint": "Response speed", "count": random.randint(15, 40)},
                    {"complaint": "Understanding context", "count": random.randint(10, 30)}
                ],
                "most_common_praise": [
                    {"praise": "Helpful responses", "count": random.randint(30, 60)},
                    {"praise": "Quick availability", "count": random.randint(25, 55)},
                    {"praise": "Easy to use", "count": random.randint(20, 45)}
                ],
                "improvement_areas": [
                    "Enhance response accuracy for complex queries",
                    "Improve context understanding",
                    "Reduce response time for technical questions"
                ]
            },
            "recommendations": [
                "Focus on training for low-rated interaction types",
                "Implement proactive feedback collection",
                "Address common complaint patterns",
                "Celebrate and replicate highly-rated interactions"
            ],
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error getting feedback trends: {e}")
        raise APIError(
            message="Failed to retrieve feedback trends",
            status_code=500,
            details=str(e)
        )
