import re
import os
from typing import List, Dict, Any, Optional, Tuple
from .base import Chun<PERSON><PERSON>trate<PERSON>, Chun<PERSON><PERSON>onfig, TokenizerManager
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Optional imports with fallbacks
try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    SEMANTIC_AVAILABLE = True
except ImportError:
    SEMANTIC_AVAILABLE = False
    logger.warning("sentence-transformers/sklearn not available - semantic chunking disabled")

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    logger.warning("spacy not available - advanced NLP features disabled")

SPACY_MODEL_PATH = os.path.abspath("data/models/spacy/en_core_web_sm/en_core_web_sm-3.8.0")

class SemanticStrategy(ChunkingStrategy):
    """Semantic chunking strategy using sentence embeddings and similarity."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.model = None
        if SEMANTIC_AVAILABLE:
            try:
                self.model = SentenceTransformer('data/models_cache/bge-base-en-v1.5')
                self.logger.info("Semantic model loaded successfully")
            except Exception as e:
                self.logger.warning(f"Failed to load semantic model: {e}")
                self.model = None

    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        if not text.strip():
            return []
        if not self.model:
            from .token_aware import TokenAwareStrategy
            return TokenAwareStrategy().chunk_text(text, config, tokenizer_manager)
        sentences = self._split_into_sentences(text)
        if len(sentences) == 0:
            return []
        # Prefix each sentence as passage for e5
        sentences_prefixed = [f"passage: {s}" for s in sentences]
        embeddings = self.model.encode(sentences_prefixed)
        # Group sentences by semantic similarity (as before)
        groups = []
        current_group = [sentences[0]]
        for i in range(1, len(sentences)):
            chunk_embedding = np.mean([embeddings[j] for j in range(len(current_group))], axis=0)
            similarity = cosine_similarity([chunk_embedding], [embeddings[i]])[0][0]
            test_group = current_group + [sentences[i]]
            test_text = " ".join(test_group)
            token_count = tokenizer_manager.count_tokens(test_text, config.tokenizer_name)
            if similarity >= config.semantic_threshold and token_count <= config.max_tokens:
                current_group.append(sentences[i])
            else:
                groups.append(current_group)
                current_group = [sentences[i]]
        if current_group:
            groups.append(current_group)
        # Now batch groups into chunks with token-aware batching
        chunks = []
        for group in groups:
            batch = []
            for sent in group:
                test_batch = batch + [sent]
                test_text = " ".join(test_batch)
                token_count = tokenizer_manager.count_tokens(test_text, config.tokenizer_name)
                if token_count <= config.max_tokens:
                    batch.append(sent)
                else:
                    if batch:
                        chunks.append(" ".join(batch))
                    # Truncate overly long sentence if needed
                    if tokenizer_manager.count_tokens(sent, config.tokenizer_name) > config.max_tokens:
                        # Truncate to max_tokens
                        tokens = sent.split()
                        truncated = " ".join(tokens[:config.max_tokens])
                        chunks.append(truncated)
                        batch = []
                    else:
                        batch = [sent]
            if batch:
                chunks.append(" ".join(batch))
        return [c for c in chunks if c.strip()]

    def _split_into_sentences(self, text: str) -> List[str]:
        if not text.strip():
            return []
        try:
            import spacy
            nlp = spacy.load(SPACY_MODEL_PATH)
            doc = nlp(text)
            return [sent.text.strip() for sent in doc.sents if sent.text.strip()]
        except Exception as e:
            import re
            # Fallback: split on period, exclamation, question, or newline
            return [s.strip() for s in re.split(r'[.!?\n]+', text) if s.strip()]

    def get_name(self) -> str:
        return "semantic" 