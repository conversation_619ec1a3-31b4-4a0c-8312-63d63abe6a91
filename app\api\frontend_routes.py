"""
Frontend routes for the HR Assistant Chatbot.
Handles frontend-specific endpoints and static file serving.
"""
import os
from flask import Blueprint, send_from_directory, current_app

from src.utils.logger import get_logger

logger = get_logger(__name__)

# Create blueprint
frontend_bp = Blueprint('frontend', __name__)


@frontend_bp.route('/assets/<path:filename>')
def serve_assets(filename):
    """
    Serve static assets from the React build directory.
    Handles CSS, JS, images, and other static files.
    """
    try:
        assets_path = os.path.join(current_app.static_folder, 'assets')
        return send_from_directory(assets_path, filename)
    except Exception as e:
        logger.error(f"Failed to serve asset {filename}: {e}")
        return "Asset not found", 404


@frontend_bp.route('/favicon.png')
def favicon():
    """
    Serve the favicon from the React build directory.
    """
    try:
        return send_from_directory(current_app.static_folder, 'favicon.png')
    except Exception as e:
        logger.error(f"Failed to serve favicon: {e}")
        return "Favicon not found", 404


@frontend_bp.route('/favicon.ico')
def favicon_ico():
    """
    Serve the favicon.ico from the React build directory.
    """
    try:
        return send_from_directory(current_app.static_folder, 'favicon.ico')
    except Exception as e:
        logger.error(f"Failed to serve favicon.ico: {e}")
        return "Favicon not found", 404


@frontend_bp.route('/manifest.json')
def manifest():
    """
    Serve the web app manifest from the React build directory.
    """
    try:
        return send_from_directory(current_app.static_folder, 'manifest.json')
    except Exception as e:
        logger.error(f"Failed to serve manifest: {e}")
        return "Manifest not found", 404


@frontend_bp.route('/robots.txt')
def robots():
    """
    Serve the robots.txt file from the React build directory.
    """
    try:
        return send_from_directory(current_app.static_folder, 'robots.txt')
    except Exception as e:
        logger.error(f"Failed to serve robots.txt: {e}")
        return "Robots.txt not found", 404


@frontend_bp.route('/static/<path:filename>')
def serve_static(filename):
    """
    Serve static files from the React build static directory.
    Handles additional static assets that may be in a static subdirectory.
    """
    try:
        static_path = os.path.join(current_app.static_folder, 'static')
        return send_from_directory(static_path, filename)
    except Exception as e:
        logger.error(f"Failed to serve static file {filename}: {e}")
        return "Static file not found", 404


# Catch-all route for React SPA - MUST BE LAST
@frontend_bp.route('/', defaults={'path': ''})
@frontend_bp.route('/<path:path>')
def serve_react(path):
    """
    Catch-all route for React Single Page Application.
    Serves index.html for all routes not handled by API or static file routes.
    This enables client-side routing in React.
    """
    try:
        # Check if the requested path is a static file
        if path != '' and os.path.exists(os.path.join(current_app.static_folder, path)):
            return send_from_directory(current_app.static_folder, path)
        else:
            # Serve index.html for all other routes (React SPA)
            return send_from_directory(current_app.static_folder, 'index.html')
    except Exception as e:
        logger.error(f"Failed to serve React app for path {path}: {e}")
        return "Application not found", 404


@frontend_bp.route('/health-ui')
def health_ui():
    """
    Simple health check endpoint for the frontend.
    Returns basic HTML page to verify frontend serving is working.
    """
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>HR Assistant - Health Check</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .status { color: green; font-weight: bold; }
            .info { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1>HR Assistant Chatbot</h1>
        <p class="status">✅ Frontend serving is working</p>
        <div class="info">
            <h3>Available Endpoints:</h3>
            <ul>
                <li><a href="/api/health">/api/health</a> - API health check</li>
                <li><a href="/api/health/detailed">/api/health/detailed</a> - Detailed health check</li>
                <li><a href="/">/</a> - Main application</li>
            </ul>
        </div>
        <p><em>This is a basic health check page. The main application is a React SPA.</em></p>
    </body>
    </html>
    """


# Error handlers specific to frontend routes
@frontend_bp.errorhandler(404)
def frontend_not_found(error):
    """
    Handle 404 errors for frontend routes.
    For SPA routes, serve index.html to let React handle the routing.
    """
    try:
        # For API routes, return JSON error
        if hasattr(error, 'original_exception') and 'api' in str(error.original_exception):
            return {"error": "Not Found", "message": "API endpoint not found"}, 404
        
        # For frontend routes, serve React app
        return send_from_directory(current_app.static_folder, 'index.html')
    except Exception as e:
        logger.error(f"Frontend 404 handler failed: {e}")
        return "Application not found", 404


@frontend_bp.errorhandler(500)
def frontend_server_error(error):
    """
    Handle 500 errors for frontend routes.
    Returns a simple error page for frontend issues.
    """
    logger.error(f"Frontend server error: {error}")
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>HR Assistant - Error</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; text-align: center; }
            .error { color: red; font-weight: bold; }
            .message { background: #ffe6e6; padding: 20px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1>HR Assistant Chatbot</h1>
        <p class="error">❌ An error occurred</p>
        <div class="message">
            <p>We're sorry, but something went wrong while loading the application.</p>
            <p>Please try refreshing the page or contact support if the problem persists.</p>
        </div>
        <p><a href="/">← Back to Home</a></p>
    </body>
    </html>
    """, 500
