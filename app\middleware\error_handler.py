"""
Centralized error handling middleware for Flask application.
Provides structured error responses and logging.
"""
import traceback
from typing import <PERSON><PERSON>, Dict, Any
from flask import Flask, jsonify, request, g
from werkzeug.exceptions import HTTPException
from datetime import datetime

from src.utils.logger import get_logger, log_error

logger = get_logger(__name__)


def register_error_handlers(app: Flask) -> None:
    """
    Register centralized error handlers for the Flask application.
    
    Args:
        app: Flask application instance
    """
    logger.info("Registering error handlers...")
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 Bad Request errors."""
        log_error(error, "bad request", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type="Bad Request",
            message="The request was invalid or malformed",
            status_code=400,
            details=str(error.description) if hasattr(error, 'description') else None
        )
    
    @app.errorhandler(401)
    def unauthorized(error):
        """Handle 401 Unauthorized errors."""
        log_error(error, "unauthorized", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type="Unauthorized",
            message="Authentication required or invalid credentials",
            status_code=401
        )
    
    @app.errorhandler(403)
    def forbidden(error):
        """Handle 403 Forbidden errors."""
        log_error(error, "forbidden", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type="Forbidden",
            message="Access denied - insufficient permissions",
            status_code=403
        )
    
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 Not Found errors."""
        log_error(error, "not found", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type="Not Found",
            message="The requested resource was not found",
            status_code=404
        )
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        """Handle 405 Method Not Allowed errors."""
        log_error(error, "method not allowed", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type="Method Not Allowed",
            message="The HTTP method is not allowed for this endpoint",
            status_code=405
        )
    
    @app.errorhandler(429)
    def rate_limit_exceeded(error):
        """Handle 429 Too Many Requests errors."""
        log_error(error, "rate limit exceeded", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type="Rate Limit Exceeded",
            message="Too many requests - please try again later",
            status_code=429
        )
    
    @app.errorhandler(500)
    def internal_server_error(error):
        """Handle 500 Internal Server Error."""
        log_error(error, "internal server error", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type="Internal Server Error",
            message="An unexpected error occurred on the server",
            status_code=500
        )
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        """Handle all other HTTP exceptions."""
        log_error(error, f"HTTP exception {error.code}", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type=error.name,
            message=error.description,
            status_code=error.code
        )
    
    @app.errorhandler(Exception)
    def handle_unexpected_error(error):
        """Handle all unexpected exceptions."""
        log_error(error, "unexpected error", req_id=getattr(g, 'request_id', None))
        return create_error_response(
            error_type="Unexpected Error",
            message="An unexpected error occurred",
            status_code=500
        )
    
    logger.info("Error handlers registered successfully")


def create_error_response(
    error_type: str,
    message: str,
    status_code: int,
    details: str = None,
    **kwargs
) -> Tuple[Dict[str, Any], int]:
    """
    Create a standardized error response.
    
    Args:
        error_type: Type of error
        message: Human-readable error message
        status_code: HTTP status code
        details: Additional error details
        **kwargs: Additional response fields
        
    Returns:
        Tuple of (response_dict, status_code)
    """
    response = {
        "error": {
            "type": error_type,
            "message": message,
            "status_code": status_code,
            "timestamp": datetime.now().isoformat()
        }
    }
    
    if details:
        response["error"]["details"] = details
    
    # Add additional fields
    response.update(kwargs)
    
    # Log the error response
    log_error_response(error_type, message, status_code, details)
    
    return response, status_code


def log_error_response(error_type: str, message: str, status_code: int, details: str = None) -> None:
    """
    Log error response details.
    
    Args:
        error_type: Type of error
        message: Error message
        status_code: HTTP status code
        details: Additional error details
    """
    # Get context information
    user_id = None
    req_id = None
    
    try:
        if hasattr(g, 'user') and g.user:
            user_id = getattr(g.user, 'id', None)
        req_id = getattr(g, 'request_id', None)
    except:
        pass
    
    # Log with context
    logger.error(
        f"Error response: {error_type} - {message} - Status: {status_code}",
        extra={
            'user_id': user_id,
            'req_id': req_id,
            'error_type': error_type,
            'status_code': status_code,
            'details': details
        }
    )


class APIError(Exception):
    """
    Custom API error class for structured error handling.
    """
    
    def __init__(self, message: str, status_code: int = 500, error_type: str = None, details: str = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.error_type = error_type or self._get_error_type_from_status(status_code)
        self.details = details
    
    def _get_error_type_from_status(self, status_code: int) -> str:
        """Get error type based on status code."""
        error_types = {
            400: "Bad Request",
            401: "Unauthorized",
            403: "Forbidden",
            404: "Not Found",
            405: "Method Not Allowed",
            429: "Rate Limit Exceeded",
            500: "Internal Server Error"
        }
        return error_types.get(status_code, "API Error")
    
    def to_response(self) -> Tuple[Dict[str, Any], int]:
        """Convert error to Flask response tuple."""
        return create_error_response(
            error_type=self.error_type,
            message=self.message,
            status_code=self.status_code,
            details=self.details
        )
