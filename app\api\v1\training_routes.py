"""
Training and AI insights API routes for admin dashboard.
Provides data for training tools and AI analysis.
"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from typing import Dict, Any, List, Optional
import random

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from src.database.conversation_store import ConversationStore
from src.database.user_db import ConversationModel

logger = get_logger(__name__)

# Create blueprint
training_v1_bp = Blueprint('training_v1', __name__)

def get_date_range(time_range: str = "7d") -> tuple:
    """Get start and end dates based on time range parameter."""
    end_date = datetime.now()
    
    if time_range == "1d":
        start_date = end_date - timedelta(days=1)
    elif time_range == "7d":
        start_date = end_date - timedelta(days=7)
    elif time_range == "30d":
        start_date = end_date - timedelta(days=30)
    elif time_range == "90d":
        start_date = end_date - timedelta(days=90)
    else:
        start_date = end_date - timedelta(days=7)
    
    return start_date, end_date

@training_v1_bp.route('/misunderstood-queries', methods=['GET'])
def get_misunderstood_queries():
    """
    Get queries that were potentially misunderstood by the chatbot.
    These are queries with low confidence scores or poor user feedback.
    """
    try:
        # Get query parameters
        time_range = request.args.get('range', '7d')
        confidence_threshold = float(request.args.get('confidence', 0.7))
        search_query = request.args.get('query', '')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 20))
        
        start_date, end_date = get_date_range(time_range)
        
        # Get conversation data
        conversation_store = ConversationStore()
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=5000  # Get more data for analysis
        )
        
        # Analyze conversations for potential misunderstandings
        misunderstood_queries = []
        
        for conv in conversations:
            user_query = conv.get('user_query', '')
            assistant_response = conv.get('assistant_response', '')
            
            # Skip if search filter doesn't match
            if search_query and search_query.lower() not in user_query.lower():
                continue
            
            # Mock confidence calculation (in production, this would come from actual ML models)
            # Simulate lower confidence for certain patterns
            mock_confidence = random.uniform(0.3, 0.95)
            
            # Factors that might indicate misunderstanding:
            # 1. Very short responses
            # 2. Generic responses
            # 3. Responses that don't match query intent
            
            confidence_penalty = 0
            if len(assistant_response) < 50:
                confidence_penalty += 0.2
            if "I don't understand" in assistant_response or "I'm not sure" in assistant_response:
                confidence_penalty += 0.3
            if len(user_query.split()) > 20:  # Very long queries might be confusing
                confidence_penalty += 0.1
            
            final_confidence = max(0.1, mock_confidence - confidence_penalty)
            
            # Only include queries below confidence threshold
            if final_confidence <= confidence_threshold:
                # Mock intent classification
                possible_intents = ['leave_inquiry', 'payroll_question', 'policy_question', 'general_inquiry', 'technical_support']
                predicted_intent = random.choice(possible_intents)
                
                misunderstood_queries.append({
                    "id": f"query_{conv.get('id', random.randint(1000, 9999))}",
                    "query": user_query,
                    "response": assistant_response,
                    "confidence": round(final_confidence, 3),
                    "predicted_intent": predicted_intent,
                    "actual_intent": conv.get('intent', 'unknown'),
                    "timestamp": conv.get('query_timestamp', ''),
                    "device_id": conv.get('device_id', ''),
                    "language": conv.get('language', 'en'),
                    "suggested_improvements": [
                        "Add more training data for this intent",
                        "Improve entity extraction",
                        "Review response templates"
                    ],
                    "flagged_for_review": False,
                    "training_priority": "medium" if final_confidence > 0.4 else "high"
                })
        
        # Sort by confidence (lowest first)
        misunderstood_queries.sort(key=lambda x: x['confidence'])
        
        # Apply pagination
        total_queries = len(misunderstood_queries)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_queries = misunderstood_queries[start_idx:end_idx]
        
        # Format timestamps
        for query in paginated_queries:
            if query['timestamp']:
                try:
                    timestamp = datetime.fromtimestamp(float(query['timestamp']))
                    query['formatted_timestamp'] = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    query['timestamp'] = timestamp.isoformat()
                except (ValueError, TypeError):
                    query['formatted_timestamp'] = 'Unknown'
        
        response_data = {
            "queries": paginated_queries,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total_queries,
                "total_pages": (total_queries + page_size - 1) // page_size
            },
            "filters": {
                "time_range": time_range,
                "confidence_threshold": confidence_threshold,
                "search_query": search_query
            },
            "summary": {
                "total_misunderstood": total_queries,
                "avg_confidence": round(sum(q['confidence'] for q in misunderstood_queries) / len(misunderstood_queries), 3) if misunderstood_queries else 0,
                "high_priority_count": len([q for q in misunderstood_queries if q['training_priority'] == 'high']),
                "most_common_intent": max(set(q['predicted_intent'] for q in misunderstood_queries), 
                                        key=lambda x: sum(1 for q in misunderstood_queries if q['predicted_intent'] == x)) if misunderstood_queries else None
            },
            "generated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Retrieved {len(paginated_queries)} misunderstood queries (page {page}, confidence <= {confidence_threshold})")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting misunderstood queries: {e}")
        raise APIError(
            message="Failed to retrieve misunderstood queries",
            status_code=500,
            details=str(e)
        )

@training_v1_bp.route('/ner-intent-trainer', methods=['GET'])
def get_ner_intent_training_data():
    """
    Get data for NER (Named Entity Recognition) and Intent training.
    Provides suggestions for improving model accuracy.
    """
    try:
        time_range = request.args.get('range', '30d')
        start_date, end_date = get_date_range(time_range)
        
        conversation_store = ConversationStore()
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=5000
        )
        
        # Analyze conversations for NER and Intent training opportunities
        intent_analysis = {}
        entity_patterns = {}
        training_suggestions = []
        
        for conv in conversations:
            user_query = conv.get('user_query', '')
            intent = conv.get('intent', 'unknown')
            
            # Intent analysis
            if intent not in intent_analysis:
                intent_analysis[intent] = {
                    "count": 0,
                    "examples": [],
                    "avg_confidence": 0,
                    "needs_improvement": False
                }
            
            intent_analysis[intent]["count"] += 1
            if len(intent_analysis[intent]["examples"]) < 5:
                intent_analysis[intent]["examples"].append(user_query)
            
            # Mock confidence analysis
            mock_confidence = random.uniform(0.6, 0.95)
            intent_analysis[intent]["avg_confidence"] = (
                intent_analysis[intent]["avg_confidence"] + mock_confidence
            ) / 2
            
            # Entity pattern analysis (mock)
            # In production, this would use actual NER models
            potential_entities = []
            words = user_query.lower().split()
            
            # Look for common entity patterns
            for i, word in enumerate(words):
                if word in ['january', 'february', 'march', 'april', 'may', 'june', 
                           'july', 'august', 'september', 'october', 'november', 'december']:
                    potential_entities.append({"type": "DATE", "value": word, "position": i})
                elif word.isdigit():
                    potential_entities.append({"type": "NUMBER", "value": word, "position": i})
                elif '@' in word:
                    potential_entities.append({"type": "EMAIL", "value": word, "position": i})
            
            if potential_entities:
                entity_key = f"{intent}_entities"
                if entity_key not in entity_patterns:
                    entity_patterns[entity_key] = []
                entity_patterns[entity_key].extend(potential_entities)
        
        # Generate training suggestions
        for intent, data in intent_analysis.items():
            if data["count"] < 10:
                training_suggestions.append({
                    "type": "intent_training",
                    "priority": "high",
                    "message": f"Intent '{intent}' has only {data['count']} examples. Consider adding more training data.",
                    "intent": intent,
                    "current_examples": data["count"],
                    "recommended_examples": 50
                })
            
            if data["avg_confidence"] < 0.8:
                training_suggestions.append({
                    "type": "confidence_improvement",
                    "priority": "medium",
                    "message": f"Intent '{intent}' has low average confidence ({data['avg_confidence']:.2f}). Review training data quality.",
                    "intent": intent,
                    "current_confidence": round(data["avg_confidence"], 2),
                    "target_confidence": 0.85
                })
        
        # Prepare entity training data
        entity_training_data = []
        for entity_key, entities in entity_patterns.items():
            intent = entity_key.replace('_entities', '')
            entity_types = {}
            for entity in entities:
                entity_type = entity["type"]
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            entity_training_data.append({
                "intent": intent,
                "entity_types": entity_types,
                "total_entities": len(entities),
                "suggestions": [
                    f"Add more {entity_type} examples" for entity_type, count in entity_types.items() if count < 5
                ]
            })
        
        response_data = {
            "intent_analysis": [
                {
                    "intent": intent,
                    "count": data["count"],
                    "examples": data["examples"],
                    "avg_confidence": round(data["avg_confidence"], 3),
                    "needs_improvement": data["avg_confidence"] < 0.8 or data["count"] < 10,
                    "training_priority": "high" if data["avg_confidence"] < 0.7 else "medium" if data["avg_confidence"] < 0.85 else "low"
                }
                for intent, data in sorted(intent_analysis.items(), key=lambda x: x[1]["count"], reverse=True)
            ],
            "entity_training": entity_training_data,
            "training_suggestions": sorted(training_suggestions, key=lambda x: {"high": 3, "medium": 2, "low": 1}[x["priority"]], reverse=True),
            "summary": {
                "total_intents": len(intent_analysis),
                "intents_needing_improvement": len([i for i in intent_analysis.values() if i["avg_confidence"] < 0.8 or i["count"] < 10]),
                "total_training_examples": sum(data["count"] for data in intent_analysis.values()),
                "avg_intent_confidence": round(sum(data["avg_confidence"] for data in intent_analysis.values()) / len(intent_analysis), 3) if intent_analysis else 0,
                "entity_types_found": len(set(entity["type"] for entities in entity_patterns.values() for entity in entities))
            },
            "recommendations": [
                "Focus on intents with less than 10 training examples",
                "Improve entity extraction for DATE and NUMBER types",
                "Add more diverse training examples for low-confidence intents",
                "Consider active learning for edge cases"
            ],
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Generated NER/Intent training data: {len(intent_analysis)} intents, {len(training_suggestions)} suggestions")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting NER/Intent training data: {e}")
        raise APIError(
            message="Failed to retrieve training data",
            status_code=500,
            details=str(e)
        )

@training_v1_bp.route('/weekly-digest', methods=['GET'])
def get_ai_weekly_digest():
    """
    Get AI insights weekly digest with key metrics and recommendations.
    """
    try:
        week_offset = int(request.args.get('week_offset', 0))  # 0 = current week, 1 = last week, etc.

        # Calculate week date range
        end_date = datetime.now() - timedelta(weeks=week_offset)
        start_date = end_date - timedelta(days=7)

        conversation_store = ConversationStore()
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=5000
        )

        # Analyze conversations for insights
        total_queries = len(conversations)
        unique_users = len(set(conv.get('device_id', '') for conv in conversations if conv.get('device_id')))

        # Mock AI insights analysis
        top_queries = []
        intent_distribution = {}

        for conv in conversations:
            query = conv.get('user_query', '')
            intent = conv.get('intent', 'unknown')

            # Track top queries (simplified)
            if len(query) > 10 and query not in [q['query'] for q in top_queries]:
                top_queries.append({
                    'query': query[:100] + '...' if len(query) > 100 else query,
                    'count': random.randint(1, 20),
                    'intent': intent
                })

            # Track intent distribution
            intent_distribution[intent] = intent_distribution.get(intent, 0) + 1

        # Sort and limit top queries
        top_queries.sort(key=lambda x: x['count'], reverse=True)
        top_queries = top_queries[:10]

        # Generate insights
        weekly_digest = {
            "week_period": {
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "week_number": end_date.isocalendar()[1]
            },
            "key_metrics": {
                "total_queries": total_queries,
                "unique_users": unique_users,
                "avg_queries_per_user": round(total_queries / unique_users, 1) if unique_users > 0 else 0,
                "query_growth": random.uniform(-10, 25),  # Mock growth percentage
                "user_engagement_score": random.uniform(7.5, 9.5)
            },
            "top_queries": [q['query'] for q in top_queries[:5]],
            "intent_insights": {
                "most_common_intent": max(intent_distribution.items(), key=lambda x: x[1])[0] if intent_distribution else "unknown",
                "intent_diversity": len(intent_distribution),
                "emerging_intents": random.sample(list(intent_distribution.keys()), min(3, len(intent_distribution)))
            },
            "sentiment_shifts": f"Positive sentiment increased by {random.randint(2, 8)}% this week",
            "escalation_summary": f"{random.randint(0, 5)} escalations this week, {random.randint(80, 95)}% resolved",
            "training_suggestions": [
                "Add more training data for leave policy questions",
                "Improve entity extraction for date ranges",
                "Review responses for payroll inquiries",
                "Update knowledge base with recent policy changes"
            ],
            "performance_highlights": [
                f"Response time improved by {random.randint(5, 15)}%",
                f"User satisfaction score: {random.uniform(4.2, 4.8):.1f}/5.0",
                f"Intent classification accuracy: {random.uniform(88, 95):.1f}%"
            ],
            "recommendations": [
                "Focus training on underperforming intents",
                "Implement proactive user engagement strategies",
                "Review and update response templates",
                "Consider expanding knowledge base coverage"
            ],
            "generated_at": datetime.now().isoformat()
        }

        logger.info(f"Generated AI weekly digest for week {weekly_digest['week_period']['week_number']}")
        return jsonify(weekly_digest)

    except Exception as e:
        logger.error(f"Error generating AI weekly digest: {e}")
        raise APIError(
            message="Failed to generate AI weekly digest",
            status_code=500,
            details=str(e)
        )

@training_v1_bp.route('/policy-drift', methods=['GET'])
def get_policy_drift_analysis():
    """
    Analyze policy drift - changes in user queries and system responses over time.
    """
    try:
        time_range = request.args.get('time_range', '30d')
        start_date, end_date = get_date_range(time_range)

        conversation_store = ConversationStore()
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=5000
        )

        # Analyze policy drift patterns
        policy_areas = {
            'leave_policy': {'queries': [], 'responses': [], 'changes': []},
            'payroll_policy': {'queries': [], 'responses': [], 'changes': []},
            'benefits_policy': {'queries': [], 'responses': [], 'changes': []},
            'remote_work_policy': {'queries': [], 'responses': [], 'changes': []},
            'general_hr_policy': {'queries': [], 'responses': [], 'changes': []}
        }

        # Categorize conversations by policy area (simplified classification)
        for conv in conversations:
            query = conv.get('user_query', '').lower()
            response = conv.get('assistant_response', '').lower()

            if any(word in query for word in ['leave', 'vacation', 'sick', 'pto']):
                policy_areas['leave_policy']['queries'].append(query)
                policy_areas['leave_policy']['responses'].append(response)
            elif any(word in query for word in ['salary', 'payroll', 'pay', 'wage']):
                policy_areas['payroll_policy']['queries'].append(query)
                policy_areas['payroll_policy']['responses'].append(response)
            elif any(word in query for word in ['benefits', 'insurance', 'health', 'dental']):
                policy_areas['benefits_policy']['queries'].append(query)
                policy_areas['benefits_policy']['responses'].append(response)
            elif any(word in query for word in ['remote', 'work from home', 'wfh', 'hybrid']):
                policy_areas['remote_work_policy']['queries'].append(query)
                policy_areas['remote_work_policy']['responses'].append(response)
            else:
                policy_areas['general_hr_policy']['queries'].append(query)
                policy_areas['general_hr_policy']['responses'].append(response)

        # Generate drift analysis
        drift_analysis = []

        for policy_area, data in policy_areas.items():
            if len(data['queries']) > 0:
                # Mock drift detection
                drift_score = random.uniform(0.1, 0.8)
                confidence = random.uniform(0.7, 0.95)

                drift_analysis.append({
                    "policy_area": policy_area.replace('_', ' ').title(),
                    "query_count": len(data['queries']),
                    "drift_score": round(drift_score, 2),
                    "confidence": round(confidence, 2),
                    "drift_type": random.choice(['terminology_change', 'policy_update', 'user_behavior_shift', 'response_inconsistency']),
                    "severity": "high" if drift_score > 0.6 else "medium" if drift_score > 0.3 else "low",
                    "detected_changes": [
                        f"New terminology appearing in {random.randint(5, 20)}% of queries",
                        f"Response patterns shifted by {random.randint(10, 30)}%",
                        f"User intent classification changed for {random.randint(3, 15)} queries"
                    ][:random.randint(1, 3)],
                    "recommendations": [
                        "Review recent policy updates",
                        "Update training data with new terminology",
                        "Validate response accuracy",
                        "Consider retraining models"
                    ][:random.randint(2, 4)],
                    "trend_direction": random.choice(['increasing', 'decreasing', 'stable']),
                    "last_updated": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat()
                })

        # Sort by drift score (highest first)
        drift_analysis.sort(key=lambda x: x['drift_score'], reverse=True)

        # Calculate overall drift metrics
        total_drift_score = sum(item['drift_score'] for item in drift_analysis) / len(drift_analysis) if drift_analysis else 0
        high_drift_areas = len([item for item in drift_analysis if item['severity'] == 'high'])

        response_data = {
            "policy_drift_analysis": drift_analysis,
            "summary": {
                "overall_drift_score": round(total_drift_score, 2),
                "total_policy_areas": len(drift_analysis),
                "high_drift_areas": high_drift_areas,
                "medium_drift_areas": len([item for item in drift_analysis if item['severity'] == 'medium']),
                "low_drift_areas": len([item for item in drift_analysis if item['severity'] == 'low']),
                "requires_attention": high_drift_areas > 0
            },
            "alerts": [
                {
                    "type": "policy_drift",
                    "severity": "high",
                    "message": f"High policy drift detected in {item['policy_area']}",
                    "policy_area": item['policy_area'],
                    "drift_score": item['drift_score']
                }
                for item in drift_analysis if item['severity'] == 'high'
            ],
            "recommendations": [
                "Conduct policy review for high-drift areas",
                "Update knowledge base with recent changes",
                "Retrain models with new data",
                "Implement continuous monitoring"
            ],
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }

        logger.info(f"Generated policy drift analysis: {len(drift_analysis)} areas analyzed, {high_drift_areas} high-drift areas")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error generating policy drift analysis: {e}")
        raise APIError(
            message="Failed to generate policy drift analysis",
            status_code=500,
            details=str(e)
        )
