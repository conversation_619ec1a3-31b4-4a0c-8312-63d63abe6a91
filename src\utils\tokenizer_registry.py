"""
Tokenizer registry for centralized tokenizer management.
Prevents redundant tokenizer loading and provides singleton access to all tokenizers.
"""

import os
import logging
from typing import Optional, Dict, Any, Callable
from pathlib import Path
from transformers import AutoTokenizer
from ..config import EMBEDDING_TOKENIZER_DIR, UNIFIED_MODEL_NAME, DEV_LOGGING

logger = logging.getLogger(__name__)


class TokenizerRegistry:
    """
    Centralized tokenizer registry with lazy loading and singleton pattern.
    Manages all tokenizers used across the application.
    """
    
    _instance: Optional['TokenizerRegistry'] = None
    _tokenizers: Dict[str, Any] = {}
    _tokenizer_functions: Dict[str, Callable[[str], int]] = {}
    _initialized = False
    
    def __new__(cls) -> 'TokenizerRegistry':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
        self._initialized = True
        logger.info("TokenizerRegistry singleton initialized")
    
    @classmethod
    def get_instance(cls) -> 'TokenizerRegistry':
        """Get the singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def get_bge_tokenizer(self) -> AutoTokenizer:
        """
        Get or create the BGE tokenizer singleton.
        This is the main tokenizer used for embeddings and context building.
        """
        if 'bge_tokenizer' not in self._tokenizers:
            try:
                if DEV_LOGGING['verbose_startup']:
                    logger.info("Loading BGE tokenizer...")
                
                # Try local cache first
                if EMBEDDING_TOKENIZER_DIR.exists():
                    tokenizer = AutoTokenizer.from_pretrained(str(EMBEDDING_TOKENIZER_DIR))
                    if DEV_LOGGING['verbose_startup']:
                        logger.info(f"Loaded BGE tokenizer from local cache: {EMBEDDING_TOKENIZER_DIR}")
                else:
                    # Fallback to unified model path
                    tokenizer = AutoTokenizer.from_pretrained(UNIFIED_MODEL_NAME)
                    if DEV_LOGGING['verbose_startup']:
                        logger.info(f"Loaded BGE tokenizer from fallback path: {UNIFIED_MODEL_NAME}")
                
                self._tokenizers['bge_tokenizer'] = tokenizer
                
                # Create token counting function
                self._tokenizer_functions['bge_tokenizer'] = lambda text: len(tokenizer.encode(text))
                
                if DEV_LOGGING['verbose_startup']:
                    logger.info("BGE tokenizer loaded successfully")
                    
            except Exception as e:
                logger.error(f"Failed to load BGE tokenizer: {e}")
                # Create fallback tokenizer function
                self._tokenizer_functions['bge_tokenizer'] = lambda text: len(text.split())
                logger.warning("Using fallback word-based tokenizer")
        
        return self._tokenizers['bge_tokenizer']
    
    def get_bge_tokenizer_function(self) -> Callable[[str], int]:
        """
        Get the BGE tokenizer function for token counting.
        Returns a function that takes text and returns token count.
        """
        if 'bge_tokenizer' not in self._tokenizer_functions:
            # This will trigger tokenizer loading if not already loaded
            self.get_bge_tokenizer()
        
        return self._tokenizer_functions['bge_tokenizer']
    
    def get_intent_tokenizer(self) -> AutoTokenizer:
        """
        Get or create the intent classifier tokenizer.
        This is typically the same as BGE tokenizer but kept separate for flexibility.
        """
        if 'intent_tokenizer' not in self._tokenizers:
            try:
                if DEV_LOGGING['verbose_startup']:
                    logger.info("Loading intent classifier tokenizer...")
                
                # Use the same tokenizer as BGE for consistency
                tokenizer = self.get_bge_tokenizer()
                self._tokenizers['intent_tokenizer'] = tokenizer
                
                if DEV_LOGGING['verbose_startup']:
                    logger.info("Intent classifier tokenizer loaded successfully")
                    
            except Exception as e:
                logger.error(f"Failed to load intent tokenizer: {e}")
                # Use BGE tokenizer as fallback
                self._tokenizers['intent_tokenizer'] = self.get_bge_tokenizer()
        
        return self._tokenizers['intent_tokenizer']
    
    def get_custom_tokenizer(self, name: str, model_path: str) -> AutoTokenizer:
        """
        Get or create a custom tokenizer for a specific model.
        
        Args:
            name: Unique name for the tokenizer
            model_path: Path to the model/tokenizer
        """
        if name not in self._tokenizers:
            try:
                if DEV_LOGGING['verbose_startup']:
                    logger.info(f"Loading custom tokenizer '{name}' from {model_path}")
                
                tokenizer = AutoTokenizer.from_pretrained(model_path)
                self._tokenizers[name] = tokenizer
                
                # Create token counting function
                self._tokenizer_functions[name] = lambda text: len(tokenizer.encode(text))
                
                if DEV_LOGGING['verbose_startup']:
                    logger.info(f"Custom tokenizer '{name}' loaded successfully")
                    
            except Exception as e:
                logger.error(f"Failed to load custom tokenizer '{name}': {e}")
                # Create fallback tokenizer function
                self._tokenizer_functions[name] = lambda text: len(text.split())
                logger.warning(f"Using fallback word-based tokenizer for '{name}'")
        
        return self._tokenizers[name]
    
    def get_tokenizer_function(self, name: str) -> Callable[[str], int]:
        """
        Get a tokenizer function by name.
        
        Args:
            name: Name of the tokenizer (e.g., 'bge_tokenizer', 'intent_tokenizer')
        """
        if name not in self._tokenizer_functions:
            # Try to load the tokenizer if it exists
            if name in self._tokenizers:
                tokenizer = self._tokenizers[name]
                self._tokenizer_functions[name] = lambda text: len(tokenizer.encode(text))
            else:
                # Create fallback function
                self._tokenizer_functions[name] = lambda text: len(text.split())
                logger.warning(f"Tokenizer '{name}' not found, using fallback")
        
        return self._tokenizer_functions[name]
    
    def get_all_tokenizers(self) -> Dict[str, Any]:
        """Get all loaded tokenizers."""
        return self._tokenizers.copy()
    
    def get_all_tokenizer_functions(self) -> Dict[str, Callable[[str], int]]:
        """Get all tokenizer functions."""
        return self._tokenizer_functions.copy()
    
    def clear_tokenizers(self) -> None:
        """Clear all tokenizers (useful for testing or memory management)."""
        self._tokenizers.clear()
        self._tokenizer_functions.clear()
        logger.info("All tokenizers cleared from registry")
    
    def get_tokenizer_info(self) -> Dict[str, Any]:
        """Get information about all tokenizers in the registry."""
        info = {
            'total_tokenizers': len(self._tokenizers),
            'total_functions': len(self._tokenizer_functions),
            'loaded_tokenizers': list(self._tokenizers.keys()),
            'available_functions': list(self._tokenizer_functions.keys())
        }
        
        # Add specific tokenizer details
        for name, tokenizer in self._tokenizers.items():
            try:
                info[f'{name}_vocab_size'] = tokenizer.vocab_size if hasattr(tokenizer, 'vocab_size') else 'Unknown'
                info[f'{name}_model_max_length'] = tokenizer.model_max_length if hasattr(tokenizer, 'model_max_length') else 'Unknown'
            except Exception as e:
                info[f'{name}_error'] = str(e)
        
        return info


# Global tokenizer registry instance
tokenizer_registry = TokenizerRegistry.get_instance()


def get_bge_tokenizer() -> AutoTokenizer:
    """Get the BGE tokenizer singleton."""
    return tokenizer_registry.get_bge_tokenizer()


def get_bge_tokenizer_function() -> Callable[[str], int]:
    """Get the BGE tokenizer function for token counting."""
    return tokenizer_registry.get_bge_tokenizer_function()


def get_intent_tokenizer() -> AutoTokenizer:
    """Get the intent classifier tokenizer."""
    return tokenizer_registry.get_intent_tokenizer()


def get_tokenizer_function(name: str) -> Callable[[str], int]:
    """Get a tokenizer function by name."""
    return tokenizer_registry.get_tokenizer_function(name)
