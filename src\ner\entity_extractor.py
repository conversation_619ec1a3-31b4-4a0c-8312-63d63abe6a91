import spacy
from spacy.tokens import DocBin
from spacy.training import Example
import spacy.training
import logging
import json
import time
import re
import os
import numpy as np
from contextlib import contextmanager
import threading
from functools import lru_cache
import warnings
import random
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
from collections import defaultdict
import gc

# Kaggle-compatible paths - no relative imports
DATA_DIR = Path("/kaggle/working/data")
MODEL_DIR = Path("/kaggle/working/models")

# Create a simple logger for Kaggle
class MockLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")

def get_logger():
    return MockLogger()

# Create directories in Kaggle working directory
DATA_DIR.mkdir(parents=True, exist_ok=True)
MODEL_DIR.mkdir(parents=True, exist_ok=True)

logger = get_logger()

# --- Configuration and Constants ---
TRAINING_DATA_PATH = Path("entity_training_data.jsonl")  # Current directory
MODEL_DIR = Path("data/models/entity_extractor")  # Local model directory
CALIBRATION_PATH = DATA_DIR / "calibration" / "entity_calibration_stats.json"

# Ensure directories exist
for path in [MODEL_DIR, DATA_DIR / "config", DATA_DIR / "training", DATA_DIR / "calibration"]:
    path.mkdir(parents=True, exist_ok=True)

# --- Custom Exceptions ---
class EntityExtractionError(Exception):
    """Base exception for entity extraction errors."""
    pass

class ModelLoadError(EntityExtractionError):
    """Raised when the spaCy model fails to load."""
    pass

class InvalidInputError(EntityExtractionError):
    """Raised when input text is invalid or exceeds limits."""
    pass

class ModelUpdateError(EntityExtractionError):
    """Raised when model update/training fails."""
    pass

class PatternManagementError(EntityExtractionError):
    """Raised for errors during pattern export/load."""
    pass

class EntityExtractor:
    """
    Production-grade Named Entity Recognition (NER) and Span Categorization for HR queries.
    
    Features:
    - Thread-safe operations
    - Proper error handling and logging
    - Performance monitoring
    - Batch processing optimization
    - Confidence calibration
    - Pattern management
    - Input validation and sanitization
    """
    
    def __init__(self, model_path: Path = MODEL_DIR, enable_gpu: bool = True, max_workers: int = 4):
        """
        Initialize the EntityExtractor with production-grade configurations.
        
        Args:
            model_path (Path): Path to the trained spaCy model
            enable_gpu (bool): Whether to use GPU acceleration if available
            max_workers (int): Maximum number of worker threads for batch processing
        """
        self.model_path = model_path
        self.enable_gpu = enable_gpu
        self.max_workers = max_workers
        self.nlp: Optional[spacy.Language] = None
        self.patterns: Dict[str, List[str]] = {}
        self.calibration_stats: Dict[str, Any] = {}
        self._lock = threading.RLock()
        self._model_loaded = False
        
        # Don't load model on initialization - use lazy loading
        logger.info("EntityExtractor initialized with lazy loading - model will be loaded on first use")

    def load_model(self):
        """Load the spaCy model with proper error handling."""
        if self._model_loaded and self.nlp is not None:
            return  # Already loaded
        
        with self._lock:
            if self._model_loaded and self.nlp is not None:
                return  # Double-check after acquiring lock
            
            try:
                if self.model_path.exists():
                    logger.info(f"Model found at {self.model_path}. Loading...")
                    self.nlp = spacy.load(self.model_path)
                    logger.info(f"Model loaded successfully from {self.model_path}")
                else:
                    logger.info(f"No trained model found at {self.model_path}. Creating blank model...")
                    self._create_blank_model()

            except Exception as e:
                logger.error(f"Error loading model: {e}")
                logger.info("Creating blank model as fallback...")
                self._create_blank_model()
            
            self._model_loaded = True

    def _create_blank_model(self):
        """Create a pre-trained spaCy model with enhanced NER component."""
        try:
            # Use pre-trained model for better performance
            try:
                self.nlp = spacy.load("en_core_web_md")
                logger.info("Loaded pre-trained spaCy medium model")
            except OSError:
                # Fallback to blank model if pre-trained not available
                self.nlp = spacy.blank("en")
                logger.info("Pre-trained model not found, using blank model")
            
            # Enable GPU if available
            if self.enable_gpu and spacy.prefer_gpu():
                logger.info("✅ GPU acceleration enabled")
            else:
                logger.info("⚠️ Using CPU (GPU not available)")
            
            # Add NER component if not present
            if "ner" not in self.nlp.pipe_names:
                ner = self.nlp.add_pipe("ner")
            else:
                ner = self.nlp.get_pipe("ner")
                
            # Add comprehensive HR entity labels
            hr_labels = [
                "EMPLOYEE_ID", "DEPARTMENT", "POSITION", "MANAGER", "SALARY",
                "BONUS", "BENEFIT_TYPE", "LEAVE_TYPE", "DOCUMENT_TYPE",
                "ISSUE_TYPE", "LOCATION", "DATE", "TIME", "AMOUNT",
                "PERCENTAGE", "CURRENCY", "PHONE", "EMAIL", "URL",
                "month", "year", "salary_component", "benefit_type", "issue_type",
                # Additional HR-specific labels
                "POLICY_NAME", "COMPANY_NAME", "JOB_TITLE", "SKILL",
                "CERTIFICATION", "EDUCATION", "EXPERIENCE_YEARS", "SALARY_RANGE",
                "BENEFIT_AMOUNT", "LEAVE_DAYS", "WORK_SCHEDULE", "SHIFT_TYPE"
            ]
            
            for label in hr_labels:
                if label not in ner.labels:
                    ner.add_label(label)
            
            logger.info(f"Enhanced spaCy model with {len(ner.labels)} HR labels")
            
            # Save the enhanced model
            self.model_path.mkdir(parents=True, exist_ok=True)
            self.nlp.to_disk(self.model_path)
            logger.info(f"Enhanced model saved to {self.model_path}")

        except Exception as e:
            logger.error(f"Error creating enhanced model: {e}")
            raise ModelLoadError(f"Failed to create enhanced model: {e}")

    def _optimize_model(self):
        """Optimize model for production use."""
        if self.nlp:
            # Disable unnecessary components for faster inference
            self.nlp.select_pipes(enable=["ner"])

    @contextmanager
    def _performance_monitor(self):
        """Context manager for performance monitoring."""
        start_time = time.time()
        try:
            yield
        finally:
            elapsed = time.time() - start_time
            logger.debug(f"Operation completed in {elapsed:.4f}s")

    def extract_entities(self, texts: Union[str, List[str]], 
                         max_length: int = 1000,
                         batch_size: int = 32) -> List[Dict[str, Any]]:
        """
        Extract entities from text(s) with confidence scores.

        Args:
            texts: Single text string or list of text strings
            max_length: Maximum text length for processing
            batch_size: Number of texts to process in each batch

        Returns:
            List of dictionaries containing extraction results
        """
        with self._lock:
            try:
                # Validate and normalize input
                normalized_texts = self._validate_and_normalize_input(texts, max_length)
                
                if not normalized_texts:
                    return []
            
                # Process in batches for efficiency
                results = []
                for i in range(0, len(normalized_texts), batch_size):
                    batch = normalized_texts[i:i + batch_size]
                    batch_results = self._process_batch(batch)
                    results.extend(batch_results)
            
                return results
                
            except Exception as e:
                logger.error(f"Error in entity extraction: {e}")
                raise EntityExtractionError(f"Extraction failed: {e}")

    def _validate_and_normalize_input(self, texts: Union[str, List[str]], 
                                      max_length: int) -> List[str]:
        """Validate and normalize input texts."""
        if isinstance(texts, str):
            texts = [texts]
        
        if not texts:
            raise InvalidInputError("No texts provided")
        
        normalized = []
        for text in texts:
            if not isinstance(text, str):
                logger.warning(f"Skipping non-string input: {type(text)}")
                continue
            
            # Clean and truncate text
            cleaned = re.sub(r'\s+', ' ', text.strip())
            if len(cleaned) > max_length:
                cleaned = cleaned[:max_length]
                logger.warning(f"Text truncated to {max_length} characters")
            
            if cleaned:
                normalized.append(cleaned)
        
        return normalized

    def _process_batch(self, texts: List[str]) -> List[Dict[str, Any]]:
        """Process a batch of texts for entity extraction."""
        results = []
        
        try:
            # Process with spaCy
            docs = list(self.nlp.pipe(texts))
            
            for i, doc in enumerate(docs):
                start_time = time.time()
                
                # Extract entities
                entities = self._extract_entities_from_doc(doc)
                
                # Calculate confidence
                confidence = self._calculate_overall_confidence(entities)
                
                processing_time = time.time() - start_time
                
                results.append({
                    'text': texts[i],
                    'entities': entities,
                    'confidence_score': confidence,
                    'processing_time': processing_time,
                    'total_entities': len(entities)
                })
            
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            # Return empty results for failed batch
            for text in texts:
                results.append({
                    'text': text,
                    'entities': [],
                    'confidence_score': 0.0,
                    'processing_time': 0.0,
                    'total_entities': 0
                })
        
        return results
    
    def _extract_entities_from_doc(self, doc: spacy.tokens.Doc) -> List[Dict[str, Any]]:
        """Extract entities from a spaCy document."""
        entities = []
        
        for ent in doc.ents:
            confidence = self._get_calibrated_confidence(ent)
            
            entities.append({
                'text': ent.text,
                'label': ent.label_,
                'start': ent.start_char,
                'end': ent.end_char,
                'score': confidence,
                'length': len(ent.text)
            })
        
        return entities

    def _get_calibrated_confidence(self, entity: spacy.tokens.Span) -> float:
        """Get calibrated confidence score for an entity."""
        try:
            # Try to get calibrated confidence
            if hasattr(entity, 'score') and entity.score is not None:
                return self._apply_calibration(entity.label_, entity.score)
            else:
                return self._heuristic_confidence(entity)
        except Exception:
            return self._heuristic_confidence(entity)

    def _heuristic_confidence(self, entity: spacy.tokens.Span) -> float:
        """Calculate heuristic confidence based on entity properties."""
        base_score = 0.7
        
        # Length-based adjustment
        length_factor = min(len(entity.text) / 10.0, 1.0)
        base_score += length_factor * 0.2
        
        # Label-based adjustment
        high_confidence_labels = ['EMPLOYEE_ID', 'EMAIL', 'PHONE', 'DATE']
        if entity.label_ in high_confidence_labels:
            base_score += 0.1
        
        # Pattern-based adjustment
        if entity.text.isupper():
            base_score += 0.05
        
        return min(base_score, 1.0)

    def _apply_calibration(self, label: str, raw_score: float) -> float:
        """Apply calibration to raw confidence scores."""
        if not self.calibration_stats:
            return raw_score
        
        if label in self.calibration_stats:
            stats = self.calibration_stats[label]
            # Simple calibration: adjust based on historical performance
            calibrated = raw_score * stats.get('calibration_factor', 1.0)
            return max(0.0, min(1.0, calibrated))
        
        return raw_score

    def _calculate_overall_confidence(self, entities: List[Dict[str, Any]]) -> float:
        """Calculate overall confidence for the extraction result."""
        if not entities:
            return 0.0
        
        # Weighted average based on entity scores and lengths
        total_weight = 0
        weighted_sum = 0
        
        for entity in entities:
            weight = entity.get('length', 1)
            score = entity.get('score', 0.0)
            
            total_weight += weight
            weighted_sum += score * weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0

    def load_calibration_stats(self):
        """Load calibration statistics from file."""
        try:
            if CALIBRATION_PATH.exists():
                with open(CALIBRATION_PATH, 'r') as f:
                    self.calibration_stats = json.load(f)
                logger.info("Calibration stats loaded successfully")
            else:
                logger.info("No calibration stats found, using default confidence scoring")
        except Exception as e:
            logger.warning(f"Error loading calibration stats: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            'model_path': str(self.model_path),
            'pipeline_components': self.nlp.pipe_names if self.nlp else [],
            'ner_labels': list(self.nlp.get_pipe("ner").labels) if self.nlp and "ner" in self.nlp.pipe_names else []
        }

    def load_patterns_from_json(self, file_path: Path):
        """Load entity patterns from JSON file."""
        try:
            with open(file_path, 'r') as f:
                self.patterns = json.load(f)
            logger.info(f"Patterns loaded from {file_path}")
        except Exception as e:
            logger.error(f"Error loading patterns: {e}")
            raise PatternManagementError(f"Failed to load patterns: {e}")

    def export_patterns_to_json(self, file_path: Path):
        """Export entity patterns to JSON file."""
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'w') as f:
                json.dump(self.patterns, f, indent=2)
            logger.info(f"Patterns exported to {file_path}")
        except Exception as e:
            logger.error(f"Error exporting patterns: {e}")
            raise PatternManagementError(f"Failed to export patterns: {e}")

    def add_entity_ruler_patterns(self):
        """Add SOTA HR-specific entity ruler patterns for advanced extraction."""
        try:
            if "entity_ruler" not in self.nlp.pipe_names:
                ruler = self.nlp.add_pipe("entity_ruler")
                
                # SOTA HR patterns with advanced matching
                hr_patterns = [
                    # Employee ID patterns (various formats)
                    {"label": "EMPLOYEE_ID", "pattern": [{"SHAPE": "XXXXX"}]},
                    {"label": "EMPLOYEE_ID", "pattern": [{"TEXT": {"REGEX": r"^(EMP|E|ID)\d{3,8}$"}}]},
                    {"label": "EMPLOYEE_ID", "pattern": [{"TEXT": {"REGEX": r"^[A-Z]{2,3}\d{4,6}$"}}]},
                    
                    # Email patterns
                    {"label": "EMAIL", "pattern": [{"LIKE_EMAIL": True}]},
                    
                    # Phone patterns (international formats)
                    {"label": "PHONE", "pattern": [{"TEXT": {"REGEX": r"^\+?[\d\s\-\(\)]{10,15}$"}}]},
                    {"label": "PHONE", "pattern": [{"LIKE_NUM": True, "LENGTH": {"IN": [10, 11, 12, 13, 14]}}]},
                    
                    # Date patterns (multiple formats)
                    {"label": "DATE", "pattern": [{"SHAPE": "dddd"}]},
                    {"label": "DATE", "pattern": [{"TEXT": {"REGEX": r"\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4}"}}]},
                    {"label": "DATE", "pattern": [{"TEXT": {"REGEX": r"\d{4}-\d{2}-\d{2}"}}]},
                    
                    # Salary/Compensation patterns
                    {"label": "SALARY", "pattern": [{"TEXT": {"REGEX": r"\$[\d,]+(\.\d{2})?"}}]},
                    {"label": "SALARY", "pattern": [{"TEXT": {"REGEX": r"₹[\d,]+(\.\d{2})?"}}]},
                    {"label": "SALARY", "pattern": [{"TEXT": {"REGEX": r"[\d,]+(\.\d{2})?\s*(USD|INR|EUR|GBP)"}}]},
                    
                    # Department patterns (comprehensive list)
                    {"label": "DEPARTMENT", "pattern": [{"LOWER": {"IN": [
                        "human resources", "hr", "finance", "accounting", "it", "information technology",
                        "engineering", "software engineering", "marketing", "sales", "operations",
                        "legal", "compliance", "customer support", "customer service", "admin",
                        "administration", "procurement", "supply chain", "quality assurance", "qa",
                        "research and development", "r&d", "product", "product management",
                        "business development", "strategy", "executive", "ceo", "cto", "cfo"
                    ]}}]},
                
                # Leave type patterns
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "sick"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "maternity"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "paternity"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "casual"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "earned"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "annual"}, {"LOWER": "leave"}]},
                {"label": "LEAVE_TYPE", "pattern": [{"LOWER": "compensatory"}, {"LOWER": "leave"}]},
                
                # Document type patterns
                {"label": "DOCUMENT_TYPE", "pattern": [{"LOWER": {"IN": [
                    "payslip", "pay slip", "salary slip", "offer letter", "appointment letter",
                    "experience letter", "relieving letter", "termination letter", "contract",
                    "agreement", "policy", "handbook", "manual", "certificate", "id card",
                    "visiting card", "business card", "invoice", "receipt", "form"
                ]}}]},
                
                # Benefit type patterns
                {"label": "BENEFIT_TYPE", "pattern": [{"LOWER": {"IN": [
                    "health insurance", "medical insurance", "dental insurance", "vision insurance",
                    "life insurance", "disability insurance", "gratuity", "provident fund", "pf",
                    "meal allowance", "transport allowance", "housing allowance", "education allowance",
                    "bonus", "incentive", "commission", "stock options", "esop", "retirement plan",
                    "pension", "401k", "superannuation"
                ]}}]},
                    
                # Issue type patterns
                {"label": "ISSUE_TYPE", "pattern": [{"LOWER": {"IN": [
                    "incorrect", "wrong", "missing", "delayed", "pending", "rejected",
                    "approved", "denied", "error", "problem", "issue", "concern", "complaint",
                    "dispute", "conflict", "overdue", "late", "failed", "successful"
                ]}}]},
                    
                # Location patterns
                {"label": "LOCATION", "pattern": [{"ENT_TYPE": "GPE"}]},
                {"label": "LOCATION", "pattern": [{"TEXT": {"REGEX": r"^[A-Z][a-z]+(?:[\s\-][A-Z][a-z]+)*$"}}]},
                
                # Month patterns
                {"label": "month", "pattern": [{"LOWER": {"IN": [
                    "january", "february", "march", "april", "may", "june",
                    "july", "august", "september", "october", "november", "december",
                    "jan", "feb", "mar", "apr", "jun", "jul", "aug", "sep", "oct", "nov", "dec"
                ]}}]},
                
                # Year patterns
                {"label": "year", "pattern": [{"TEXT": {"REGEX": r"^(19|20)\d{2}$"}}]},
                
                # Salary component patterns
                {"label": "salary_component", "pattern": [{"LOWER": {"IN": [
                    "basic salary", "basic pay", "gross salary", "net salary", "ctc", "cost to company",
                    "hra", "house rent allowance", "da", "dearness allowance", "ta", "transport allowance",
                    "ma", "medical allowance", "lta", "leave travel allowance", "special allowance",
                    "performance bonus", "joining bonus", "retention bonus", "overtime", "shift allowance"
                ]}}]},
            ]
            
            ruler.add_patterns(hr_patterns)
            logger.info(f"SOTA HR entity ruler patterns added successfully ({len(hr_patterns)} patterns)")

        except Exception as e:
            logger.warning(f"Error adding entity ruler patterns: {e}")

    def train_model(self, 
                    training_data_path: Path = TRAINING_DATA_PATH, 
                   n_iter: int = 25,  # SOTA: Reduced iterations for faster training
                   dropout: float = 0.4,  # SOTA: Higher dropout for regularization
                   learn_rate: float = 0.001,  # SOTA: Lower LR for stability
                   batch_size: int = 16,  # SOTA: Optimal batch size
                   patience: int = 5,  # SOTA: Early stopping patience
                   min_loss_threshold: float = 500.0,  # SOTA: Loss threshold
                   use_transformer: bool = True,  # SOTA: Use transformer architecture
                    output_path: Path = MODEL_DIR):
        """
        Train the NER model with improved parameters.

        Args:
            training_data_path: Path to training data file
            n_iter: Number of training iterations (increased to 20)
            dropout: Dropout rate (increased to 0.3)
            learn_rate: Learning rate (increased to 0.002)
            output_path: Output path for trained model
        """
        logger.info(f"Starting model training from data at {training_data_path}")
        
        # Load and validate training data
        TRAIN_DATA = []
        problematic_lines = []
        valid_lines = []

        try:
            if not training_data_path.exists():
                raise ModelUpdateError(f"Training data file not found: {training_data_path}")
            
            # Read training data with improved validation
            with open(training_data_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                total_lines = len(lines)
                    
                for line_num, original_line in enumerate(lines, 1):
                    try:
                        data = json.loads(original_line)
                        
                        # Handle different data formats
                        if 'spans' in data:
                            entities = [(s['start'], s['end'], s['label']) for s in data['spans']]
                        elif 'entities' in data:
                            if isinstance(data['entities'], list) and data['entities']:
                                if isinstance(data['entities'][0], dict):
                                    # Convert dict format to tuple
                                    entities = [(e['start'], e['end'], e['label']) for e in data['entities']]
                                else:
                                    # Already in tuple format
                                    entities = data['entities']
                            else:
                                entities = []
                        else:
                            logger.warning(f"Line {line_num}: No entities or spans found, skipping")
                            problematic_lines.append(original_line)
                            continue
                        
                        # Validate entities with lenient overlap detection
                        text = data.get('text', '')
                        valid_entities = []
                        has_problem = False
                        
                        # Check for overlapping entities (more lenient)
                        for start, end, label in entities:
                            # Check entity boundaries first
                            if not (0 <= start < end <= len(text)):
                                has_problem = True
                                break
                            
                            # Only check for exact overlaps (not adjacent entities)
                            for other_start, other_end, other_label in valid_entities:
                                # Check if entities overlap (not just adjacent)
                                if (start < other_end and end > other_start):
                                    has_problem = True
                                    break
                            
                            if has_problem:
                                break
                            
                            valid_entities.append((start, end, label))
                        
                        # Skip alignment checks - let spaCy handle all entities
                        # This ensures all valid entities are used for training
                        
                        if has_problem or not valid_entities:
                            problematic_lines.append(original_line)
                            continue
                        
                        TRAIN_DATA.append((text, {'entities': valid_entities}))
                        valid_lines.append(original_line)
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"Line {line_num}: Invalid JSON, skipping - {e}")
                        problematic_lines.append(original_line)
                        continue
                    except KeyError as e:
                        logger.warning(f"Line {line_num}: Missing required field {e}, skipping")
                        problematic_lines.append(original_line)
                        continue
                    except Exception as e:
                        logger.warning(f"Line {line_num}: Unexpected error, skipping - {e}")
                        problematic_lines.append(original_line)
                        continue
                        
            logger.info(f"Loaded {len(TRAIN_DATA)} valid training examples out of {total_lines} total lines")
            logger.info(f"Found {len(problematic_lines)} problematic lines")
            
            # Write problematic data to file (only the original data, no metadata)
            problematic_data_path = training_data_path.parent / f"{training_data_path.stem}_problematic.jsonl"
            if problematic_lines:
                with open(problematic_data_path, 'w', encoding='utf-8') as f:
                    for line in problematic_lines:
                        f.write(line + '\n')
                logger.info(f"Problematic data written to: {problematic_data_path}")
            
            # Note: Not rewriting original file since it's read-only in Kaggle
            logger.info(f"Using {len(valid_lines)} valid lines for training")
            
            if not TRAIN_DATA:
                raise ModelUpdateError("No valid training data loaded")
                
        except Exception as e:
            logger.error(f"Error loading training data: {e}")
            raise ModelUpdateError(f"Failed to load training data: {e}")

        # Initialize model for training
        try:
            # Start with blank model for custom training
            nlp = spacy.blank("en")
            ner = nlp.add_pipe("ner")
            
            # Add all labels from training data
            for text, annotations in TRAIN_DATA:
                for start, end, label in annotations.get('entities', []):
                    ner.add_label(label)
            
            logger.info(f"Added {len(ner.labels)} unique labels to NER component")
            
        except Exception as e:
            logger.error(f"Error initializing model for training: {e}")
            raise ModelUpdateError(f"Model initialization failed: {e}")

        # Convert to spaCy Examples with forced entity usage
        try:
            examples = []
            # Suppress alignment warnings to force use of all entities
            import warnings
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", UserWarning)
                
            for text, annotations in TRAIN_DATA:
                doc = nlp.make_doc(text)
                example = Example.from_dict(doc, annotations)
                examples.append(example)
                
        except Exception as e:
            logger.error(f"Error creating training examples: {e}")
            raise ModelUpdateError(f"Example creation failed: {e}")

        # Initialize training with improved parameters
        optimizer = nlp.initialize()
        
        # Training loop with SOTA monitoring and early stopping
        logger.info("Starting SOTA training...")
        best_loss = float('inf')
        patience_counter = 0
        loss_history = []
        
        for iteration in range(n_iter):
            random.shuffle(examples)
            losses = {}
            
            # Process in batches with SOTA batch size
            batches = spacy.util.minibatch(examples, size=batch_size)
            for batch in batches:
                nlp.update(batch, drop=dropout, losses=losses, sgd=optimizer)
            
            # SOTA loss monitoring and early stopping
            current_loss = losses.get('ner', 0)
            loss_history.append(current_loss)
            
            logger.info(f"Iteration {iteration + 1}/{n_iter} - Loss: {current_loss:.2f}")
            
            # Early stopping logic
            if current_loss < best_loss:
                best_loss = current_loss
                patience_counter = 0
                # Save best model
                try:
                    nlp.to_disk(output_path / "best_model")
                    logger.info(f"New best model saved (loss: {best_loss:.2f})")
                except Exception as e:
                    logger.warning(f"Could not save best model: {e}")
            else:
                patience_counter += 1
                
            # Early stopping if loss plateaus
            if patience_counter >= patience:
                logger.info(f"Early stopping at iteration {iteration + 1} (patience: {patience})")
                break
                
            # Stop if loss is very low (converged)
            if current_loss < min_loss_threshold:
                logger.info(f"Loss converged below threshold {min_loss_threshold} at iteration {iteration + 1}")
                break
                
            # Note: spaCy handles learning rate scheduling automatically
        
        # Load best model if early stopping occurred
        if (output_path / "best_model").exists():
            try:
                nlp = spacy.load(output_path / "best_model")
                logger.info("Loaded best model from early stopping")
            except Exception as e:
                logger.warning(f"Could not load best model: {e}")
        
        logger.info(f"Training completed. Best loss: {best_loss:.2f}")
        logger.info(f"Loss history: {[f'{l:.2f}' for l in loss_history[-5:]]}")  # Last 5 losses

        # Save trained model
        try:
            output_path.mkdir(parents=True, exist_ok=True)
            nlp.to_disk(output_path)
            logger.info(f"Trained model saved to {output_path}")
            
            # Update current instance
            self.nlp = nlp
            self.add_entity_ruler_patterns()
            
        except Exception as e:
            logger.error(f"Error saving trained model: {e}")
            raise ModelUpdateError(f"Model saving failed: {e}")

    def train_model_enhanced(self, 
                            training_data_path: Path = TRAINING_DATA_PATH, 
                            n_iter: int = 30,  # Reduced: 30 epochs to prevent overfitting
                            dropout: float = 0.5,  # Increased: 0.5 dropout for better regularization
                            learn_rate: float = 0.001,  # Reduced: 0.001 learning rate for stability
                            batch_size: int = 64,  # Increased: 64 batch size for better gradients
                            patience: int = 5,  # Reduced: 5 patience for early stopping
                            min_loss_threshold: float = 2000.0,  # Increased: 2000 loss target
                            use_transformer: bool = True,  # Enhanced: Use transformer architecture
                            metrics_interval: int = 3,  # Increased: Metrics every 3 iterations
                            output_path: Path = MODEL_DIR) -> Dict[str, Any]:
        """
        Enhanced training method with detailed accuracy metrics and monitoring.

        Args:
            training_data_path: Path to training data file
            n_iter: Number of training iterations (50 epochs)
            dropout: Dropout rate (0.3)
            learn_rate: Learning rate (0.002)
            batch_size: Batch size (32)
            patience: Early stopping patience (8)
            min_loss_threshold: Loss threshold (300)
            use_transformer: Use transformer architecture
            metrics_interval: How often to calculate metrics
            output_path: Output path for trained model

        Returns:
            Dict containing training results and metrics
        """
        logger.info(f"Starting enhanced model training from data at {training_data_path}")
        
        # Load and validate training data (same as original method)
        TRAIN_DATA = []
        problematic_lines = []
        valid_lines = []

        try:
            if not training_data_path.exists():
                raise ModelUpdateError(f"Training data file not found: {training_data_path}")
            
            # Read training data with improved validation
            with open(training_data_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                total_lines = len(lines)
                    
                for line_num, original_line in enumerate(lines, 1):
                    try:
                        data = json.loads(original_line)
                        
                        # Handle different data formats
                        if 'spans' in data:
                            entities = [(s['start'], s['end'], s['label']) for s in data['spans']]
                        elif 'entities' in data:
                            if isinstance(data['entities'], list) and data['entities']:
                                if isinstance(data['entities'][0], dict):
                                    # Convert dict format to tuple
                                    entities = [(e['start'], e['end'], e['label']) for e in data['entities']]
                                else:
                                    # Already in tuple format
                                    entities = data['entities']
                            else:
                                entities = []
                        else:
                            logger.warning(f"Line {line_num}: No entities or spans found, skipping")
                            problematic_lines.append(original_line)
                            continue
                        
                        # Validate entities with very lenient approach to maximize training data
                        text = data.get('text', '')
                        valid_entities = []
                        
                        # Very lenient validation - only reject completely invalid entities
                        for start, end, label in entities:
                            # Only check basic boundary validity
                            if start < 0 or end > len(text) or start >= end:
                                continue  # Skip this entity but continue with others
                            
                            # Add entity without strict overlap checking
                            valid_entities.append((start, end, label))
                        
                        # Accept data if we have any valid entities or if it's a valid JSON
                        if valid_entities or ('text' in data and data['text'].strip()):
                            TRAIN_DATA.append((text, {'entities': valid_entities}))
                            valid_lines.append(original_line)
                        else:
                            problematic_lines.append(original_line)
                            continue
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"Line {line_num}: Invalid JSON, skipping - {e}")
                        problematic_lines.append(original_line)
                        continue
                    except KeyError as e:
                        logger.warning(f"Line {line_num}: Missing required field {e}, skipping")
                        problematic_lines.append(original_line)
                        continue
                    except Exception as e:
                        logger.warning(f"Line {line_num}: Unexpected error, skipping - {e}")
                        problematic_lines.append(original_line)
                        continue
                        
            logger.info(f"Loaded {len(TRAIN_DATA)} valid training examples out of {total_lines} total lines")
            logger.info(f"Found {len(problematic_lines)} problematic lines")
            
            if not TRAIN_DATA:
                raise ModelUpdateError("No valid training data loaded")
                
        except Exception as e:
            logger.error(f"Error loading training data: {e}")
            raise ModelUpdateError(f"Failed to load training data: {e}")

        # Initialize model for training
        try:
            # Start with blank model for custom training
            nlp = spacy.blank("en")
            ner = nlp.add_pipe("ner")
            
            # Add all labels from training data
            for text, annotations in TRAIN_DATA:
                for start, end, label in annotations.get('entities', []):
                    ner.add_label(label)
            
            logger.info(f"Added {len(ner.labels)} unique labels to NER component")
            
        except Exception as e:
            logger.error(f"Error initializing model for training: {e}")
            raise ModelUpdateError(f"Model initialization failed: {e}")

        # Convert to spaCy Examples with better alignment
        try:
            examples = []
            import warnings
            with warnings.catch_warnings():
                warnings.simplefilter("ignore", UserWarning)
                
            for text, annotations in TRAIN_DATA:
                doc = nlp.make_doc(text)
                # Clean and align entities to prevent warnings
                cleaned_entities = []
                for start, end, label in annotations.get('entities', []):
                    # Ensure boundaries are within text length and aligned to token boundaries
                    start = max(0, min(start, len(text)))
                    end = max(start + 1, min(end, len(text)))
                    
                    # Check if entity text matches the boundaries
                    entity_text = text[start:end].strip()
                    if entity_text and start < end:
                        # Ensure entity doesn't span across word boundaries incorrectly
                        if entity_text and not entity_text.startswith(' ') and not entity_text.endswith(' '):
                            cleaned_entities.append((start, end, label))
                
                # Create example with cleaned entities
                cleaned_annotations = {'entities': cleaned_entities}
                example = Example.from_dict(doc, cleaned_annotations)
                examples.append(example)
                
        except Exception as e:
            logger.error(f"Error creating training examples: {e}")
            raise ModelUpdateError(f"Example creation failed: {e}")

        # Initialize training with enhanced parameters
        optimizer = nlp.initialize()
        
        # Enhanced training loop with anti-overfitting measures
        logger.info("Starting enhanced training with anti-overfitting measures...")
        best_loss = float('inf')
        best_f1 = 0.0
        patience_counter = 0
        loss_history = []
        metrics_history = []
        overfitting_counter = 0
        
        # Split data for evaluation (80% train, 20% eval)
        random.shuffle(examples)
        split_idx = int(len(examples) * 0.8)
        train_examples = examples[:split_idx]
        eval_examples = examples[split_idx:]
        
        logger.info(f"Training on {len(train_examples)} examples, evaluating on {len(eval_examples)} examples")
        
        def calculate_metrics(model, eval_data):
            """Calculate precision, recall, and F1 score"""
            tp, fp, fn = 0, 0, 0
            total_entities = 0
            
            for example in eval_data:
                # Get predicted entities
                doc = model(example.text)
                predicted_entities = set()
                for ent in doc.ents:
                    predicted_entities.add((ent.start_char, ent.end_char, ent.label_))
                
                # Get true entities - handle different formats safely
                true_entities = set()
                try:
                                            # Handle spaCy Example format - check if it's a Doc object
                        if hasattr(example.reference, 'ents') and hasattr(example.reference.ents, '__iter__'):
                            for entity in example.reference.ents:
                                if hasattr(entity, 'start_char') and hasattr(entity, 'end_char') and hasattr(entity, 'label_'):
                                    true_entities.add((entity.start_char, entity.end_char, entity.label_))
                                elif isinstance(entity, (list, tuple)):
                                    if len(entity) == 3:
                                        start, end, label = entity
                                        true_entities.add((start, end, label))
                                    elif len(entity) == 2:
                                        start, end = entity
                                        true_entities.add((start, end, "UNKNOWN"))
                        # Handle dict format
                        elif isinstance(example.reference, dict) and 'entities' in example.reference:
                            for entity in example.reference['entities']:
                                if len(entity) == 3:
                                    start, end, label = entity
                                    true_entities.add((start, end, label))
                                elif len(entity) == 2:
                                    start, end = entity
                                    true_entities.add((start, end, "UNKNOWN"))
                        # Handle direct entities list
                        elif isinstance(example.reference, (list, tuple)):
                            for entity in example.reference:
                                if len(entity) == 3:
                                    start, end, label = entity
                                    true_entities.add((start, end, label))
                                elif len(entity) == 2:
                                    start, end = entity
                                    true_entities.add((start, end, "UNKNOWN"))
                        # Handle the actual spaCy Example format
                        else:
                            # Try to access entities from the example annotations
                            try:
                                if hasattr(example, 'reference') and hasattr(example.reference, 'ents'):
                                    for entity in example.reference.ents:
                                        if hasattr(entity, 'start_char') and hasattr(entity, 'end_char') and hasattr(entity, 'label_'):
                                            true_entities.add((entity.start_char, entity.end_char, entity.label_))
                                        else:
                                            # Skip problematic entities
                                            continue
                            except Exception as e:
                                logger.warning(f"Could not process entities: {e}")
                                continue
                except Exception as e:
                    logger.warning(f"Error processing entities: {e}")
                    continue
                
                total_entities += len(true_entities)
                
                # Calculate TP, FP, FN
                tp += len(predicted_entities.intersection(true_entities))
                fp += len(predicted_entities - true_entities)
                fn += len(true_entities - predicted_entities)
            
            # Calculate metrics
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            
            return {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'tp': tp,
                'fp': fp,
                'fn': fn,
                'total_entities': total_entities
            }
        
        for iteration in range(n_iter):
            random.shuffle(train_examples)
            losses = {}
            
            # Process in batches with enhanced batch size
            batches = spacy.util.minibatch(train_examples, size=batch_size)
            for batch in batches:
                nlp.update(batch, drop=dropout, losses=losses, sgd=optimizer)
            
            # Enhanced loss monitoring
            current_loss = losses.get('ner', 0)
            loss_history.append(current_loss)
            
            # Calculate metrics every metrics_interval iterations (with error handling)
            current_metrics = None
            if iteration % metrics_interval == 0 or iteration == n_iter - 1:
                try:
                    current_metrics = calculate_metrics(nlp, eval_examples)
                    metrics_history.append({
                        'iteration': iteration,
                        'loss': current_loss,
                        **current_metrics
                    })
                    
                    logger.info(f"Iteration {iteration + 1}/{n_iter} - Loss: {current_loss:.2f}")
                    logger.info(f"  Precision: {current_metrics['precision']:.3f}")
                    logger.info(f"  Recall: {current_metrics['recall']:.3f}")
                    logger.info(f"  F1 Score: {current_metrics['f1']:.3f}")
                    logger.info(f"  Entities: {current_metrics['total_entities']} (TP: {current_metrics['tp']}, FP: {current_metrics['fp']}, FN: {current_metrics['fn']})")
                except Exception as e:
                    logger.warning(f"Metrics calculation failed: {e}")
                    logger.info(f"Iteration {iteration + 1}/{n_iter} - Loss: {current_loss:.2f}")
            else:
                logger.info(f"Iteration {iteration + 1}/{n_iter} - Loss: {current_loss:.2f}")
            
            # Enhanced early stopping logic with F1-based validation
            if current_metrics and 'f1' in current_metrics:
                current_f1 = current_metrics['f1']
                if current_f1 > best_f1:
                    best_f1 = current_f1
                    patience_counter = 0
                    # Save best model based on F1 score
                    try:
                        nlp.to_disk(output_path / "best_model")
                        logger.info(f"New best model saved (F1: {best_f1:.3f}, loss: {current_loss:.2f})")
                    except Exception as e:
                        logger.warning(f"Could not save best model: {e}")
                else:
                    patience_counter += 1
                    overfitting_counter += 1
                    logger.info(f"F1 not improving (current: {current_f1:.3f}, best: {best_f1:.3f})")
            else:
                # Fallback to loss-based early stopping
                if current_loss < best_loss:
                    best_loss = current_loss
                    patience_counter = 0
                    try:
                        nlp.to_disk(output_path / "best_model")
                        logger.info(f"New best model saved (loss: {best_loss:.2f})")
                    except Exception as e:
                        logger.warning(f"Could not save best model: {e}")
                else:
                    patience_counter += 1
                
            # Early stopping if loss plateaus
            if patience_counter >= patience:
                logger.info(f"Early stopping at iteration {iteration + 1} (patience: {patience})")
                break
                
            # Stop if loss is very low (converged)
            if current_loss < min_loss_threshold:
                logger.info(f"Loss converged below threshold {min_loss_threshold} at iteration {iteration + 1}")
                break
        
        # Load best model if early stopping occurred
        if (output_path / "best_model").exists():
            try:
                nlp = spacy.load(output_path / "best_model")
                logger.info("Loaded best model from early stopping")
            except Exception as e:
                logger.warning(f"Could not load best model: {e}")
        
        # Calculate final metrics with error handling
        try:
            final_metrics = calculate_metrics(nlp, eval_examples)
            logger.info(f"Enhanced training completed. Best loss: {best_loss:.2f}")
            logger.info(f"Final metrics - Precision: {final_metrics['precision']:.3f}, Recall: {final_metrics['recall']:.3f}, F1: {final_metrics['f1']:.3f}")
        except Exception as e:
            logger.warning(f"Final metrics calculation failed: {e}")
            final_metrics = {
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0,
                'total_entities': 0,
                'tp': 0,
                'fp': 0,
                'fn': 0
            }
            logger.info(f"Enhanced training completed. Best loss: {best_loss:.2f}")

        # Save trained model
        try:
            output_path.mkdir(parents=True, exist_ok=True)
            nlp.to_disk(output_path)
            logger.info(f"Trained model saved to {output_path}")
            
            # Update current instance
            self.nlp = nlp
            self.add_entity_ruler_patterns()
            
        except Exception as e:
            logger.error(f"Error saving trained model: {e}")
            raise ModelUpdateError(f"Model saving failed: {e}")
        
        # Return comprehensive training results
        return {
            'best_loss': best_loss,
            'final_precision': final_metrics['precision'],
            'final_recall': final_metrics['recall'],
            'final_f1': final_metrics['f1'],
            'total_entities': final_metrics['total_entities'],
            'iterations_completed': iteration + 1,
            'loss_history': loss_history,
            'metrics_history': metrics_history,
            'training_examples': len(train_examples),
            'eval_examples': len(eval_examples)
        }


# Example usage and testing
if __name__ == "__main__":
    try:
        # Create sample training data for testing
        dummy_training_data_path = DATA_DIR / "training" / "entity_training_data.jsonl"
        
        if not dummy_training_data_path.exists():
            sample_data = [
                {"text": "I need my pay slip for December 2023.", "entities": [(12, 20, "DOCUMENT_TYPE")]},
                {"text": "What is the sick leave policy?", "entities": [(12, 22, "LEAVE_TYPE")]},
                {"text": "My employee ID is EMP123.", "entities": [(18, 24, "EMPLOYEE_ID")]},
                {"text": "I work in the Finance department.", "entities": [(14, 21, "DEPARTMENT")]},
                {"text": "Can I check health insurance benefits?", "entities": [(12, 28, "BENEFIT_TYPE")]}
            ]
            
            dummy_training_data_path.parent.mkdir(parents=True, exist_ok=True)
            with open(dummy_training_data_path, 'w', encoding='utf-8') as f:
                for entry in sample_data:
                    json.dump(entry, f, ensure_ascii=False)
                    f.write('\n')
            
            logger.info(f"Created sample training data at {dummy_training_data_path}")

    # Initialize extractor
        extractor = EntityExtractor(model_path=MODEL_DIR)
        
        # Train model
        print("\n--- Starting Model Training ---")
        extractor.train_model(training_data_path=dummy_training_data_path, n_iter=20)
        print("--- Model Training Complete ---")

        # Test extraction
        print("\n=== Entity Extraction Results ===")
        test_texts = [
            "I need my pay slip for December 2023.",
            "What is the sick leave policy for software engineers?",
            "Can I check my health insurance benefits in Mumbai office?",
            "My employee ID is EMP123 and I work in the Finance department.",
            "I want to apply for maternity leave starting from January 2024."
        ]

        results = extractor.extract_entities(test_texts)
        
        for i, result in enumerate(results, 1):
            print(f"\nText {i}: '{result['text']}'")
            print(f"Processing Time: {result['processing_time']:.4f}s")
            print(f"Overall Confidence: {result['confidence_score']:.3f}")
            
            if result['entities']:
                for entity in result['entities']:
                    print(f"   • '{entity['text']}' -> {entity['label']} (confidence: {entity['score']:.3f})")
            else:
                print("   • No entities found")

    except Exception as e:
        print(f"Error in main execution: {e}")
        import traceback
        traceback.print_exc()
