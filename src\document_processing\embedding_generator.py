"""
Embedding generation and direct push to Qdrant Cloud with optimized Singleton pattern.
Implements lazy loading, device optimization, and centralized tokenizer management.
"""

import os
import numpy as np
from typing import List, Dict, Any, Optional
from pathlib import Path
from uuid import uuid4
from dotenv import load_dotenv

import torch
import torch.nn.functional as F
from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer, AutoModel

from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct

from ..utils.logger import get_logger
from ..utils.device_manager import get_optimal_device, get_device_info
from ..utils.tokenizer_registry import get_bge_tokenizer
from ..utils.async_task_manager import async_task, run_gpu_task
from ..config import (
    EMBEDDING_MODEL_NAME, VECTOR_DIMENSION, EMBEDDING_MODEL_DIR, 
    EMBEDDING_TOKENIZER_DIR, LAZY_LOADING, DEV_LOGGING
)

# Load environment variables
load_dotenv()
logger = get_logger(__name__)

QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
QDRANT_URL = os.getenv("QDRANT_URL")
QDRANT_COLLECTION_NAME = os.getenv("QDRANT_COLLECTION_NAME")

class EmbeddingGenerator:
    """Handles embedding generation and syncing with Qdrant Cloud (Optimized Singleton)."""

    _instance = None
    _initialized = False
    _model_loaded = False
    _dimension_detected = False

    def __new__(cls, model_name: str = EMBEDDING_MODEL_NAME, force_cpu: bool = False):
        if cls._instance is None:
            cls._instance = super(EmbeddingGenerator, cls).__new__(cls)
        return cls._instance

    def __init__(self, model_name: str = EMBEDDING_MODEL_NAME, force_cpu: bool = False):
        if self._initialized:
            return
            
        self.model_name = model_name
        self.force_cpu = force_cpu
        self.device = self._select_device()
        self.actual_dimension = VECTOR_DIMENSION  # Default value
        
        # Initialize Qdrant client
        self.qdrant_client = QdrantClient(
            url=QDRANT_URL,
            api_key=QDRANT_API_KEY
        )
        
        self._initialized = True
        
        # Lazy load model and detect dimension only when needed
        if not LAZY_LOADING['embedding_dimension_detection']:
            self._initialize_model()
            self._detect_embedding_dimension()

    def _select_device(self):
        """Select optimal device using the device manager."""
        if self.force_cpu:
            if DEV_LOGGING['verbose_startup']:
                logger.info("[TOOL] Forcing CPU-only mode")
            return torch.device("cpu")
        
        # Use centralized device manager
        device = get_optimal_device()
        device_info = get_device_info()
        
        if DEV_LOGGING['verbose_startup']:
            logger.info(f"[DEVICE] Using {device_info['device_name']}")
        
        return device

    def _initialize_model(self):
        """Initialize the embedding model (lazy loading)."""
        if self._model_loaded:
            return
            
        try:
            # Load from local cache path
            local_model_path = "data/models_cache/bge-base-en-v1.5"
            
            if DEV_LOGGING['verbose_startup']:
                logger.info(f"Loading BGE model from local cache: {local_model_path}")
            
            # Use SentenceTransformer to load the local model
            self.model = SentenceTransformer(local_model_path, device=str(self.device))
            self.model_type = "sentence_transformer"
            self.tokenizer = None  # SentenceTransformer handles tokenization internally
            
            if DEV_LOGGING['verbose_startup']:
                logger.info("[OK] Successfully loaded BGE model from local cache")
                
            self._model_loaded = True
            
        except Exception as e:
            logger.error(f"Failed to load embedding model from local cache: {e}")
            raise

    def _detect_embedding_dimension(self):
        """Detect embedding dimension (lazy loading)."""
        if self._dimension_detected:
            return
            
        try:
            if DEV_LOGGING['verbose_startup']:
                logger.info("[SEARCH] Starting embedding dimension detection...")
                
            # Ensure model is loaded
            if not self._model_loaded:
                self._initialize_model()
                
            test_vector = self._generate_embedding("test")
            
            if DEV_LOGGING['verbose_startup']:
                logger.info("[OK] Embedding dimension detection completed")
                
            self.actual_dimension = len(test_vector)
            
            if self.actual_dimension != VECTOR_DIMENSION:
                logger.warning(f"[WARNING] Embedding dimension mismatch: config={VECTOR_DIMENSION}, actual={self.actual_dimension}")
                
            self._dimension_detected = True
            
        except Exception as e:
            logger.error(f"❌ Dimension detection failed: {e}")
            self.actual_dimension = VECTOR_DIMENSION
            self._dimension_detected = True

    def _generate_embedding(self, text: str, is_query: bool = False) -> np.ndarray:
        logger.info(f"[REFRESH] Generating embedding for text (first 20 chars): '{text[:20]}...'")
        result = self.generate_embeddings([text], is_query=is_query)[0]
        logger.info(f"[OK] Embedding generated successfully, shape: {result.shape}")
        return result

    def _mean_pooling(self, model_output, attention_mask):
        token_embeddings = model_output[0]
        input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
        pooled = torch.sum(token_embeddings * input_mask_expanded, dim=1)
        return pooled / torch.clamp(input_mask_expanded.sum(1), min=1e-9)

    def _huggingface_encode(self, texts: List[str]) -> np.ndarray:
        encoded_input = self.tokenizer(
            texts, padding=True, truncation=True, return_tensors='pt', max_length=256
        ).to(self.device)

        with torch.no_grad():
            model_output = self.model(**encoded_input)

        # CLS pooling for BGE models (recommended by BAAI)
        cls_embeddings = model_output.last_hidden_state[:, 0]  # CLS token
        pooled = F.normalize(cls_embeddings, p=2, dim=1)
        return pooled.cpu().numpy()

    def generate_embeddings(self, texts: List[str], is_query: bool = False) -> np.ndarray:
        # Ensure model is loaded (lazy loading)
        if not self._model_loaded:
            self._initialize_model()
        
        if DEV_LOGGING['decorative_banners']:
            logger.info("=" * 80)
            logger.info("🧠 EMBEDDING GENERATOR - GENERATE_EMBEDDINGS STARTED")
            logger.info("=" * 80)
            logger.info(f"📝 Number of texts: {len(texts)}")
            logger.info(f"🔍 Is query: {is_query}")
            logger.info(f"🧠 Model type: {getattr(self, 'model_type', 'Not loaded')}")
            logger.info(f"💻 Device: {self.device}")
            logger.info(f"📏 Text lengths: {[len(t) for t in texts[:5]]}{'...' if len(texts) > 5 else ''}")
            logger.info("=" * 80)
        
        # Prefix according to e5 requirements
        if is_query:
            texts = [f"query: {t}" for t in texts]
            if DEV_LOGGING['verbose_startup']:
                logger.info("🔍 Applied 'query:' prefix to texts")
        else:
            texts = [f"passage: {t}" for t in texts]
            if DEV_LOGGING['verbose_startup']:
                logger.info("📄 Applied 'passage:' prefix to texts")
        
        if DEV_LOGGING['verbose_startup']:
            logger.info(f"[TARGET] Starting model.encode with {len(texts)} texts, model_type: {getattr(self, 'model_type', 'Not loaded')}")
        
        try:
            if getattr(self, 'model_type', None) == "sentence_transformer":
                if DEV_LOGGING['verbose_startup']:
                    logger.info("🚀 Using SentenceTransformer encode method...")
                    logger.info(f"📊 Batch size: 32")
                    
                result = self.model.encode(texts, convert_to_numpy=True, batch_size=32)
                
                if DEV_LOGGING['verbose_startup']:
                    logger.info(f"✅ SentenceTransformer encode completed successfully")
                    logger.info(f"📊 Result shape: {result.shape}")
                    logger.info(f"📊 Result type: {type(result)}")
                    logger.info(f"📊 Result dtype: {result.dtype if hasattr(result, 'dtype') else 'N/A'}")
                
                if DEV_LOGGING['decorative_banners']:
                    logger.info("=" * 80)
                    logger.info("🎉 EMBEDDING GENERATOR - GENERATE_EMBEDDINGS COMPLETED SUCCESSFULLY")
                    logger.info("=" * 80)
                    logger.info(f"📝 Texts processed: {len(texts)}")
                    logger.info(f"🔍 Query mode: {is_query}")
                    logger.info(f"📊 Output shape: {result.shape}")
                    logger.info(f"🧠 Model: SentenceTransformer")
                    logger.info("=" * 80)
                
                return result
            else:
                if DEV_LOGGING['verbose_startup']:
                    logger.info("[REFRESH] Using HuggingFace encode method")
                    
                result = self._huggingface_encode(texts)
                
                if DEV_LOGGING['decorative_banners']:
                    logger.info("=" * 80)
                    logger.info("🎉 EMBEDDING GENERATOR - GENERATE_EMBEDDINGS COMPLETED SUCCESSFULLY")
                    logger.info("=" * 80)
                    logger.info(f"📝 Texts processed: {len(texts)}")
                    logger.info(f"🔍 Query mode: {is_query}")
                    logger.info(f"📊 Output shape: {result.shape}")
                    logger.info(f"🧠 Model: HuggingFace")
                    logger.info("=" * 80)
                
                return result
                
        except Exception as e:
            if DEV_LOGGING['decorative_banners']:
                logger.error("=" * 80)
                logger.error("❌ EMBEDDING GENERATOR - GENERATE_EMBEDDINGS FAILED")
                logger.error("=" * 80)
                logger.error(f"📝 Texts count: {len(texts)}")
                logger.error(f"🔍 Query mode: {is_query}")
                logger.error(f"❌ Error type: {type(e).__name__}")
                logger.error(f"❌ Error message: {str(e)}")
                logger.error(f"🔍 Traceback: {traceback.format_exc()}")
                logger.error("=" * 80)
            else:
                logger.error(f"Embedding generation failed: {e}")
            
            raise

    @async_task
    async def generate_embeddings_async(self, texts: List[str], is_query: bool = False) -> np.ndarray:
        """Async version of generate_embeddings for concurrent processing."""
        return self.generate_embeddings(texts, is_query)

    def generate(self, text: str, is_query: bool = False) -> np.ndarray:
        if not text.strip():
            logger.warning("⚠️ Empty text provided, returning zero vector")
            return np.zeros(self.actual_dimension)
        return self._generate_embedding(text, is_query=is_query)

    def generate_and_store_embeddings(self, documents: List[Dict[str, Any]]) -> None:
        if not documents:
            logger.warning("⚠️ No documents provided for embedding")
            return
        texts = [doc["content"] for doc in documents if doc.get("content")]
        payloads = [doc for doc in documents if doc.get("content")]
        if not texts:
            logger.warning("⚠️ No valid document texts found")
            return
        logger.info(f"🧠 Generating embeddings for {len(texts)} documents")
        embeddings = self.generate_embeddings(texts, is_query=False)
        points = [
            PointStruct(id=str(uuid4()), vector=vec.tolist(), payload=payload)
            for vec, payload in zip(embeddings, payloads)
        ]
        logger.info(f"📡 Pushing {len(points)} vectors to Qdrant Cloud")
        self.qdrant_client.upsert(
            collection_name=QDRANT_COLLECTION_NAME,
            points=points
        )

    def generate_query_embedding(self, query: str) -> np.ndarray:
        # Ensure model is loaded (lazy loading)
        if not self._model_loaded:
            self._initialize_model()
        
        if DEV_LOGGING['decorative_banners']:
            logger.info("=" * 80)
            logger.info("🔍 EMBEDDING GENERATOR - GENERATE_QUERY_EMBEDDING STARTED")
            logger.info("=" * 80)
            logger.info(f"📝 Query: '{query[:100]}...'")
            logger.info(f"📏 Query length: {len(query)} characters")
            logger.info(f"🧠 Model type: {getattr(self, 'model_type', 'Not loaded')}")
            logger.info(f"💻 Device: {self.device}")
            logger.info("=" * 80)
        
        try:
            result = self._generate_embedding(query, is_query=True)
            
            if DEV_LOGGING['decorative_banners']:
                logger.info("=" * 80)
                logger.info("🎉 EMBEDDING GENERATOR - GENERATE_QUERY_EMBEDDING COMPLETED SUCCESSFULLY")
                logger.info("=" * 80)
                logger.info(f"📝 Query: '{query[:100]}...'")
                logger.info(f"📊 Output shape: {result.shape}")
                logger.info(f"📊 Output type: {type(result)}")
                logger.info(f"📊 Output dtype: {result.dtype if hasattr(result, 'dtype') else 'N/A'}")
                logger.info("=" * 80)
            
            return result
            
        except Exception as e:
            if DEV_LOGGING['decorative_banners']:
                logger.error("=" * 80)
                logger.error("❌ EMBEDDING GENERATOR - GENERATE_QUERY_EMBEDDING FAILED")
                logger.error("=" * 80)
                logger.error(f"📝 Query: '{query[:100]}...'")
                logger.error(f"❌ Error type: {type(e).__name__}")
                logger.error(f"❌ Error message: {str(e)}")
                logger.error(f"🔍 Traceback: {traceback.format_exc()}")
                logger.error("=" * 80)
            else:
                logger.error(f"Query embedding generation failed: {e}")
            
            raise

    def get_model_info(self) -> Dict[str, Any]:
        return {
            "model_name": self.model_name,
            "model_type": getattr(self, 'model_type', 'Not loaded'),
            "device": str(self.device),
            "embedding_dimension": self.actual_dimension,
            "config_dimension": VECTOR_DIMENSION,
            "dimension_match": self.actual_dimension == VECTOR_DIMENSION,
            "model_loaded": self._model_loaded,
            "dimension_detected": self._dimension_detected,
        }
