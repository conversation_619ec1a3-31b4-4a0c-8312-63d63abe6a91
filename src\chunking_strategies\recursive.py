import re
from typing import List, Dict, Any, Optional
from .base import ChunkingStrategy, ChunkingConfig, TokenizerManager
from ..utils.logger import get_logger

logger = get_logger(__name__)

class RecursiveChunkingStrategy(ChunkingStrategy):
    """Recursive chunking strategy using LangChain's RecursiveCharacterTextSplitter."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def chunk_text(self, text: str, config: ChunkingConfig, tokenizer_manager: TokenizerManager) -> List[str]:
        if not text.strip():
            return []
        
        try:
            from langchain.text_splitter import RecursiveCharacterTextSplitter
            
            # Configure the splitter
            splitter = RecursiveCharacterTextSplitter(
                chunk_size=config.chunk_size,
                chunk_overlap=config.chunk_overlap,
                length_function=lambda x: tokenizer_manager.count_tokens(x, config.tokenizer_name) if config.use_tokens else len(x),
                separators=["\n\n", "\n", ". ", "! ", "? ", " ", ""]
            )
            
            chunks = splitter.split_text(text)
            return [chunk.strip() for chunk in chunks if chunk.strip()]
            
        except ImportError:
            self.logger.warning("LangChain not available, falling back to token-aware strategy")
            from .token_aware import TokenAwareStrategy
            return TokenAwareStrategy().chunk_text(text, config, tokenizer_manager)
        except Exception as e:
            self.logger.warning(f"LangChain splitter failed: {e}")
            from .token_aware import TokenAwareStrategy
            return TokenAwareStrategy().chunk_text(text, config, tokenizer_manager)

    def get_name(self) -> str:
        return "recursive" 