"""
Admin routes for the HR Assistant Chatbot.
Handles administrative functions, user management, and system administration.
"""
import json
import time
import os
from datetime import datetime, timedelta
from functools import wraps
from flask import Blueprint, request, jsonify, session
from typing import Dict, Any, List, Optional

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from src.database.user_db import ConversationModel
from src.database.session_db import SessionModel
from src.config import MOCKAPI_URL

logger = get_logger(__name__)

# Create blueprint
admin_bp = Blueprint('admin', __name__)


def admin_required(f):
    """Decorator for admin-only endpoints."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Simple admin check using query parameter
        admin_secret = request.args.get('admin_secret')
        if admin_secret != os.getenv('ADMIN_BACKFILL_SECRET', 'changeme'):
            raise APIError(
                message="Unauthorized - Admin access required",
                status_code=403
            )
        return f(*args, **kwargs)
    return decorated_function


@admin_bp.route('/hr-representatives', methods=["GET"])
def get_hr_representatives():
    """
    Get list of HR representatives.
    Returns mock HR representative data.
    """
    try:
        hr_reps = [
            {
                "id": "hr1",
                "name": "John Smith",
                "email": "<EMAIL>",
                "department": "HR Operations"
            },
            {
                "id": "hr2",
                "name": "Sarah Johnson",
                "email": "<EMAIL>",
                "department": "Employee Relations"
            },
            {
                "id": "hr3",
                "name": "Michael Brown",
                "email": "<EMAIL>",
                "department": "Benefits"
            }
        ]
        
        return jsonify({
            "success": True,
            "representatives": hr_reps
        })
        
    except Exception as e:
        logger.exception(f"Get HR representatives failed: {e}")
        raise APIError(
            message=f"Failed to get HR representatives: {str(e)}",
            status_code=500
        )


@admin_bp.route('/leave-balance', methods=["GET"])
def get_leave_balance():
    """
    Get leave balance for an employee.
    Fetches data from external API based on email and employee ID.
    """
    try:
        email = request.args.get("email")
        employee_id = request.args.get("employee_id")
        user_id = session.get("user_id")
        
        logger.debug(f"Leave balance request: user_id={user_id}, email={email}, employee_id={employee_id}")
        
        if not email or not employee_id:
            # Try to get from session user_id
            if user_id:
                service_manager = ServiceManager.get_instance()
                auth_service = service_manager.get_auth_service()
                user = auth_service.user_model.get_user_by_id(user_id)
                
                if user:
                    email = email or user.get("email")
                    employee_id = employee_id or user.get("employee_id")
        
        if not email or not employee_id:
            raise APIError(
                message="Email and employee ID are required. Please complete your info in Settings.",
                status_code=400
            )
        
        try:
            url = f"{MOCKAPI_URL}?employee_id={employee_id}&email={email}"
            resp = requests.get(url, timeout=10)
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Handle array response from mockapi
                if isinstance(data, list) and data:
                    data = data[0]
                
                if not data or not any(k in data for k in ("paid_leave", "sick_leave", "casual_leave")):
                    raise APIError(
                        message="We couldn't find your leave records. Please check your info in Settings and enter correct information.",
                        status_code=404
                    )
                
                # Create chatbot-friendly response
                response_msg = (
                    f"Hi {data.get('name', '')}, your leave balance is: "
                    f"Paid Leave: {data.get('paid_leave', 0)}, "
                    f"Sick Leave: {data.get('sick_leave', 0)}, "
                    f"Casual Leave: {data.get('casual_leave', 0)}."
                )
                
                return jsonify({
                    "success": True,
                    "response": response_msg,
                    "paid_leave": data.get("paid_leave", 0),
                    "sick_leave": data.get("sick_leave", 0),
                    "casual_leave": data.get("casual_leave", 0),
                    "name": data.get("name", "")
                })
            else:
                raise APIError(
                    message="We couldn't find your leave records. Please check your info in Settings and enter correct information.",
                    status_code=404
                )
                
        except requests.RequestException as e:
            logger.exception(f"Leave balance API request failed: {e}")
            raise APIError(
                message="Something went wrong while fetching your leave data. Try again later.",
                status_code=500,
                details=str(e)
            )
            
    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Leave balance failed: {e}")
        raise APIError(
            message=f"Leave balance request failed: {str(e)}",
            status_code=500
        )


@admin_bp.route('/payslips', methods=['GET'])
def get_payslip():
    """
    Get payslip download URL for an employee.
    Fetches payslip information from external service.
    """
    try:
        employee_id = request.args.get('employee_id')
        email = request.args.get('email')
        month = request.args.get('month')
        year = request.args.get('year')
        
        if not all([employee_id, email, month, year]):
            raise APIError(
                message="All parameters (employee_id, email, month, year) are required",
                status_code=400
            )

        base_url = os.getenv("SALARY_SLIP_URL")
        if not base_url:
            raise APIError(
                message="SALARY_SLIP_URL not configured",
                status_code=500
            )

        def normalize_month(val):
            try:
                return str(int(val))
            except Exception:
                return str(val).strip().lower()

        try:
            resp = requests.get(base_url, timeout=10)
            if resp.status_code == 200:
                payslips = resp.json()
                req_month_str = normalize_month(month)
                req_month_name = str(month).strip().lower()
                
                for slip in payslips:
                    slip_month_str = normalize_month(slip.get("month"))
                    slip_month_name = str(slip.get("month_name", "")).strip().lower()
                    
                    if (
                        str(slip.get("employee_id")) == str(employee_id)
                        and slip.get("email", "").lower() == email.lower()
                        and str(slip.get("year")) == str(year)
                        and (
                            slip_month_str == req_month_str
                            or slip_month_name == req_month_name
                        )
                    ):
                        if "download_url" in slip:
                            return jsonify({
                                "success": True,
                                "download_url": slip["download_url"]
                            })
                
                raise APIError(
                    message="Payslip not found",
                    status_code=404
                )
            else:
                raise APIError(
                    message="Payslip not found",
                    status_code=404
                )
                
        except requests.RequestException as e:
            logger.exception(f"Payslip API request failed: {e}")
            raise APIError(
                message=f"Payslip request failed: {str(e)}",
                status_code=500
            )
            
    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Get payslip failed: {e}")
        raise APIError(
            message=f"Payslip request failed: {str(e)}",
            status_code=500
        )


@admin_bp.route('/roles', methods=['GET'])
def get_roles():
    """
    Get available user roles.
    Returns mock role data for demo purposes.
    """
    try:
        roles = [
            {"id": 1, "name": "Superadmin", "level": 3},
            {"id": 2, "name": "Admin", "level": 2},
            {"id": 3, "name": "Viewer", "level": 1}
        ]
        
        return jsonify(roles)
        
    except Exception as e:
        logger.exception(f"Get roles failed: {e}")
        raise APIError(
            message=f"Failed to get roles: {str(e)}",
            status_code=500
        )


@admin_bp.route('/user/metrics', methods=['GET'])
def get_user_metrics():
    """
    Get user-specific metrics like total chats, average response time, etc.
    Returns personalized usage statistics.
    """
    try:
        user_id = session.get('user_id')
        if not user_id:
            raise APIError(
                message="User not authenticated",
                status_code=401
            )

        service_manager = ServiceManager.get_instance()
        user_model = service_manager.get_user_model()
        
        user = user_model.get_user_by_id(user_id)
        if not user:
            raise APIError(
                message="User not found",
                status_code=404
            )

        conv_model = ConversationModel()
        
        # Get user-specific metrics
        total_chats = conv_model.get_chat_message_count_by_user(user_id)
        avg_response_time = conv_model.get_average_response_time_by_user(user_id)
        total_documents = conv_model.get_total_documents_processed_by_user(user_id)

        return jsonify({
            "success": True,
            "total_chats": total_chats,
            "average_response_time": avg_response_time,
            "total_documents": total_documents
        })
        
    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Get user metrics failed: {e}")
        raise APIError(
            message=f"Failed to get user metrics: {str(e)}",
            status_code=500
        )


@admin_bp.route('/all-chats', methods=['GET'])
def get_all_chats():
    """
    Get all chatbot conversations with optional filters.
    Supports filtering by device_id, date range, and pagination.
    """
    try:
        device_id = request.args.get('device_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        try:
            limit = int(request.args.get('limit', 100))
            offset = int(request.args.get('offset', 0))
        except ValueError:
            limit = 100
            offset = 0

        from src.database.conversation_store import ConversationStore
        store = ConversationStore()
        conversations = store.get_all_conversations(
            device_id=device_id,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            offset=offset
        )

        return jsonify(conversations)

    except Exception as e:
        logger.exception(f"Get all chats failed: {e}")
        raise APIError(
            message=f"Failed to get chat conversations: {str(e)}",
            status_code=500
        )


@admin_bp.route('/chat-trends', methods=['GET'])
def chat_trends():
    """
    Get daily chat counts for the last 30 days.
    Returns trend data for analytics dashboard.
    """
    try:
        conv_model = ConversationModel()
        today = datetime.now().date()
        days = [(today - timedelta(days=i)) for i in range(29, -1, -1)]
        day_strs = [d.strftime('%Y-%m-%d') for d in days]

        chats_per_day = Counter()
        all_convs = conv_model.get_conversations(limit=50000)

        for c in all_convs:
            dt = c.get('query_timestamp', '')[:10]
            if dt in day_strs:
                chats_per_day[dt] += 1

        data = [{"date": d, "count": chats_per_day[d]} for d in day_strs]
        return jsonify(data)

    except Exception as e:
        logger.exception(f"Chat trends failed: {e}")
        raise APIError(
            message=f"Failed to get chat trends: {str(e)}",
            status_code=500
        )


@admin_bp.route('/chat-types', methods=['GET'])
def chat_types():
    """
    Get sentiment/type distribution.
    Returns distribution of conversation types/languages.
    """
    try:
        conv_model = ConversationModel()
        all_convs = conv_model.get_conversations(limit=10000)
        type_counter = Counter(c.get('language', 'en') for c in all_convs)
        data = [{"name": k, "value": v} for k, v in type_counter.items()]

        return jsonify(data)

    except Exception as e:
        logger.exception(f"Chat types failed: {e}")
        raise APIError(
            message=f"Failed to get chat types: {str(e)}",
            status_code=500
        )


@admin_bp.route('/queries', methods=['GET'])
def queries():
    """
    Get top user questions.
    Returns most frequently asked questions with metadata.
    """
    try:
        conv_model = ConversationModel()
        all_convs = conv_model.get_conversations(limit=10000)

        question_counter = Counter(c.get('user_query', '') for c in all_convs)
        last_asked = {}
        intent_map = {}

        for c in all_convs:
            q = c.get('user_query', '')
            last_asked[q] = c.get('query_timestamp', '')
            intent_map[q] = c.get('intent') or q.split()[0] if q else 'unknown'

        top = question_counter.most_common(10)
        data = [{
            "question": q,
            "count": count,
            "lastAsked": last_asked[q],
            "intent": intent_map[q]
        } for q, count in top]

        return jsonify(data)

    except Exception as e:
        logger.exception(f"Queries failed: {e}")
        raise APIError(
            message=f"Failed to get queries: {str(e)}",
            status_code=500
        )


@admin_bp.route('/chat-analytics/live', methods=['GET'])
def chat_analytics_live():
    """
    Get comprehensive live chat analytics.
    Returns detailed analytics including sentiment, intents, and performance metrics.
    """
    try:
        # Get session and conversation data
        session_model = SessionModel()
        active_sessions = session_model.get_active_sessions(user_type="chatbot_user")
        active_users = len(set(s['user_id'] for s in active_sessions if s['user_id']))

        conv_model = ConversationModel()
        all_convs = conv_model.get_conversations(limit=50000)

        now = datetime.now()
        last_30_days = [(now - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(29, -1, -1)]

        # Initialize counters
        chats_per_day = Counter()
        unique_questions = set()
        total_queries = 0
        total_duration = 0
        duration_count = 0
        feedbacks = []
        response_times = defaultdict(list)
        sentiment_counter = Counter()
        intent_counter = Counter()
        intent_samples = defaultdict(list)
        trending_by_day = {d: Counter() for d in last_30_days}
        question_counter = Counter()

        # Process conversations
        for c in all_convs:
            dt = c.get('query_timestamp', '')[:10]
            if dt in last_30_days:
                chats_per_day[dt] += 1

            total_queries += 1
            user_query = c.get('user_query', '').strip()
            unique_questions.add(user_query.lower())
            question_counter[user_query] += 1

            # Calculate duration
            try:
                start = datetime.fromisoformat(c['query_timestamp'])
                end = datetime.fromisoformat(c['response_timestamp'])
                duration = (end - start).total_seconds()
                total_duration += duration
                duration_count += 1
                response_times[dt].append(duration)
            except Exception:
                pass

            # Process feedback
            if c.get('feedback') in ('like', 'dislike'):
                feedbacks.append(1 if c['feedback'] == 'like' else 0)

            # Sentiment analysis
            sentiment = c.get('sentiment')
            if not sentiment:
                sentiment = simple_sentiment(user_query or c.get('assistant_response', ''))
            sentiment_counter[sentiment] += 1

            # Intent processing
            intent = c.get('intent') or 'unknown'
            intent_counter[intent] += 1
            if len(intent_samples[intent]) < 3 and user_query not in intent_samples[intent]:
                intent_samples[intent].append(user_query)

            # Trending topics
            if dt in last_30_days:
                trending_by_day[dt][intent] += 1

        # Calculate metrics
        avg_chat_duration = (total_duration / duration_count) if duration_count else 0
        user_satisfaction = (sum(feedbacks) / len(feedbacks)) if feedbacks else None

        # Response time metrics per day
        resp_time_metrics = []
        for day in last_30_days:
            times = response_times[day]
            if times:
                avg = sum(times) / len(times)
                p95 = sorted(times)[int(0.95 * len(times)) - 1] if len(times) >= 20 else max(times)
                p99 = sorted(times)[int(0.99 * len(times)) - 1] if len(times) >= 100 else max(times)
                resp_time_metrics.append({
                    "timestamp": day,
                    "avg_response_time": avg,
                    "p95_response_time": p95,
                    "p99_response_time": p99
                })
            else:
                resp_time_metrics.append({
                    "timestamp": day,
                    "avg_response_time": 0,
                    "p95_response_time": 0,
                    "p99_response_time": 0
                })

        # Sentiment distribution
        total_sentiments = sum(sentiment_counter.values())
        sentiment_distribution = [
            {
                "sentiment": k,
                "count": v,
                "percentage": round(100 * v / total_sentiments, 1) if total_sentiments else 0
            }
            for k, v in sentiment_counter.items()
        ]

        # Ensure all sentiments are present
        for s in ["positive", "neutral", "negative"]:
            if not any(d["sentiment"] == s for d in sentiment_distribution):
                sentiment_distribution.append({"sentiment": s, "count": 0, "percentage": 0})
        sentiment_distribution.sort(key=lambda x: ["positive", "neutral", "negative"].index(x["sentiment"]))

        # Top intents
        top_intents = []
        for intent, count in intent_counter.most_common(5):
            top_intents.append({
                "intent": intent,
                "count": count,
                "samples": intent_samples[intent]
            })

        # Trending topics
        trending_topics = []
        top_trend_intents = [i for i, _ in intent_counter.most_common(3)]
        for intent in top_trend_intents:
            trend = [trending_by_day[day][intent] for day in last_30_days]
            trending_topics.append({"topic": intent, "trend": trend})

        # Top questions
        top_questions = []
        for q, count in question_counter.most_common(10):
            top_questions.append({"question": q, "count": count})

        # Compose final response
        result = {
            "total_queries": total_queries,
            "active_users": active_users,
            "avg_sentiment": (sentiment_counter["positive"] - sentiment_counter["negative"]) / total_queries if total_queries else 0,
            "unique_questions": len(unique_questions),
            "chats_per_day": [chats_per_day[day] for day in last_30_days],
            "avg_chat_duration": avg_chat_duration,
            "resolution_rate": 0.95,  # Example value
            "escalation_rate": 0.05,  # Example value
            "user_satisfaction": user_satisfaction,
            "top_intents": top_intents,
            "trending_topics": trending_topics,
            "sentiment_distribution": sentiment_distribution,
            "top_questions": top_questions,
            "response_times": resp_time_metrics,
            "error_rate": 0.01,
            "uptime_percentage": 100
        }

        return jsonify(result)

    except Exception as e:
        logger.exception(f"Live analytics failed: {e}")
        raise APIError(
            message=f"Failed to get live analytics: {str(e)}",
            status_code=500
        )


@admin_bp.route('/admin/backfill-intents', methods=['POST'])
@admin_required
def backfill_intents():
    """
    One-time endpoint to backfill missing intents in conversations.
    Requires admin authentication for access.
    """
    try:
        from src.intent.intent_classifier import IntentClassifier

        conv_model = ConversationModel()
        batch_size = 100
        updated = 0
        skipped = 0
        processed = 0
        total = 0

        classifier = IntentClassifier()

        # Fetch all conversations with missing/empty intent
        all_missing = [
            c for c in conv_model.get_conversations(limit=50000)
            if not c.get('intent') or c.get('intent') == ''
        ]
        total = len(all_missing)

        for i in range(0, total, batch_size):
            batch = all_missing[i:i+batch_size]
            for c in batch:
                convo_id = c.get('id')
                user_query = c.get('user_query', '')

                if not user_query:
                    skipped += 1
                    continue

                intent = classifier.classify_intent(user_query).get('intent', 'unknown')

                # Only update if intent is missing
                if not c.get('intent') or c.get('intent') == '':
                    try:
                        with conv_model.db._get_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute(
                                'UPDATE conversations SET intent = ? WHERE id = ?',
                                (intent, convo_id)
                            )
                            conn.commit()
                        updated += 1
                    except Exception as e:
                        logger.error(f"Failed to update conversation {convo_id}: {e}")
                        skipped += 1
                else:
                    skipped += 1
                processed += 1

            logger.info(f"Backfill progress: {processed}/{total} (updated: {updated}, skipped: {skipped})")

        return jsonify({
            'total_missing': total,
            'processed': processed,
            'updated': updated,
            'skipped': skipped
        })

    except Exception as e:
        logger.exception(f"Intent backfill failed: {e}")
        raise APIError(
            message=f"Intent backfill failed: {str(e)}",
            status_code=500
        )


def simple_sentiment(text):
    """Basic sentiment analysis using keyword matching."""
    if not text:
        return "neutral"

    text = text.lower()
    positive_words = [
        "good", "great", "excellent", "happy", "love", "awesome",
        "fantastic", "thanks", "thank you", "helpful", "appreciate"
    ]
    negative_words = [
        "bad", "terrible", "hate", "awful", "worst", "problem",
        "issue", "not happy", "disappointed", "angry", "useless", "frustrated"
    ]

    for w in positive_words:
        if w in text:
            return "positive"
    for w in negative_words:
        if w in text:
            return "negative"
    return "neutral"
