"""
Authentication routes for the HR Assistant Chatbot.
Handles user registration, login, logout, 2FA, and profile management.
"""
import base64
import qrcode
import jwt
from io import BytesIO
from datetime import datetime
from flask import Blueprint, request, jsonify, session

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from src.database.session_db import SessionModel

logger = get_logger(__name__)

# Create blueprint
auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/register', methods=['POST', 'OPTIONS'])
def register():
    """
    Register a new user with 2FA setup.
    Creates user account and generates 2FA secrets for both user and admin roles.
    """
    try:
        if request.method == "OPTIONS":
            return ('', 204)
            
        data = request.json
        email = data.get("email")
        password = data.get("password")
        full_name = data.get("full_name")
        employee_id = data.get("employee_id")

        if not all([email, password, full_name]):
            raise APIError(
                message="Email, password, and full name are required",
                status_code=400
            )

        service_manager = ServiceManager.get_instance()
        auth_service = service_manager.get_auth_service()
        
        # Generate both secrets for new user
        user_2fa_secret = auth_service.generate_2fa_secret()
        admin_2fa_secret = auth_service.generate_2fa_secret()

        try:
            password_hash = auth_service.hash_password(password)
        except ValueError as ve:
            raise APIError(message=str(ve), status_code=400)

        result = auth_service.user_model.create_user(
            email=email,
            password_hash=password_hash,
            full_name=full_name,
            employee_id=employee_id,
            two_fa_secret_user=user_2fa_secret,
            two_fa_secret_admin=admin_2fa_secret,
            role='user'
        )

        if not result.get('success'):
            raise APIError(
                message=result.get('message', 'Failed to register user'),
                status_code=400
            )

        # Return both QR URLs for setup
        user_qr = auth_service.get_2fa_qr_url(email, user_2fa_secret, role='user')
        admin_qr = auth_service.get_2fa_qr_url(email, admin_2fa_secret, role='admin')
        
        return jsonify({
            "success": True,
            "message": "User registered successfully",
            "2fa_qr_url_user": user_qr,
            "2fa_qr_url_admin": admin_qr
        })
        
    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Registration failed: {e}")
        raise APIError(
            message=f"Registration failed: {str(e)}",
            status_code=500
        )


@auth_bp.route('/user-2fa-setup', methods=["POST"])
def user_2fa_setup():
    """
    Setup 2FA for a user and return QR code image.
    Generates QR code as base64 data URL for frontend display.
    """
    try:
        data = request.json
        email = data.get("email")
        
        if not email:
            raise APIError(message="Email is required", status_code=400)
            
        service_manager = ServiceManager.get_instance()
        user_model = service_manager.get_user_model()
        auth_service = service_manager.get_auth_service()
        
        user = user_model.get_user_by_email(email)
        if not user:
            raise APIError(message="User not found", status_code=404)
            
        secret = user.get("two_fa_secret_user")
        if not secret:
            # Generate and save a new secret if needed
            secret = auth_service.generate_2fa_secret()
            # Update user in database with new secret
            # Note: This would require implementing an update method
            
        qr_url = auth_service.get_2fa_qr_url(email, secret, role='user')

        # Generate QR code image as data URL
        qr_img = qrcode.make(qr_url)
        buffered = BytesIO()
        qr_img.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
        data_url = f"data:image/png;base64,{img_str}"

        return jsonify({"success": True, "qr_url": data_url})
        
    except APIError:
        raise
    except Exception as e:
        logger.exception(f"2FA setup failed: {e}")
        raise APIError(
            message=f"2FA setup failed: {str(e)}",
            status_code=500
        )


@auth_bp.route('/verify-2fa', methods=["POST"])
def verify_2fa():
    """
    Verify 2FA code for pending login.
    Completes the login process after successful 2FA verification.
    """
    try:
        data = request.json
        code = data.get("code")

        if not code:
            raise APIError(message="2FA code is required", status_code=400)

        # Get pending login from session
        user_id = session.get("pending_user_id")
        if not user_id:
            raise APIError(message="No pending login found", status_code=400)

        service_manager = ServiceManager.get_instance()
        auth_service = service_manager.get_auth_service()
        
        user = auth_service.user_model.get_user_by_id(user_id)
        if not user:
            raise APIError(message="User not found", status_code=404)

        # Determine role based on user object
        user_role = user.get('role', 'user')

        # Verify 2FA code
        if not auth_service.verify_2fa_code(user, code, role=user_role):
            raise APIError(message="Invalid 2FA code", status_code=401)

        # 2FA verification successful - complete the login
        auth_service.user_model.update_last_login(user['id'])
        token = auth_service.generate_token(user)

        # Set session data
        session["user_id"] = user["id"]
        session["email"] = user["email"]
        session.pop("pending_user_id", None)  # Clear pending login
        logger.info(f"2FA verification successful for user {user['id']}")

        # Create session record
        try:
            session_model = SessionModel()
            import uuid
            session_model.create_session(
                id=str(uuid.uuid4()),
                user_id=user['id'],
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent'),
                browser=None,
                os=None,
                device_fingerprint=None,
                login_time=datetime.utcnow().isoformat(),
                last_activity=datetime.utcnow().isoformat(),
                location_country=None,
                location_city=None,
                latitude=None,
                longitude=None,
                auth_method="password_2fa",
                success=True,
                user_type="chatbot_user"
            )
        except Exception as e:
            logger.exception("Failed to create session record")

        return jsonify({
            "success": True,
            "message": "2FA verification successful",
            "token": token,
            "user": {
                "id": user['id'],
                "email": user['email'],
                "full_name": user.get('full_name', ''),
                "employee_id": user.get('employee_id', ''),
            }
        }), 200

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"2FA verification failed: {e}")
        raise APIError(
            message=f"2FA verification failed: {str(e)}",
            status_code=500
        )


@auth_bp.route('/login', methods=["POST"])
def login():
    """
    User login with optional 2FA.
    Initiates login process and may require 2FA verification.
    """
    try:
        data = request.json
        email = data.get("email")
        password = data.get("password")
        two_fa_code = data.get("two_fa_code")
        
        if not email or not password:
            raise APIError(
                message="Email and password are required",
                status_code=400
            )
            
        service_manager = ServiceManager.get_instance()
        auth_service = service_manager.get_auth_service()
        
        result = auth_service.login_user(email, password, two_fa_code)
        
        if result.get("success"):
            session["user_id"] = result["user"]["id"]
            session["email"] = result["user"]["email"]
            
            response = jsonify(result)
            # Set refresh token as HttpOnly cookie
            response.set_cookie(
                'refresh_token',
                result['refresh_token'],
                httponly=True,
                secure=False,  # Set to True in production with HTTPS
                samesite='Lax',
                max_age=7 * 24 * 60 * 60  # 7 days
            )
            return response, 200
        else:
            # If 2FA required, store user ID for pending verification
            if result.get("message") == "2FA code required":
                user_id_from_result = result.get("user_id")
                if user_id_from_result:
                    session["pending_user_id"] = user_id_from_result
                return jsonify(result), 401
            return jsonify(result), 401
            
    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Login failed: {e}")
        raise APIError(
            message=f"Login failed: {str(e)}",
            status_code=500
        )


@auth_bp.route('/refresh-token', methods=['POST'])
def refresh_token():
    """
    Refresh access token using refresh token from HttpOnly cookie.
    Provides new access token for continued authentication.
    """
    try:
        refresh_token = request.cookies.get('refresh_token')
        if not refresh_token:
            raise APIError(
                message="No refresh token found",
                status_code=401,
                error_type="NO_REFRESH_TOKEN"
            )

        service_manager = ServiceManager.get_instance()
        auth_service = service_manager.get_auth_service()

        user = auth_service.verify_refresh_token(refresh_token)

        if not user:
            raise APIError(
                message="Invalid refresh token",
                status_code=401,
                error_type="INVALID_REFRESH_TOKEN"
            )

        # Generate new access token
        new_access_token = auth_service.generate_access_token(user)

        return jsonify({
            "success": True,
            "message": "Token refreshed successfully",
            "access_token": new_access_token
        }), 200

    except jwt.ExpiredSignatureError:
        raise APIError(
            message="Refresh token has expired. Please log in again.",
            status_code=401,
            error_type="REFRESH_TOKEN_EXPIRED"
        )
    except jwt.InvalidTokenError:
        raise APIError(
            message="Invalid refresh token",
            status_code=401,
            error_type="INVALID_REFRESH_TOKEN"
        )
    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Token refresh failed: {e}")
        raise APIError(
            message="Token refresh failed",
            status_code=500,
            error_type="REFRESH_ERROR"
        )


@auth_bp.route('/logout', methods=['POST'])
def logout():
    """
    User logout.
    Clears session and refresh token cookie.
    """
    try:
        session.clear()
        response = jsonify({"success": True, "message": "Logged out successfully"})
        # Clear refresh token cookie
        response.delete_cookie('refresh_token', path='/')
        return response

    except Exception as e:
        logger.exception(f"Logout failed: {e}")
        raise APIError(
            message=f"Logout failed: {str(e)}",
            status_code=500
        )


@auth_bp.route('/user', methods=["GET"])
def get_user():
    """
    Get the current user's information.
    Returns user profile data for authenticated user.
    """
    try:
        user_id = session.get("user_id")
        if not user_id:
            raise APIError(
                message="Not authenticated",
                status_code=401
            )

        service_manager = ServiceManager.get_instance()
        auth_service = service_manager.get_auth_service()

        user = auth_service.user_model.get_user_by_id(user_id)
        if not user:
            session.clear()
            raise APIError(
                message="User not found",
                status_code=404
            )

        return jsonify({
            "success": True,
            "user": {
                "id": user["id"],
                "email": user["email"],
                "full_name": user["full_name"],
                "company_name": user.get("company_name", ""),
                "employee_id": user.get("employee_id", "")
            }
        }), 200

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Get user failed: {e}")
        raise APIError(
            message=f"Failed to get user information: {str(e)}",
            status_code=500
        )


@auth_bp.route('/user/profile', methods=["GET", "PUT"])
def user_profile():
    """
    Get or update user profile information.
    Supports both JWT token and session-based authentication.
    """
    try:
        service_manager = ServiceManager.get_instance()
        auth_service = service_manager.get_auth_service()
        user_model = service_manager.get_user_model()

        user_id = None

        # JWT authentication support
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ', 1)[1]
            try:
                user = auth_service.verify_access_token(token)
                if user:
                    user_id = user.get('id')
            except jwt.ExpiredSignatureError:
                raise APIError(
                    message="Access token has expired. Please refresh.",
                    status_code=401,
                    error_type="ACCESS_TOKEN_EXPIRED"
                )
            except jwt.InvalidTokenError:
                raise APIError(
                    message="Invalid access token. Please log in again.",
                    status_code=401,
                    error_type="INVALID_ACCESS_TOKEN"
                )

        # Session fallback
        if not user_id:
            user_id = session.get('user_id')

        if not user_id:
            raise APIError(
                message="Authentication required. Please log in.",
                status_code=401,
                error_type="AUTH_REQUIRED"
            )

        if request.method == "GET":
            user = user_model.get_user_by_id(user_id)
            if not user:
                raise APIError(
                    message="User not found",
                    status_code=404
                )

            return jsonify({
                "success": True,
                "profile": {
                    "fullName": user.get("full_name", ""),
                    "email": user.get("email", ""),
                    "employeeId": user.get("employee_id", "")
                }
            })

        elif request.method == "PUT":
            data = request.json
            full_name = data.get("full_name")
            email = data.get("email")
            employee_id = data.get("employee_id")

            # Update user in database
            user = user_model.get_user_by_id(user_id)
            if not user:
                raise APIError(
                    message="User not found",
                    status_code=404
                )

            try:
                with user_model.db._get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        UPDATE users SET full_name = ?, email = ?, employee_id = ? WHERE id = ?
                    ''', (full_name, email, employee_id, user_id))
                    conn.commit()

                return jsonify({
                    "success": True,
                    "message": "Profile updated successfully"
                })

            except Exception as e:
                logger.exception(f"Profile update failed: {e}")
                raise APIError(
                    message=f"Failed to update profile: {str(e)}",
                    status_code=500
                )

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Profile operation failed: {e}")
        raise APIError(
            message=f"Profile operation failed: {str(e)}",
            status_code=500
        )
