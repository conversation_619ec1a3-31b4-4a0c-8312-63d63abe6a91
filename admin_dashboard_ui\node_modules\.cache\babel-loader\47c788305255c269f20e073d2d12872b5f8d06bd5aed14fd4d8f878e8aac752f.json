{"ast": null, "code": "import axios, { InternalAxiosRequestConfig } from \"axios\";\nimport { API_BASE_URL } from \"../apiConfig\";\nimport { useAuthStore } from \"../hooks/useAuthStore\";\nimport toast from \"react-hot-toast\";\n\n// Extend AxiosRequestConfig to allow 'metadata' property\n\n// ============================================================================\n// AXIOS INSTANCE CONFIGURATION\n// ============================================================================\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  withCredentials: true,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// ============================================================================\n// REQUEST INTERCEPTOR - Add JWT Token\n// ============================================================================\n\napi.interceptors.request.use(config => {\n  const token = useAuthStore.getState().token;\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n\n  // Add request timestamp for performance monitoring\n  config.metadata = {\n    startTime: new Date()\n  };\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// ============================================================================\n// RESPONSE INTERCEPTOR - Handle Errors & Token Refresh\n// ============================================================================\n\napi.interceptors.response.use(response => {\n  // Calculate request duration\n  const endTime = new Date();\n  const duration = response.config.metadata && response.config.metadata.startTime ? endTime.getTime() - response.config.metadata.startTime.getTime() : 0;\n\n  // Log slow requests (> 2 seconds)\n  if (duration > 2000) {\n    console.warn(`Slow API request: ${response.config.url} took ${duration}ms`);\n  }\n  return response;\n}, async error => {\n  var _error$response, _error$response2, _error$response3, _error$response4, _error$response5;\n  const originalRequest = error.config;\n\n  // Handle 401 Unauthorized - Token expired\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n    originalRequest._retry = true;\n    try {\n      // Attempt to refresh token\n      const refreshResponse = await api.post('/auth/refresh');\n      const newToken = refreshResponse.data.access_token;\n\n      // Update token in store\n      useAuthStore.getState().setToken(newToken);\n\n      // Retry original request with new token\n      originalRequest.headers.Authorization = `Bearer ${newToken}`;\n      return api(originalRequest);\n    } catch (refreshError) {\n      // Refresh failed, redirect to login\n      useAuthStore.getState().logout();\n      window.location.href = '/login';\n      return Promise.reject(refreshError);\n    }\n  }\n\n  // Handle different error types\n  if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 403) {\n    toast.error('Access denied. You do not have permission to perform this action.');\n  } else if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 429) {\n    toast.error('Rate limit exceeded. Please try again later.');\n  } else if (typeof ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status) === 'number' && ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status) >= 500) {\n    toast.error('Server error. Please try again later.');\n  } else if (error.code === 'ECONNABORTED') {\n    toast.error('Request timeout. Please check your connection.');\n  } else if (!error.response) {\n    toast.error('Network error. Please check your connection.');\n  }\n  return Promise.reject(error);\n});\n\n// ============================================================================\n// GENERIC API HELPERS\n// ============================================================================\n\nexport const get = (url, config) => api.get(url, config);\nexport const post = (url, data, config) => api.post(url, data, config);\nexport const put = (url, data, config) => api.put(url, data, config);\nexport const patch = (url, data, config) => api.patch(url, data, config);\nexport const del = (url, config) => api.delete(url, config);\n\n// ============================================================================\n// AUTHENTICATION API\n// ============================================================================\n\nexport const authApi = {\n  // COMMENTED OUT: OTP functionality temporarily disabled\n  // requestOtp: (email: string) =>\n  //   post('/api/auth/request-otp', { email }),\n\n  // login: (email: string, otp: string) =>\n  //   post('/admin/verify-otp', { email, otp }),\n\n  // NEW: 2FA-based authentication\n  check2FAStatus: email => post('/admin/check-2fa-status', {\n    email\n  }),\n  request2FASetup: email => post('/admin/request-2fa-setup', {\n    email\n  }),\n  verify2FA: (email, totpCode) => post('/admin/verify-2fa', {\n    email,\n    totp: totpCode\n  }),\n  logout: () => post('/auth/logout'),\n  refreshToken: () => post('/auth/refresh'),\n  enable2FA: () => post('/auth/2fa/enable'),\n  disable2FA: otp => post('/auth/2fa/disable', {\n    otp\n  }),\n  generateBackupCodes: () => post('/auth/backup-codes/generate'),\n  verifyBackupCode: code => post('/auth/backup-codes/verify', {\n    code\n  })\n};\n\n// ============================================================================\n// USER MANAGEMENT API\n// ============================================================================\n\nexport const userApi = {\n  getCurrentUser: () => get('/api/user'),\n  updateProfile: data => patch('/api/user/profile', data),\n  updatePreferences: preferences => patch('/api/user/preferences', preferences),\n  changePassword: (currentPassword, newPassword) => post('/api/user/change-password', {\n    current_password: currentPassword,\n    new_password: newPassword\n  }),\n  uploadAvatar: file => {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    return post('/api/user/avatar', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  }\n};\n\n// ============================================================================\n// ADMIN MANAGEMENT API\n// ============================================================================\n\nexport const adminApi = {\n  getUsers: params => get('/admin/users', {\n    params\n  }),\n  createUser: userData => post('/admin/users', userData),\n  updateUser: (userId, userData) => patch(`/admin/users/${userId}`, userData),\n  deleteUser: userId => del(`/admin/users/${userId}`),\n  changeRole: (email, newRole) => post('/admin/change-role', {\n    email,\n    role: newRole\n  }),\n  inviteUser: (email, role, organizationId) => post('/admin/invite', {\n    email,\n    role,\n    organization_id: organizationId\n  }),\n  revokeInvitation: invitationId => del(`/admin/invitations/${invitationId}`),\n  getInvitations: () => get('/admin/invitations')\n};\n\n// ============================================================================\n// CHATBOT METRICS & ANALYTICS API\n// ============================================================================\n\nexport const metricsApi = {\n  getDashboardMetrics: timeRange => get('/api/chat-analytics/live', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getChatbotMetrics: timeRange => get('/api/metrics/chatbot', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getPerformanceMetrics: timeRange => get('/api/metrics/performance', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getUserEngagement: timeRange => get('/api/metrics/engagement', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getSentimentAnalysis: timeRange => get('/api/metrics/sentiment', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getIntentMetrics: timeRange => get('/api/metrics/intents', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getAnomalies: (severity, timeRange) => get('/api/sessions/anomalies', {\n    params: {\n      severity,\n      range: timeRange\n    }\n  }),\n  acknowledgeAnomaly: (anomalyId, notes) => post(`/api/metrics/anomalies/${anomalyId}/acknowledge`, {\n    notes\n  }),\n  // Real-time live data endpoints\n  getLiveSessions: () => get('/api/sessions/live'),\n  getActiveEscalations: () => get('/api/escalations/pending'),\n  getRealTimeFeedback: timeRange => get('/api/feedback/trends', {\n    params: {\n      range: timeRange\n    }\n  }),\n  getTopQuestions: (timeRange, limit) => get('/api/analytics/top-questions', {\n    params: {\n      range: timeRange,\n      limit\n    }\n  }),\n  getResponseTimes: timeRange => get('/api/analytics/response-times', {\n    params: {\n      range: timeRange\n    }\n  }),\n  getSystemHealth: () => get('/api/metrics/system-health'),\n  getModelPerformance: timeRange => get('/api/metrics/model-performance', {\n    params: {\n      time_range: timeRange\n    }\n  })\n};\n\n// ============================================================================\n// CHAT LOGS & SESSIONS API\n// ============================================================================\n\nexport const chatApi = {\n  getAllChats: params => get('/api/all-chats', {\n    params\n  }),\n  getChatLogs: params => get('/api/chatlogs', {\n    params\n  }),\n  // Use proxy endpoint\n  getChatSession: sessionId => get(`/api/chat-sessions/${sessionId}`),\n  getChatMessages: sessionId => get(`/api/chat-sessions/${sessionId}/messages`),\n  getQueries: params => get('/api/queries', {\n    params\n  }),\n  getChatTrends: timeRange => get('/api/chat-trends', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getChatTypes: () => get('/api/chat-types'),\n  escalateChat: (sessionId, reason, priority) => post(`/api/chat-sessions/${sessionId}/escalate`, {\n    reason,\n    priority\n  }),\n  resolveChat: (sessionId, resolution) => post(`/api/chat-sessions/${sessionId}/resolve`, {\n    resolution\n  }),\n  addChatNote: (sessionId, note) => post(`/api/chat-sessions/${sessionId}/notes`, {\n    note\n  })\n};\n\n// ============================================================================\n// AUDIT & COMPLIANCE API\n// ============================================================================\n\nexport const auditApi = {\n  getAuditLogs: params => get('/audit/logs', {\n    params\n  }),\n  getDeviceLogs: params => get('/devices/logs', {\n    params\n  }),\n  getSessions: params => get('/sessions', {\n    params\n  }),\n  revokeSession: sessionData => post('/sessions/revoke', sessionData),\n  getComplianceReport: (type, timeRange) => get(`/api/compliance/${type}`, {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  requestDataDeletion: (userId, reason) => post('/api/compliance/data-deletion', {\n    user_id: userId,\n    reason\n  }),\n  exportUserData: (userId, format) => post('/api/compliance/export-user-data', {\n    user_id: userId,\n    format\n  }),\n  getGDPRRequests: () => get('/api/compliance/gdpr-requests')\n};\n\n// ============================================================================\n// FEATURE REQUESTS & FEEDBACK API\n// ============================================================================\n\nexport const feedbackApi = {\n  getFeatureRequests: params => get('/api/feature-requests', {\n    params\n  }),\n  createFeatureRequest: requestData => post('/api/feature-requests', requestData),\n  updateFeatureRequest: (requestId, updateData) => patch(`/api/feature-requests/${requestId}`, updateData),\n  voteFeatureRequest: requestId => post(`/api/feature-requests/${requestId}/vote`),\n  addComment: (requestId, comment) => post(`/api/feature-requests/${requestId}/comments`, {\n    comment\n  }),\n  getFeedbackTrends: timeRange => get('/api/feedback/trends', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getEscalatedFeedback: () => get('/api/feedback/escalated'),\n  getResolutionMetrics: () => get('/api/feedback/resolution')\n};\n\n// ============================================================================\n// API KEYS & WEBHOOKS API\n// ============================================================================\n\nexport const integrationApi = {\n  getApiKeys: () => get('/api/integrations/api-keys'),\n  createApiKey: keyData => post('/api/integrations/api-keys', keyData),\n  revokeApiKey: keyId => del(`/api/integrations/api-keys/${keyId}`),\n  getApiKeyUsage: keyId => get(`/api/integrations/api-keys/${keyId}/usage`),\n  getWebhooks: () => get('/api/integrations/webhooks'),\n  createWebhook: webhookData => post('/api/integrations/webhooks', webhookData),\n  updateWebhook: (webhookId, webhookData) => patch(`/api/integrations/webhooks/${webhookId}`, webhookData),\n  deleteWebhook: webhookId => del(`/api/integrations/webhooks/${webhookId}`),\n  testWebhook: webhookId => post(`/api/integrations/webhooks/${webhookId}/test`),\n  getWebhookDeliveries: webhookId => get(`/api/integrations/webhooks/${webhookId}/deliveries`)\n};\n\n// ============================================================================\n// SYSTEM HEALTH & MONITORING API\n// ============================================================================\n\nexport const systemApi = {\n  getSystemHealth: () => get('/api/system/health'),\n  getPerformanceMetrics: timeRange => get('/api/system/performance', {\n    params: {\n      time_range: timeRange\n    }\n  }),\n  getServiceStatus: () => get('/api/system/services'),\n  getDocumentHealth: () => get('/api/health/documents'),\n  getUptime: () => get('/api/system/uptime'),\n  getErrorLogs: params => get('/api/system/errors', {\n    params\n  }),\n  getSlowQueries: () => get('/api/system/slow-queries'),\n  clearCache: cacheType => post('/api/system/cache/clear', {\n    cache_type: cacheType\n  })\n};\n\n// ============================================================================\n// EXPORT & REPORTING API\n// ============================================================================\n\nexport const exportApi = {\n  requestExport: exportData => post('/api/exports', exportData),\n  getExportStatus: exportId => get(`/api/exports/${exportId}`),\n  downloadExport: exportId => get(`/api/exports/${exportId}/download`, {\n    responseType: 'blob'\n  }),\n  getReports: () => get('/api/reports'),\n  createReport: reportData => post('/api/reports', reportData),\n  updateReport: (reportId, reportData) => patch(`/api/reports/${reportId}`, reportData),\n  deleteReport: reportId => del(`/api/reports/${reportId}`),\n  generateReport: reportId => post(`/api/reports/${reportId}/generate`)\n};\n\n// ============================================================================\n// LEGACY API ENDPOINTS (for backward compatibility)\n// ============================================================================\n\nexport const legacyApi = {\n  getStatsUsers: () => get('/stats/users'),\n  getStatsUsage: () => get('/stats/usage'),\n  getStatsIntents: () => get('/stats/intents'),\n  getOverrides: () => get('/api/overrides'),\n  getHRRepresentatives: () => get('/api/hr-representatives')\n};\n\n// ============================================================================\n// EXPORT DEFAULT API INSTANCE\n// ============================================================================\n\nexport default api;\n\n// Add missing API exports for dashboard\nexport const getChatLogs = chatApi.getChatLogs;\nexport const getChatTrends = chatApi.getChatTrends;\nexport const getAuditLogs = auditApi.getAuditLogs;\nexport const getDeviceLogs = auditApi.getDeviceLogs;\nexport const getStatsUsers = legacyApi.getStatsUsers;\nexport const getStatsUsage = legacyApi.getStatsUsage;\nexport const getStatsIntents = legacyApi.getStatsIntents;\nexport const changeRole = adminApi.changeRole;", "map": {"version": 3, "names": ["axios", "InternalAxiosRequestConfig", "API_BASE_URL", "useAuthStore", "toast", "api", "create", "baseURL", "timeout", "withCredentials", "headers", "interceptors", "request", "use", "config", "token", "getState", "Authorization", "metadata", "startTime", "Date", "error", "Promise", "reject", "response", "endTime", "duration", "getTime", "console", "warn", "url", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response5", "originalRequest", "status", "_retry", "refreshResponse", "post", "newToken", "data", "access_token", "setToken", "refreshError", "logout", "window", "location", "href", "code", "get", "put", "patch", "del", "delete", "authApi", "check2FAStatus", "email", "request2FASetup", "verify2FA", "totpCode", "totp", "refreshToken", "enable2FA", "disable2FA", "otp", "generateBackupCodes", "verifyBackupCode", "userApi", "getCurrentUser", "updateProfile", "updatePreferences", "preferences", "changePassword", "currentPassword", "newPassword", "current_password", "new_password", "uploadAvatar", "file", "formData", "FormData", "append", "adminApi", "getUsers", "params", "createUser", "userData", "updateUser", "userId", "deleteUser", "changeRole", "newRole", "role", "inviteUser", "organizationId", "organization_id", "revokeInvitation", "invitationId", "getInvitations", "metricsApi", "getDashboardMetrics", "timeRange", "time_range", "getChatbotMetrics", "getPerformanceMetrics", "getUserEngagement", "getSentimentAnalysis", "getIntentMetrics", "getAnomalies", "severity", "range", "acknowledgeAnomaly", "anomalyId", "notes", "getLiveSessions", "getActiveEscalations", "getRealTimeFeedback", "getTopQuestions", "limit", "getResponseTimes", "getSystemHealth", "getModelPerformance", "chatApi", "getAllChats", "getChatLogs", "getChatSession", "sessionId", "getChatMessages", "getQueries", "getChatTrends", "getChatTypes", "escalateChat", "reason", "priority", "resolveChat", "resolution", "addChatNote", "note", "auditApi", "getAuditLogs", "getDeviceLogs", "getSessions", "revokeSession", "sessionData", "getComplianceReport", "type", "requestDataDeletion", "user_id", "exportUserData", "format", "getGDPRRequests", "feedbackApi", "getFeatureRequests", "createFeatureRequest", "requestData", "updateFeatureRequest", "requestId", "updateData", "voteFeatureRequest", "addComment", "comment", "getFeedbackTrends", "getEscalatedFeedback", "getResolutionMetrics", "integrationApi", "getApi<PERSON>eys", "createApiKey", "keyData", "revokeApiKey", "keyId", "getApiKeyUsage", "getWebhooks", "createWebhook", "webhookData", "updateWebhook", "webhookId", "deleteWebhook", "testWebhook", "getWebhookDeliveries", "systemApi", "getServiceStatus", "getDocumentHealth", "getUptime", "getErrorLogs", "getSlowQueries", "clearCache", "cacheType", "cache_type", "exportApi", "requestExport", "exportData", "getExportStatus", "exportId", "downloadExport", "responseType", "getReports", "createReport", "reportData", "updateReport", "reportId", "deleteReport", "generateReport", "legacyApi", "getStatsUsers", "getStatsUsage", "getStatsIntents", "getOverrides", "getHRRepresentatives"], "sources": ["C:/Generative AI Projects/Multi-Model RAG Chatbot-react/admin_dashboard_ui/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from \"axios\";\nimport { API_BASE_URL } from \"../apiConfig\";\nimport { useAuthStore } from \"../hooks/useAuthStore\";\nimport toast from \"react-hot-toast\";\n\n// Extend AxiosRequestConfig to allow 'metadata' property\ndeclare module 'axios' {\n  export interface InternalAxiosRequestConfig {\n    metadata?: { startTime: Date };\n  }\n}\n\n// ============================================================================\n// AXIOS INSTANCE CONFIGURATION\n// ============================================================================\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  withCredentials: true,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// ============================================================================\n// REQUEST INTERCEPTOR - Add JWT Token\n// ============================================================================\n\napi.interceptors.request.use(\n  (config) => {\n    const token = useAuthStore.getState().token;\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n\n    // Add request timestamp for performance monitoring\n    config.metadata = { startTime: new Date() };\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// ============================================================================\n// RESPONSE INTERCEPTOR - Handle Errors & Token Refresh\n// ============================================================================\n\napi.interceptors.response.use(\n  (response: AxiosResponse) => {\n    // Calculate request duration\n    const endTime = new Date();\n    const duration = response.config.metadata && response.config.metadata.startTime ? endTime.getTime() - response.config.metadata.startTime.getTime() : 0;\n\n    // Log slow requests (> 2 seconds)\n    if (duration > 2000) {\n      console.warn(`Slow API request: ${response.config.url} took ${duration}ms`);\n    }\n\n    return response;\n  },\n  async (error: AxiosError) => {\n    const originalRequest = error.config as any;\n\n    // Handle 401 Unauthorized - Token expired\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        // Attempt to refresh token\n        const refreshResponse = await api.post('/auth/refresh');\n        const newToken = refreshResponse.data.access_token;\n\n        // Update token in store\n        useAuthStore.getState().setToken(newToken);\n\n        // Retry original request with new token\n        originalRequest.headers.Authorization = `Bearer ${newToken}`;\n        return api(originalRequest);\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        useAuthStore.getState().logout();\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n\n    // Handle different error types\n    if (error.response?.status === 403) {\n      toast.error('Access denied. You do not have permission to perform this action.');\n    } else if (error.response?.status === 429) {\n      toast.error('Rate limit exceeded. Please try again later.');\n    } else if (typeof error.response?.status === 'number' && error.response?.status >= 500) {\n      toast.error('Server error. Please try again later.');\n    } else if (error.code === 'ECONNABORTED') {\n      toast.error('Request timeout. Please check your connection.');\n    } else if (!error.response) {\n      toast.error('Network error. Please check your connection.');\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// ============================================================================\n// GENERIC API HELPERS\n// ============================================================================\n\nexport const get = <T = any>(url: string, config?: any): Promise<AxiosResponse<T>> =>\n  api.get(url, config);\n\nexport const post = <T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> =>\n  api.post(url, data, config);\n\nexport const put = <T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> =>\n  api.put(url, data, config);\n\nexport const patch = <T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> =>\n  api.patch(url, data, config);\n\nexport const del = <T = any>(url: string, config?: any): Promise<AxiosResponse<T>> =>\n  api.delete(url, config);\n\n// ============================================================================\n// AUTHENTICATION API\n// ============================================================================\n\nexport const authApi = {\n  // COMMENTED OUT: OTP functionality temporarily disabled\n  // requestOtp: (email: string) =>\n  //   post('/api/auth/request-otp', { email }),\n\n  // login: (email: string, otp: string) =>\n  //   post('/admin/verify-otp', { email, otp }),\n\n  // NEW: 2FA-based authentication\n  check2FAStatus: (email: string) =>\n    post('/admin/check-2fa-status', { email }),\n\n  request2FASetup: (email: string) =>\n    post('/admin/request-2fa-setup', { email }),\n\n  verify2FA: (email: string, totpCode: string) =>\n    post('/admin/verify-2fa', { email, totp: totpCode }),\n\n  logout: () =>\n    post('/auth/logout'),\n\n  refreshToken: () =>\n    post('/auth/refresh'),\n\n  enable2FA: () =>\n    post('/auth/2fa/enable'),\n\n  disable2FA: (otp: string) =>\n    post('/auth/2fa/disable', { otp }),\n\n  generateBackupCodes: () =>\n    post('/auth/backup-codes/generate'),\n\n  verifyBackupCode: (code: string) =>\n    post('/auth/backup-codes/verify', { code }),\n};\n\n// ============================================================================\n// USER MANAGEMENT API\n// ============================================================================\n\nexport const userApi = {\n  getCurrentUser: () =>\n    get('/api/user'),\n\n  updateProfile: (data: any) =>\n    patch('/api/user/profile', data),\n\n  updatePreferences: (preferences: any) =>\n    patch('/api/user/preferences', preferences),\n\n  changePassword: (currentPassword: string, newPassword: string) =>\n    post('/api/user/change-password', { current_password: currentPassword, new_password: newPassword }),\n\n  uploadAvatar: (file: File) => {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    return post('/api/user/avatar', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    });\n  },\n};\n\n// ============================================================================\n// ADMIN MANAGEMENT API\n// ============================================================================\n\nexport const adminApi = {\n  getUsers: (params?: any) =>\n    get('/admin/users', { params }),\n\n  createUser: (userData: any) =>\n    post('/admin/users', userData),\n\n  updateUser: (userId: string, userData: any) =>\n    patch(`/admin/users/${userId}`, userData),\n\n  deleteUser: (userId: string) =>\n    del(`/admin/users/${userId}`),\n\n  changeRole: (email: string, newRole: string) =>\n    post('/admin/change-role', { email, role: newRole }),\n\n  inviteUser: (email: string, role: string, organizationId: string) =>\n    post('/admin/invite', { email, role, organization_id: organizationId }),\n\n  revokeInvitation: (invitationId: string) =>\n    del(`/admin/invitations/${invitationId}`),\n\n  getInvitations: () =>\n    get('/admin/invitations'),\n};\n\n// ============================================================================\n// CHATBOT METRICS & ANALYTICS API\n// ============================================================================\n\nexport const metricsApi = {\n  getDashboardMetrics: (timeRange?: string) =>\n    get('/api/chat-analytics/live', { params: { time_range: timeRange } }),\n  getChatbotMetrics: (timeRange?: string) =>\n    get('/api/metrics/chatbot', { params: { time_range: timeRange } }),\n  getPerformanceMetrics: (timeRange?: string) =>\n    get('/api/metrics/performance', { params: { time_range: timeRange } }),\n  getUserEngagement: (timeRange?: string) =>\n    get('/api/metrics/engagement', { params: { time_range: timeRange } }),\n  getSentimentAnalysis: (timeRange?: string) =>\n    get('/api/metrics/sentiment', { params: { time_range: timeRange } }),\n  getIntentMetrics: (timeRange?: string) =>\n    get('/api/metrics/intents', { params: { time_range: timeRange } }),\n  getAnomalies: (severity?: string, timeRange?: string) =>\n    get('/api/sessions/anomalies', { params: { severity, range: timeRange } }),\n  acknowledgeAnomaly: (anomalyId: string, notes?: string) =>\n    post(`/api/metrics/anomalies/${anomalyId}/acknowledge`, { notes }),\n  // Real-time live data endpoints\n  getLiveSessions: () =>\n    get('/api/sessions/live'),\n  getActiveEscalations: () =>\n    get('/api/escalations/pending'),\n  getRealTimeFeedback: (timeRange?: string) =>\n    get('/api/feedback/trends', { params: { range: timeRange } }),\n  getTopQuestions: (timeRange?: string, limit?: number) =>\n    get('/api/analytics/top-questions', { params: { range: timeRange, limit } }),\n  getResponseTimes: (timeRange?: string) =>\n    get('/api/analytics/response-times', { params: { range: timeRange } }),\n  getSystemHealth: () =>\n    get('/api/metrics/system-health'),\n  getModelPerformance: (timeRange?: string) =>\n    get('/api/metrics/model-performance', { params: { time_range: timeRange } }),\n};\n\n// ============================================================================\n// CHAT LOGS & SESSIONS API\n// ============================================================================\n\nexport const chatApi = {\n  getAllChats: (params?: any) =>\n    get('/api/all-chats', { params }),\n  getChatLogs: (params?: any) =>\n    get('/api/chatlogs', { params }), // Use proxy endpoint\n  getChatSession: (sessionId: string) =>\n    get(`/api/chat-sessions/${sessionId}`),\n  getChatMessages: (sessionId: string) =>\n    get(`/api/chat-sessions/${sessionId}/messages`),\n  getQueries: (params?: any) =>\n    get('/api/queries', { params }),\n  getChatTrends: (timeRange?: string) =>\n    get('/api/chat-trends', { params: { time_range: timeRange } }),\n  getChatTypes: () =>\n    get('/api/chat-types'),\n  escalateChat: (sessionId: string, reason: string, priority: string) =>\n    post(`/api/chat-sessions/${sessionId}/escalate`, { reason, priority }),\n  resolveChat: (sessionId: string, resolution: string) =>\n    post(`/api/chat-sessions/${sessionId}/resolve`, { resolution }),\n  addChatNote: (sessionId: string, note: string) =>\n    post(`/api/chat-sessions/${sessionId}/notes`, { note }),\n};\n\n// ============================================================================\n// AUDIT & COMPLIANCE API\n// ============================================================================\n\nexport const auditApi = {\n  getAuditLogs: (params?: any) =>\n    get('/audit/logs', { params }),\n\n  getDeviceLogs: (params?: any) =>\n    get('/devices/logs', { params }),\n\n  getSessions: (params?: any) =>\n    get('/sessions', { params }),\n\n  revokeSession: (sessionData: any) =>\n    post('/sessions/revoke', sessionData),\n\n  getComplianceReport: (type: string, timeRange?: string) =>\n    get(`/api/compliance/${type}`, { params: { time_range: timeRange } }),\n\n  requestDataDeletion: (userId: string, reason: string) =>\n    post('/api/compliance/data-deletion', { user_id: userId, reason }),\n\n  exportUserData: (userId: string, format: string) =>\n    post('/api/compliance/export-user-data', { user_id: userId, format }),\n\n  getGDPRRequests: () =>\n    get('/api/compliance/gdpr-requests'),\n};\n\n// ============================================================================\n// FEATURE REQUESTS & FEEDBACK API\n// ============================================================================\n\nexport const feedbackApi = {\n  getFeatureRequests: (params?: any) =>\n    get('/api/feature-requests', { params }),\n\n  createFeatureRequest: (requestData: any) =>\n    post('/api/feature-requests', requestData),\n\n  updateFeatureRequest: (requestId: string, updateData: any) =>\n    patch(`/api/feature-requests/${requestId}`, updateData),\n\n  voteFeatureRequest: (requestId: string) =>\n    post(`/api/feature-requests/${requestId}/vote`),\n\n  addComment: (requestId: string, comment: string) =>\n    post(`/api/feature-requests/${requestId}/comments`, { comment }),\n\n  getFeedbackTrends: (timeRange?: string) =>\n    get('/api/feedback/trends', { params: { time_range: timeRange } }),\n\n  getEscalatedFeedback: () =>\n    get('/api/feedback/escalated'),\n\n  getResolutionMetrics: () =>\n    get('/api/feedback/resolution'),\n};\n\n// ============================================================================\n// API KEYS & WEBHOOKS API\n// ============================================================================\n\nexport const integrationApi = {\n  getApiKeys: () =>\n    get('/api/integrations/api-keys'),\n\n  createApiKey: (keyData: any) =>\n    post('/api/integrations/api-keys', keyData),\n\n  revokeApiKey: (keyId: string) =>\n    del(`/api/integrations/api-keys/${keyId}`),\n\n  getApiKeyUsage: (keyId: string) =>\n    get(`/api/integrations/api-keys/${keyId}/usage`),\n\n  getWebhooks: () =>\n    get('/api/integrations/webhooks'),\n\n  createWebhook: (webhookData: any) =>\n    post('/api/integrations/webhooks', webhookData),\n\n  updateWebhook: (webhookId: string, webhookData: any) =>\n    patch(`/api/integrations/webhooks/${webhookId}`, webhookData),\n\n  deleteWebhook: (webhookId: string) =>\n    del(`/api/integrations/webhooks/${webhookId}`),\n\n  testWebhook: (webhookId: string) =>\n    post(`/api/integrations/webhooks/${webhookId}/test`),\n\n  getWebhookDeliveries: (webhookId: string) =>\n    get(`/api/integrations/webhooks/${webhookId}/deliveries`),\n};\n\n// ============================================================================\n// SYSTEM HEALTH & MONITORING API\n// ============================================================================\n\nexport const systemApi = {\n  getSystemHealth: () =>\n    get('/api/system/health'),\n\n  getPerformanceMetrics: (timeRange?: string) =>\n    get('/api/system/performance', { params: { time_range: timeRange } }),\n\n  getServiceStatus: () =>\n    get('/api/system/services'),\n\n  getDocumentHealth: () =>\n    get('/api/health/documents'),\n\n  getUptime: () =>\n    get('/api/system/uptime'),\n\n  getErrorLogs: (params?: any) =>\n    get('/api/system/errors', { params }),\n\n  getSlowQueries: () =>\n    get('/api/system/slow-queries'),\n\n  clearCache: (cacheType?: string) =>\n    post('/api/system/cache/clear', { cache_type: cacheType }),\n};\n\n// ============================================================================\n// EXPORT & REPORTING API\n// ============================================================================\n\nexport const exportApi = {\n  requestExport: (exportData: any) =>\n    post('/api/exports', exportData),\n\n  getExportStatus: (exportId: string) =>\n    get(`/api/exports/${exportId}`),\n\n  downloadExport: (exportId: string) =>\n    get(`/api/exports/${exportId}/download`, { responseType: 'blob' }),\n\n  getReports: () =>\n    get('/api/reports'),\n\n  createReport: (reportData: any) =>\n    post('/api/reports', reportData),\n\n  updateReport: (reportId: string, reportData: any) =>\n    patch(`/api/reports/${reportId}`, reportData),\n\n  deleteReport: (reportId: string) =>\n    del(`/api/reports/${reportId}`),\n\n  generateReport: (reportId: string) =>\n    post(`/api/reports/${reportId}/generate`),\n};\n\n// ============================================================================\n// LEGACY API ENDPOINTS (for backward compatibility)\n// ============================================================================\n\nexport const legacyApi = {\n  getStatsUsers: () => get('/stats/users'),\n  getStatsUsage: () => get('/stats/usage'),\n  getStatsIntents: () => get('/stats/intents'),\n  getOverrides: () => get('/api/overrides'),\n  getHRRepresentatives: () => get('/api/hr-representatives'),\n};\n\n// ============================================================================\n// EXPORT DEFAULT API INSTANCE\n// ============================================================================\n\nexport default api;\n\n// Add missing API exports for dashboard\nexport const getChatLogs = chatApi.getChatLogs;\nexport const getChatTrends = chatApi.getChatTrends;\nexport const getAuditLogs = auditApi.getAuditLogs;\nexport const getDeviceLogs = auditApi.getDeviceLogs;\nexport const getStatsUsers = legacyApi.getStatsUsers;\nexport const getStatsUsage = legacyApi.getStatsUsage;\nexport const getStatsIntents = legacyApi.getStatsIntents;\nexport const changeRole = adminApi.changeRole;"], "mappings": "AAAA,OAAOA,KAAK,IAA+BC,0BAA0B,QAAQ,OAAO;AACpF,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;;AAOA;AACA;AACA;;AAEA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEL,YAAY;EACrBM,OAAO,EAAE,KAAK;EACdC,eAAe,EAAE,IAAI;EACrBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA;AACA;;AAEAL,GAAG,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGZ,YAAY,CAACa,QAAQ,CAAC,CAAC,CAACD,KAAK;EAC3C,IAAIA,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACO,aAAa,GAAG,UAAUF,KAAK,EAAE;EAClD;;EAEA;EACAD,MAAM,CAACI,QAAQ,GAAG;IAAEC,SAAS,EAAE,IAAIC,IAAI,CAAC;EAAE,CAAC;EAE3C,OAAON,MAAM;AACf,CAAC,EACAO,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA;AACA;;AAEAhB,GAAG,CAACM,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAuB,IAAK;EAC3B;EACA,MAAMC,OAAO,GAAG,IAAIL,IAAI,CAAC,CAAC;EAC1B,MAAMM,QAAQ,GAAGF,QAAQ,CAACV,MAAM,CAACI,QAAQ,IAAIM,QAAQ,CAACV,MAAM,CAACI,QAAQ,CAACC,SAAS,GAAGM,OAAO,CAACE,OAAO,CAAC,CAAC,GAAGH,QAAQ,CAACV,MAAM,CAACI,QAAQ,CAACC,SAAS,CAACQ,OAAO,CAAC,CAAC,GAAG,CAAC;;EAEtJ;EACA,IAAID,QAAQ,GAAG,IAAI,EAAE;IACnBE,OAAO,CAACC,IAAI,CAAC,qBAAqBL,QAAQ,CAACV,MAAM,CAACgB,GAAG,SAASJ,QAAQ,IAAI,CAAC;EAC7E;EAEA,OAAOF,QAAQ;AACjB,CAAC,EACD,MAAOH,KAAiB,IAAK;EAAA,IAAAU,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;EAC3B,MAAMC,eAAe,GAAGf,KAAK,CAACP,MAAa;;EAE3C;EACA,IAAI,EAAAiB,eAAA,GAAAV,KAAK,CAACG,QAAQ,cAAAO,eAAA,uBAAdA,eAAA,CAAgBM,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;IAE7B,IAAI;MACF;MACA,MAAMC,eAAe,GAAG,MAAMlC,GAAG,CAACmC,IAAI,CAAC,eAAe,CAAC;MACvD,MAAMC,QAAQ,GAAGF,eAAe,CAACG,IAAI,CAACC,YAAY;;MAElD;MACAxC,YAAY,CAACa,QAAQ,CAAC,CAAC,CAAC4B,QAAQ,CAACH,QAAQ,CAAC;;MAE1C;MACAL,eAAe,CAAC1B,OAAO,CAACO,aAAa,GAAG,UAAUwB,QAAQ,EAAE;MAC5D,OAAOpC,GAAG,CAAC+B,eAAe,CAAC;IAC7B,CAAC,CAAC,OAAOS,YAAY,EAAE;MACrB;MACA1C,YAAY,CAACa,QAAQ,CAAC,CAAC,CAAC8B,MAAM,CAAC,CAAC;MAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,OAAO3B,OAAO,CAACC,MAAM,CAACsB,YAAY,CAAC;IACrC;EACF;;EAEA;EACA,IAAI,EAAAb,gBAAA,GAAAX,KAAK,CAACG,QAAQ,cAAAQ,gBAAA,uBAAdA,gBAAA,CAAgBK,MAAM,MAAK,GAAG,EAAE;IAClCjC,KAAK,CAACiB,KAAK,CAAC,mEAAmE,CAAC;EAClF,CAAC,MAAM,IAAI,EAAAY,gBAAA,GAAAZ,KAAK,CAACG,QAAQ,cAAAS,gBAAA,uBAAdA,gBAAA,CAAgBI,MAAM,MAAK,GAAG,EAAE;IACzCjC,KAAK,CAACiB,KAAK,CAAC,8CAA8C,CAAC;EAC7D,CAAC,MAAM,IAAI,SAAAa,gBAAA,GAAOb,KAAK,CAACG,QAAQ,cAAAU,gBAAA,uBAAdA,gBAAA,CAAgBG,MAAM,MAAK,QAAQ,IAAI,EAAAF,gBAAA,GAAAd,KAAK,CAACG,QAAQ,cAAAW,gBAAA,uBAAdA,gBAAA,CAAgBE,MAAM,KAAI,GAAG,EAAE;IACtFjC,KAAK,CAACiB,KAAK,CAAC,uCAAuC,CAAC;EACtD,CAAC,MAAM,IAAIA,KAAK,CAAC6B,IAAI,KAAK,cAAc,EAAE;IACxC9C,KAAK,CAACiB,KAAK,CAAC,gDAAgD,CAAC;EAC/D,CAAC,MAAM,IAAI,CAACA,KAAK,CAACG,QAAQ,EAAE;IAC1BpB,KAAK,CAACiB,KAAK,CAAC,8CAA8C,CAAC;EAC7D;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAM8B,GAAG,GAAGA,CAAUrB,GAAW,EAAEhB,MAAY,KACpDT,GAAG,CAAC8C,GAAG,CAACrB,GAAG,EAAEhB,MAAM,CAAC;AAEtB,OAAO,MAAM0B,IAAI,GAAGA,CAAUV,GAAW,EAAEY,IAAU,EAAE5B,MAAY,KACjET,GAAG,CAACmC,IAAI,CAACV,GAAG,EAAEY,IAAI,EAAE5B,MAAM,CAAC;AAE7B,OAAO,MAAMsC,GAAG,GAAGA,CAAUtB,GAAW,EAAEY,IAAU,EAAE5B,MAAY,KAChET,GAAG,CAAC+C,GAAG,CAACtB,GAAG,EAAEY,IAAI,EAAE5B,MAAM,CAAC;AAE5B,OAAO,MAAMuC,KAAK,GAAGA,CAAUvB,GAAW,EAAEY,IAAU,EAAE5B,MAAY,KAClET,GAAG,CAACgD,KAAK,CAACvB,GAAG,EAAEY,IAAI,EAAE5B,MAAM,CAAC;AAE9B,OAAO,MAAMwC,GAAG,GAAGA,CAAUxB,GAAW,EAAEhB,MAAY,KACpDT,GAAG,CAACkD,MAAM,CAACzB,GAAG,EAAEhB,MAAM,CAAC;;AAEzB;AACA;AACA;;AAEA,OAAO,MAAM0C,OAAO,GAAG;EACrB;EACA;EACA;;EAEA;EACA;;EAEA;EACAC,cAAc,EAAGC,KAAa,IAC5BlB,IAAI,CAAC,yBAAyB,EAAE;IAAEkB;EAAM,CAAC,CAAC;EAE5CC,eAAe,EAAGD,KAAa,IAC7BlB,IAAI,CAAC,0BAA0B,EAAE;IAAEkB;EAAM,CAAC,CAAC;EAE7CE,SAAS,EAAEA,CAACF,KAAa,EAAEG,QAAgB,KACzCrB,IAAI,CAAC,mBAAmB,EAAE;IAAEkB,KAAK;IAAEI,IAAI,EAAED;EAAS,CAAC,CAAC;EAEtDf,MAAM,EAAEA,CAAA,KACNN,IAAI,CAAC,cAAc,CAAC;EAEtBuB,YAAY,EAAEA,CAAA,KACZvB,IAAI,CAAC,eAAe,CAAC;EAEvBwB,SAAS,EAAEA,CAAA,KACTxB,IAAI,CAAC,kBAAkB,CAAC;EAE1ByB,UAAU,EAAGC,GAAW,IACtB1B,IAAI,CAAC,mBAAmB,EAAE;IAAE0B;EAAI,CAAC,CAAC;EAEpCC,mBAAmB,EAAEA,CAAA,KACnB3B,IAAI,CAAC,6BAA6B,CAAC;EAErC4B,gBAAgB,EAAGlB,IAAY,IAC7BV,IAAI,CAAC,2BAA2B,EAAE;IAAEU;EAAK,CAAC;AAC9C,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMmB,OAAO,GAAG;EACrBC,cAAc,EAAEA,CAAA,KACdnB,GAAG,CAAC,WAAW,CAAC;EAElBoB,aAAa,EAAG7B,IAAS,IACvBW,KAAK,CAAC,mBAAmB,EAAEX,IAAI,CAAC;EAElC8B,iBAAiB,EAAGC,WAAgB,IAClCpB,KAAK,CAAC,uBAAuB,EAAEoB,WAAW,CAAC;EAE7CC,cAAc,EAAEA,CAACC,eAAuB,EAAEC,WAAmB,KAC3DpC,IAAI,CAAC,2BAA2B,EAAE;IAAEqC,gBAAgB,EAAEF,eAAe;IAAEG,YAAY,EAAEF;EAAY,CAAC,CAAC;EAErGG,YAAY,EAAGC,IAAU,IAAK;IAC5B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,IAAI,CAAC;IAC/B,OAAOxC,IAAI,CAAC,kBAAkB,EAAEyC,QAAQ,EAAE;MACxCvE,OAAO,EAAE;QAAE,cAAc,EAAE;MAAsB;IACnD,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAM0E,QAAQ,GAAG;EACtBC,QAAQ,EAAGC,MAAY,IACrBnC,GAAG,CAAC,cAAc,EAAE;IAAEmC;EAAO,CAAC,CAAC;EAEjCC,UAAU,EAAGC,QAAa,IACxBhD,IAAI,CAAC,cAAc,EAAEgD,QAAQ,CAAC;EAEhCC,UAAU,EAAEA,CAACC,MAAc,EAAEF,QAAa,KACxCnC,KAAK,CAAC,gBAAgBqC,MAAM,EAAE,EAAEF,QAAQ,CAAC;EAE3CG,UAAU,EAAGD,MAAc,IACzBpC,GAAG,CAAC,gBAAgBoC,MAAM,EAAE,CAAC;EAE/BE,UAAU,EAAEA,CAAClC,KAAa,EAAEmC,OAAe,KACzCrD,IAAI,CAAC,oBAAoB,EAAE;IAAEkB,KAAK;IAAEoC,IAAI,EAAED;EAAQ,CAAC,CAAC;EAEtDE,UAAU,EAAEA,CAACrC,KAAa,EAAEoC,IAAY,EAAEE,cAAsB,KAC9DxD,IAAI,CAAC,eAAe,EAAE;IAAEkB,KAAK;IAAEoC,IAAI;IAAEG,eAAe,EAAED;EAAe,CAAC,CAAC;EAEzEE,gBAAgB,EAAGC,YAAoB,IACrC7C,GAAG,CAAC,sBAAsB6C,YAAY,EAAE,CAAC;EAE3CC,cAAc,EAAEA,CAAA,KACdjD,GAAG,CAAC,oBAAoB;AAC5B,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMkD,UAAU,GAAG;EACxBC,mBAAmB,EAAGC,SAAkB,IACtCpD,GAAG,CAAC,0BAA0B,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EACxEE,iBAAiB,EAAGF,SAAkB,IACpCpD,GAAG,CAAC,sBAAsB,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EACpEG,qBAAqB,EAAGH,SAAkB,IACxCpD,GAAG,CAAC,0BAA0B,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EACxEI,iBAAiB,EAAGJ,SAAkB,IACpCpD,GAAG,CAAC,yBAAyB,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EACvEK,oBAAoB,EAAGL,SAAkB,IACvCpD,GAAG,CAAC,wBAAwB,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EACtEM,gBAAgB,EAAGN,SAAkB,IACnCpD,GAAG,CAAC,sBAAsB,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EACpEO,YAAY,EAAEA,CAACC,QAAiB,EAAER,SAAkB,KAClDpD,GAAG,CAAC,yBAAyB,EAAE;IAAEmC,MAAM,EAAE;MAAEyB,QAAQ;MAAEC,KAAK,EAAET;IAAU;EAAE,CAAC,CAAC;EAC5EU,kBAAkB,EAAEA,CAACC,SAAiB,EAAEC,KAAc,KACpD3E,IAAI,CAAC,0BAA0B0E,SAAS,cAAc,EAAE;IAAEC;EAAM,CAAC,CAAC;EACpE;EACAC,eAAe,EAAEA,CAAA,KACfjE,GAAG,CAAC,oBAAoB,CAAC;EAC3BkE,oBAAoB,EAAEA,CAAA,KACpBlE,GAAG,CAAC,0BAA0B,CAAC;EACjCmE,mBAAmB,EAAGf,SAAkB,IACtCpD,GAAG,CAAC,sBAAsB,EAAE;IAAEmC,MAAM,EAAE;MAAE0B,KAAK,EAAET;IAAU;EAAE,CAAC,CAAC;EAC/DgB,eAAe,EAAEA,CAAChB,SAAkB,EAAEiB,KAAc,KAClDrE,GAAG,CAAC,8BAA8B,EAAE;IAAEmC,MAAM,EAAE;MAAE0B,KAAK,EAAET,SAAS;MAAEiB;IAAM;EAAE,CAAC,CAAC;EAC9EC,gBAAgB,EAAGlB,SAAkB,IACnCpD,GAAG,CAAC,+BAA+B,EAAE;IAAEmC,MAAM,EAAE;MAAE0B,KAAK,EAAET;IAAU;EAAE,CAAC,CAAC;EACxEmB,eAAe,EAAEA,CAAA,KACfvE,GAAG,CAAC,4BAA4B,CAAC;EACnCwE,mBAAmB,EAAGpB,SAAkB,IACtCpD,GAAG,CAAC,gCAAgC,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC;AAC/E,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMqB,OAAO,GAAG;EACrBC,WAAW,EAAGvC,MAAY,IACxBnC,GAAG,CAAC,gBAAgB,EAAE;IAAEmC;EAAO,CAAC,CAAC;EACnCwC,WAAW,EAAGxC,MAAY,IACxBnC,GAAG,CAAC,eAAe,EAAE;IAAEmC;EAAO,CAAC,CAAC;EAAE;EACpCyC,cAAc,EAAGC,SAAiB,IAChC7E,GAAG,CAAC,sBAAsB6E,SAAS,EAAE,CAAC;EACxCC,eAAe,EAAGD,SAAiB,IACjC7E,GAAG,CAAC,sBAAsB6E,SAAS,WAAW,CAAC;EACjDE,UAAU,EAAG5C,MAAY,IACvBnC,GAAG,CAAC,cAAc,EAAE;IAAEmC;EAAO,CAAC,CAAC;EACjC6C,aAAa,EAAG5B,SAAkB,IAChCpD,GAAG,CAAC,kBAAkB,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EAChE6B,YAAY,EAAEA,CAAA,KACZjF,GAAG,CAAC,iBAAiB,CAAC;EACxBkF,YAAY,EAAEA,CAACL,SAAiB,EAAEM,MAAc,EAAEC,QAAgB,KAChE/F,IAAI,CAAC,sBAAsBwF,SAAS,WAAW,EAAE;IAAEM,MAAM;IAAEC;EAAS,CAAC,CAAC;EACxEC,WAAW,EAAEA,CAACR,SAAiB,EAAES,UAAkB,KACjDjG,IAAI,CAAC,sBAAsBwF,SAAS,UAAU,EAAE;IAAES;EAAW,CAAC,CAAC;EACjEC,WAAW,EAAEA,CAACV,SAAiB,EAAEW,IAAY,KAC3CnG,IAAI,CAAC,sBAAsBwF,SAAS,QAAQ,EAAE;IAAEW;EAAK,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,YAAY,EAAGvD,MAAY,IACzBnC,GAAG,CAAC,aAAa,EAAE;IAAEmC;EAAO,CAAC,CAAC;EAEhCwD,aAAa,EAAGxD,MAAY,IAC1BnC,GAAG,CAAC,eAAe,EAAE;IAAEmC;EAAO,CAAC,CAAC;EAElCyD,WAAW,EAAGzD,MAAY,IACxBnC,GAAG,CAAC,WAAW,EAAE;IAAEmC;EAAO,CAAC,CAAC;EAE9B0D,aAAa,EAAGC,WAAgB,IAC9BzG,IAAI,CAAC,kBAAkB,EAAEyG,WAAW,CAAC;EAEvCC,mBAAmB,EAAEA,CAACC,IAAY,EAAE5C,SAAkB,KACpDpD,GAAG,CAAC,mBAAmBgG,IAAI,EAAE,EAAE;IAAE7D,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EAEvE6C,mBAAmB,EAAEA,CAAC1D,MAAc,EAAE4C,MAAc,KAClD9F,IAAI,CAAC,+BAA+B,EAAE;IAAE6G,OAAO,EAAE3D,MAAM;IAAE4C;EAAO,CAAC,CAAC;EAEpEgB,cAAc,EAAEA,CAAC5D,MAAc,EAAE6D,MAAc,KAC7C/G,IAAI,CAAC,kCAAkC,EAAE;IAAE6G,OAAO,EAAE3D,MAAM;IAAE6D;EAAO,CAAC,CAAC;EAEvEC,eAAe,EAAEA,CAAA,KACfrG,GAAG,CAAC,+BAA+B;AACvC,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMsG,WAAW,GAAG;EACzBC,kBAAkB,EAAGpE,MAAY,IAC/BnC,GAAG,CAAC,uBAAuB,EAAE;IAAEmC;EAAO,CAAC,CAAC;EAE1CqE,oBAAoB,EAAGC,WAAgB,IACrCpH,IAAI,CAAC,uBAAuB,EAAEoH,WAAW,CAAC;EAE5CC,oBAAoB,EAAEA,CAACC,SAAiB,EAAEC,UAAe,KACvD1G,KAAK,CAAC,yBAAyByG,SAAS,EAAE,EAAEC,UAAU,CAAC;EAEzDC,kBAAkB,EAAGF,SAAiB,IACpCtH,IAAI,CAAC,yBAAyBsH,SAAS,OAAO,CAAC;EAEjDG,UAAU,EAAEA,CAACH,SAAiB,EAAEI,OAAe,KAC7C1H,IAAI,CAAC,yBAAyBsH,SAAS,WAAW,EAAE;IAAEI;EAAQ,CAAC,CAAC;EAElEC,iBAAiB,EAAG5D,SAAkB,IACpCpD,GAAG,CAAC,sBAAsB,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EAEpE6D,oBAAoB,EAAEA,CAAA,KACpBjH,GAAG,CAAC,yBAAyB,CAAC;EAEhCkH,oBAAoB,EAAEA,CAAA,KACpBlH,GAAG,CAAC,0BAA0B;AAClC,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMmH,cAAc,GAAG;EAC5BC,UAAU,EAAEA,CAAA,KACVpH,GAAG,CAAC,4BAA4B,CAAC;EAEnCqH,YAAY,EAAGC,OAAY,IACzBjI,IAAI,CAAC,4BAA4B,EAAEiI,OAAO,CAAC;EAE7CC,YAAY,EAAGC,KAAa,IAC1BrH,GAAG,CAAC,8BAA8BqH,KAAK,EAAE,CAAC;EAE5CC,cAAc,EAAGD,KAAa,IAC5BxH,GAAG,CAAC,8BAA8BwH,KAAK,QAAQ,CAAC;EAElDE,WAAW,EAAEA,CAAA,KACX1H,GAAG,CAAC,4BAA4B,CAAC;EAEnC2H,aAAa,EAAGC,WAAgB,IAC9BvI,IAAI,CAAC,4BAA4B,EAAEuI,WAAW,CAAC;EAEjDC,aAAa,EAAEA,CAACC,SAAiB,EAAEF,WAAgB,KACjD1H,KAAK,CAAC,8BAA8B4H,SAAS,EAAE,EAAEF,WAAW,CAAC;EAE/DG,aAAa,EAAGD,SAAiB,IAC/B3H,GAAG,CAAC,8BAA8B2H,SAAS,EAAE,CAAC;EAEhDE,WAAW,EAAGF,SAAiB,IAC7BzI,IAAI,CAAC,8BAA8ByI,SAAS,OAAO,CAAC;EAEtDG,oBAAoB,EAAGH,SAAiB,IACtC9H,GAAG,CAAC,8BAA8B8H,SAAS,aAAa;AAC5D,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMI,SAAS,GAAG;EACvB3D,eAAe,EAAEA,CAAA,KACfvE,GAAG,CAAC,oBAAoB,CAAC;EAE3BuD,qBAAqB,EAAGH,SAAkB,IACxCpD,GAAG,CAAC,yBAAyB,EAAE;IAAEmC,MAAM,EAAE;MAAEkB,UAAU,EAAED;IAAU;EAAE,CAAC,CAAC;EAEvE+E,gBAAgB,EAAEA,CAAA,KAChBnI,GAAG,CAAC,sBAAsB,CAAC;EAE7BoI,iBAAiB,EAAEA,CAAA,KACjBpI,GAAG,CAAC,uBAAuB,CAAC;EAE9BqI,SAAS,EAAEA,CAAA,KACTrI,GAAG,CAAC,oBAAoB,CAAC;EAE3BsI,YAAY,EAAGnG,MAAY,IACzBnC,GAAG,CAAC,oBAAoB,EAAE;IAAEmC;EAAO,CAAC,CAAC;EAEvCoG,cAAc,EAAEA,CAAA,KACdvI,GAAG,CAAC,0BAA0B,CAAC;EAEjCwI,UAAU,EAAGC,SAAkB,IAC7BpJ,IAAI,CAAC,yBAAyB,EAAE;IAAEqJ,UAAU,EAAED;EAAU,CAAC;AAC7D,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAME,SAAS,GAAG;EACvBC,aAAa,EAAGC,UAAe,IAC7BxJ,IAAI,CAAC,cAAc,EAAEwJ,UAAU,CAAC;EAElCC,eAAe,EAAGC,QAAgB,IAChC/I,GAAG,CAAC,gBAAgB+I,QAAQ,EAAE,CAAC;EAEjCC,cAAc,EAAGD,QAAgB,IAC/B/I,GAAG,CAAC,gBAAgB+I,QAAQ,WAAW,EAAE;IAAEE,YAAY,EAAE;EAAO,CAAC,CAAC;EAEpEC,UAAU,EAAEA,CAAA,KACVlJ,GAAG,CAAC,cAAc,CAAC;EAErBmJ,YAAY,EAAGC,UAAe,IAC5B/J,IAAI,CAAC,cAAc,EAAE+J,UAAU,CAAC;EAElCC,YAAY,EAAEA,CAACC,QAAgB,EAAEF,UAAe,KAC9ClJ,KAAK,CAAC,gBAAgBoJ,QAAQ,EAAE,EAAEF,UAAU,CAAC;EAE/CG,YAAY,EAAGD,QAAgB,IAC7BnJ,GAAG,CAAC,gBAAgBmJ,QAAQ,EAAE,CAAC;EAEjCE,cAAc,EAAGF,QAAgB,IAC/BjK,IAAI,CAAC,gBAAgBiK,QAAQ,WAAW;AAC5C,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAMG,SAAS,GAAG;EACvBC,aAAa,EAAEA,CAAA,KAAM1J,GAAG,CAAC,cAAc,CAAC;EACxC2J,aAAa,EAAEA,CAAA,KAAM3J,GAAG,CAAC,cAAc,CAAC;EACxC4J,eAAe,EAAEA,CAAA,KAAM5J,GAAG,CAAC,gBAAgB,CAAC;EAC5C6J,YAAY,EAAEA,CAAA,KAAM7J,GAAG,CAAC,gBAAgB,CAAC;EACzC8J,oBAAoB,EAAEA,CAAA,KAAM9J,GAAG,CAAC,yBAAyB;AAC3D,CAAC;;AAED;AACA;AACA;;AAEA,eAAe9C,GAAG;;AAElB;AACA,OAAO,MAAMyH,WAAW,GAAGF,OAAO,CAACE,WAAW;AAC9C,OAAO,MAAMK,aAAa,GAAGP,OAAO,CAACO,aAAa;AAClD,OAAO,MAAMU,YAAY,GAAGD,QAAQ,CAACC,YAAY;AACjD,OAAO,MAAMC,aAAa,GAAGF,QAAQ,CAACE,aAAa;AACnD,OAAO,MAAM+D,aAAa,GAAGD,SAAS,CAACC,aAAa;AACpD,OAAO,MAAMC,aAAa,GAAGF,SAAS,CAACE,aAAa;AACpD,OAAO,MAAMC,eAAe,GAAGH,SAAS,CAACG,eAAe;AACxD,OAAO,MAAMnH,UAAU,GAAGR,QAAQ,CAACQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}