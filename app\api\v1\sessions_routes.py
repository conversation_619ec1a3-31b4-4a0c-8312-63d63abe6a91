"""
Sessions and device intelligence API routes for admin dashboard.
Provides live session monitoring, historical logs, and geographic data.
"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from typing import Dict, Any, List, Optional
import random

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from src.database.session_db import SessionModel
from src.database.user_db import UserModel

logger = get_logger(__name__)

# Create blueprint
sessions_v1_bp = Blueprint('sessions_v1', __name__)

def get_date_range(time_range: str = "7d") -> tuple:
    """Get start and end dates based on time range parameter."""
    end_date = datetime.now()
    
    if time_range == "1d":
        start_date = end_date - timedelta(days=1)
    elif time_range == "7d":
        start_date = end_date - timedelta(days=7)
    elif time_range == "30d":
        start_date = end_date - timedelta(days=30)
    elif time_range == "90d":
        start_date = end_date - timedelta(days=90)
    else:
        start_date = end_date - timedelta(days=7)
    
    return start_date, end_date

@sessions_v1_bp.route('/live', methods=['GET'])
def get_live_sessions():
    """
    Get currently active sessions for live monitoring.
    Shows real-time user activity and session details.
    """
    try:
        user_type = request.args.get('user_type', 'admin')
        limit = int(request.args.get('limit', 50))
        
        session_model = SessionModel()
        
        # Get recent sessions (active in last 30 minutes)
        recent_sessions = session_model.get_recent_sessions(limit=limit * 2)  # Get more to filter
        
        # Filter for active sessions (last activity within 30 minutes)
        active_sessions = []
        cutoff_time = datetime.now() - timedelta(minutes=30)
        
        for session in recent_sessions:
            if session.get('last_activity'):
                try:
                    last_activity = datetime.fromisoformat(session['last_activity'].replace('Z', '+00:00'))
                    if last_activity > cutoff_time:
                        # Enhance session data
                        enhanced_session = {
                            "id": session.get('id', ''),
                            "user_id": session.get('user_id', ''),
                            "ip_address": session.get('ip_address', ''),
                            "user_agent": session.get('user_agent', ''),
                            "browser": session.get('browser', 'Unknown'),
                            "os": session.get('os', 'Unknown'),
                            "device_fingerprint": session.get('device_fingerprint', ''),
                            "login_time": session.get('login_time', ''),
                            "last_activity": session.get('last_activity', ''),
                            "location_country": session.get('location_country', 'Unknown'),
                            "location_city": session.get('location_city', 'Unknown'),
                            "latitude": session.get('latitude'),
                            "longitude": session.get('longitude'),
                            "auth_method": session.get('auth_method', 'unknown'),
                            "user_type": session.get('user_type', 'admin'),
                            "status": "active",
                            "duration_minutes": int((datetime.now() - datetime.fromisoformat(session.get('login_time', datetime.now().isoformat()).replace('Z', '+00:00'))).total_seconds() / 60),
                            "activity_level": random.choice(['high', 'medium', 'low']),  # Mock activity level
                            "pages_visited": random.randint(5, 50),  # Mock page visits
                            "last_page": random.choice(['/dashboard', '/analytics', '/users', '/settings']),  # Mock last page
                            "risk_score": random.randint(0, 100),  # Mock security risk score
                            "is_suspicious": random.choice([True, False]) if random.random() < 0.1 else False  # 10% chance of suspicious activity
                        }
                        
                        # Filter by user type if specified
                        if user_type == 'all' or enhanced_session['user_type'] == user_type:
                            active_sessions.append(enhanced_session)
                            
                except (ValueError, TypeError):
                    continue
        
        # Sort by last activity (most recent first)
        active_sessions.sort(key=lambda x: x['last_activity'], reverse=True)
        
        # Limit results
        active_sessions = active_sessions[:limit]
        
        # Calculate summary statistics
        total_active = len(active_sessions)
        unique_countries = len(set(s['location_country'] for s in active_sessions if s['location_country'] != 'Unknown'))
        suspicious_sessions = len([s for s in active_sessions if s['is_suspicious']])
        avg_duration = sum(s['duration_minutes'] for s in active_sessions) / len(active_sessions) if active_sessions else 0
        
        response_data = {
            "sessions": active_sessions,
            "summary": {
                "total_active_sessions": total_active,
                "unique_countries": unique_countries,
                "suspicious_sessions": suspicious_sessions,
                "avg_session_duration_minutes": round(avg_duration, 1),
                "peak_activity_hour": datetime.now().hour,  # Mock peak hour
                "auth_methods": {
                    method: len([s for s in active_sessions if s['auth_method'] == method])
                    for method in set(s['auth_method'] for s in active_sessions)
                }
            },
            "alerts": [
                {
                    "type": "security",
                    "severity": "medium",
                    "message": f"Suspicious activity detected in {suspicious_sessions} sessions",
                    "timestamp": datetime.now().isoformat()
                }
            ] if suspicious_sessions > 0 else [],
            "user_type": user_type,
            "generated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Retrieved {total_active} live sessions for user_type: {user_type}")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting live sessions: {e}")
        raise APIError(
            message="Failed to retrieve live sessions",
            status_code=500,
            details=str(e)
        )

@sessions_v1_bp.route('/historical', methods=['GET'])
def get_historical_sessions():
    """
    Get historical session data with filtering and pagination.
    Provides detailed session logs for analysis and auditing.
    """
    try:
        # Get query parameters
        time_range = request.args.get('time_range', '7d')
        user_type = request.args.get('user_type', 'admin')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        user_filter = request.args.get('user')
        ip_filter = request.args.get('ip')
        auth_method_filter = request.args.get('auth_method')
        result_filter = request.args.get('result')  # success, failed
        
        start_date, end_date = get_date_range(time_range)
        
        session_model = SessionModel()
        
        # Get sessions within date range
        all_sessions = session_model.get_recent_sessions(limit=5000)  # Get large dataset for filtering
        
        # Filter sessions
        filtered_sessions = []
        for session in all_sessions:
            # Date filter
            if session.get('login_time'):
                try:
                    login_time = datetime.fromisoformat(session['login_time'].replace('Z', '+00:00'))
                    if not (start_date <= login_time <= end_date):
                        continue
                except (ValueError, TypeError):
                    continue
            
            # User type filter
            if user_type != 'all' and session.get('user_type') != user_type:
                continue
            
            # User filter
            if user_filter and user_filter.lower() not in session.get('user_id', '').lower():
                continue
            
            # IP filter
            if ip_filter and ip_filter not in session.get('ip_address', ''):
                continue
            
            # Auth method filter
            if auth_method_filter and session.get('auth_method') != auth_method_filter:
                continue
            
            # Result filter (mock success/failure)
            session_success = session.get('success', True)
            if result_filter == 'success' and not session_success:
                continue
            if result_filter == 'failed' and session_success:
                continue
            
            # Enhance session data
            enhanced_session = {
                "id": session.get('id', ''),
                "user_id": session.get('user_id', ''),
                "ip_address": session.get('ip_address', ''),
                "user_agent": session.get('user_agent', ''),
                "browser": session.get('browser', 'Unknown'),
                "os": session.get('os', 'Unknown'),
                "device_fingerprint": session.get('device_fingerprint', ''),
                "login_time": session.get('login_time', ''),
                "last_activity": session.get('last_activity', ''),
                "logout_time": session.get('logout_time'),
                "location_country": session.get('location_country', 'Unknown'),
                "location_city": session.get('location_city', 'Unknown'),
                "latitude": session.get('latitude'),
                "longitude": session.get('longitude'),
                "auth_method": session.get('auth_method', 'unknown'),
                "user_type": session.get('user_type', 'admin'),
                "success": session_success,
                "duration_minutes": 0,
                "pages_visited": random.randint(1, 100),
                "data_transferred_mb": round(random.uniform(0.1, 50.0), 2),
                "risk_score": random.randint(0, 100),
                "threat_indicators": []
            }
            
            # Calculate session duration
            if session.get('login_time') and session.get('logout_time'):
                try:
                    login_time = datetime.fromisoformat(session['login_time'].replace('Z', '+00:00'))
                    logout_time = datetime.fromisoformat(session['logout_time'].replace('Z', '+00:00'))
                    duration = (logout_time - login_time).total_seconds() / 60
                    enhanced_session['duration_minutes'] = round(duration, 1)
                except (ValueError, TypeError):
                    pass
            elif session.get('login_time') and session.get('last_activity'):
                try:
                    login_time = datetime.fromisoformat(session['login_time'].replace('Z', '+00:00'))
                    last_activity = datetime.fromisoformat(session['last_activity'].replace('Z', '+00:00'))
                    duration = (last_activity - login_time).total_seconds() / 60
                    enhanced_session['duration_minutes'] = round(duration, 1)
                except (ValueError, TypeError):
                    pass
            
            # Add threat indicators for high-risk sessions
            if enhanced_session['risk_score'] > 70:
                enhanced_session['threat_indicators'] = random.sample([
                    'Multiple failed login attempts',
                    'Unusual geographic location',
                    'Suspicious user agent',
                    'High data transfer volume',
                    'Unusual access patterns'
                ], random.randint(1, 3))
            
            filtered_sessions.append(enhanced_session)
        
        # Sort by login time (newest first)
        filtered_sessions.sort(key=lambda x: x['login_time'], reverse=True)
        
        # Apply pagination
        total_sessions = len(filtered_sessions)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_sessions = filtered_sessions[start_idx:end_idx]
        
        # Calculate summary statistics
        successful_sessions = len([s for s in filtered_sessions if s['success']])
        failed_sessions = total_sessions - successful_sessions
        unique_users = len(set(s['user_id'] for s in filtered_sessions))
        unique_ips = len(set(s['ip_address'] for s in filtered_sessions))
        avg_duration = sum(s['duration_minutes'] for s in filtered_sessions) / len(filtered_sessions) if filtered_sessions else 0
        
        response_data = {
            "sessions": paginated_sessions,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total_sessions,
                "total_pages": (total_sessions + page_size - 1) // page_size
            },
            "summary": {
                "total_sessions": total_sessions,
                "successful_sessions": successful_sessions,
                "failed_sessions": failed_sessions,
                "success_rate": round((successful_sessions / total_sessions) * 100, 1) if total_sessions > 0 else 0,
                "unique_users": unique_users,
                "unique_ip_addresses": unique_ips,
                "avg_session_duration_minutes": round(avg_duration, 1),
                "total_data_transferred_mb": round(sum(s['data_transferred_mb'] for s in filtered_sessions), 2)
            },
            "filters": {
                "time_range": time_range,
                "user_type": user_type,
                "user": user_filter,
                "ip": ip_filter,
                "auth_method": auth_method_filter,
                "result": result_filter
            },
            "generated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Retrieved {len(paginated_sessions)} historical sessions (page {page}/{response_data['pagination']['total_pages']})")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting historical sessions: {e}")
        raise APIError(
            message="Failed to retrieve historical sessions",
            status_code=500,
            details=str(e)
        )

@sessions_v1_bp.route('/geo-locations', methods=['GET'])
def get_session_geo_locations():
    """
    Get geographic distribution of sessions for mapping.
    Provides location data for geographic visualization.
    """
    try:
        time_range = request.args.get('time_range', '7d')
        user_type = request.args.get('user_type', 'admin')
        
        start_date, end_date = get_date_range(time_range)
        
        session_model = SessionModel()
        
        # Get sessions with location data
        sessions_with_location = session_model.get_session_locations(user_type=user_type)
        
        # Filter by date range and enhance data
        geo_sessions = []
        location_counts = {}
        
        for session in sessions_with_location:
            if session.get('login_time'):
                try:
                    login_time = datetime.fromisoformat(session['login_time'].replace('Z', '+00:00'))
                    if not (start_date <= login_time <= end_date):
                        continue
                except (ValueError, TypeError):
                    continue
            
            if session.get('latitude') and session.get('longitude'):
                location_key = f"{session['location_country']},{session['location_city']}"
                if location_key not in location_counts:
                    location_counts[location_key] = {
                        "country": session['location_country'],
                        "city": session['location_city'],
                        "latitude": session['latitude'],
                        "longitude": session['longitude'],
                        "session_count": 0,
                        "unique_users": set(),
                        "last_activity": session['login_time']
                    }
                
                location_counts[location_key]["session_count"] += 1
                location_counts[location_key]["unique_users"].add(session['user_id'])
                
                # Update last activity if this session is more recent
                if session['login_time'] > location_counts[location_key]["last_activity"]:
                    location_counts[location_key]["last_activity"] = session['login_time']
        
        # Convert to list format for response
        geo_data = []
        for location_data in location_counts.values():
            geo_data.append({
                "country": location_data["country"],
                "city": location_data["city"],
                "latitude": location_data["latitude"],
                "longitude": location_data["longitude"],
                "session_count": location_data["session_count"],
                "unique_users": len(location_data["unique_users"]),
                "last_activity": location_data["last_activity"],
                "risk_level": random.choice(['low', 'medium', 'high']) if random.random() < 0.1 else 'low'  # 10% chance of elevated risk
            })
        
        # Sort by session count (highest first)
        geo_data.sort(key=lambda x: x['session_count'], reverse=True)
        
        # Calculate summary statistics
        total_locations = len(geo_data)
        total_sessions = sum(loc['session_count'] for loc in geo_data)
        total_unique_users = len(set().union(*[location_counts[key]["unique_users"] for key in location_counts]))
        
        # Get top countries
        country_stats = {}
        for loc in geo_data:
            country = loc['country']
            if country not in country_stats:
                country_stats[country] = {"sessions": 0, "users": 0, "cities": 0}
            country_stats[country]["sessions"] += loc['session_count']
            country_stats[country]["users"] += loc['unique_users']
            country_stats[country]["cities"] += 1
        
        top_countries = sorted(country_stats.items(), key=lambda x: x[1]['sessions'], reverse=True)[:10]
        
        response_data = {
            "locations": geo_data,
            "summary": {
                "total_locations": total_locations,
                "total_sessions": total_sessions,
                "total_unique_users": total_unique_users,
                "countries_represented": len(country_stats),
                "avg_sessions_per_location": round(total_sessions / total_locations, 1) if total_locations > 0 else 0
            },
            "top_countries": [
                {
                    "country": country,
                    "sessions": stats["sessions"],
                    "users": stats["users"],
                    "cities": stats["cities"]
                }
                for country, stats in top_countries
            ],
            "time_range": time_range,
            "user_type": user_type,
            "generated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Retrieved geographic data for {total_locations} locations, {total_sessions} sessions")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting session geo-locations: {e}")
        raise APIError(
            message="Failed to retrieve session geographic data",
            status_code=500,
            details=str(e)
        )
