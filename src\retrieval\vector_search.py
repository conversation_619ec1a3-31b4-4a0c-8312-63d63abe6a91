"""
Vector similarity search for document retrieval using Qdrant.
"""
from typing import List, Dict, Any, Optional
import numpy as np
import time
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type, wait_exponential
import traceback # Added for detailed error logging

from ..utils.logger import get_logger
from ..database.vector_store import QdrantVectorStore, VectorStoreError # Import VectorStoreError
from ..document_processing.embedding_generator import Embedding<PERSON>enerator
from ..config import SIMILARITY_THRESHOLD, MAX_VECTOR_SEARCH_TOP_K # Ensure these are well-defined

logger = get_logger(__name__)

# Define custom exceptions for more granular error handling
class SearchServiceError(Exception):
    """Custom exception for errors specifically within the VectorSearch service."""
    pass

class EmbeddingGenerationError(SearchServiceError):
    """Exception for failures during embedding generation."""
    pass

class VectorStoreSearchError(SearchServiceError):
    """Exception for failures during vector store search."""
    pass

# Lazy imports to avoid circular dependencies
def get_embedding_generator():
    from ..document_processing.embedding_generator import EmbeddingGenerator
    return EmbeddingGenerator()

def get_vector_store():
    from ..database.vector_store import QdrantVectorStore
    return QdrantVectorStore()

class VectorSearch:
    """Vector similarity search for document retrieval using Qdrant.
    Orchestrates embedding generation and Qdrant search with post-processing.
    """

    def __init__(self,
                 vector_store: Optional[QdrantVectorStore] = None,
                 embedding_generator: Optional[EmbeddingGenerator] = None):
        # Initialize dependencies, allowing for dependency injection for testing
        try:
            self.vector_store = vector_store or get_vector_store()
            self.embedding_generator = embedding_generator or get_embedding_generator()
            logger.info("VectorSearch initialized successfully.")
        except Exception as e:
            logger.critical(f"Failed to initialize VectorSearch components: {e}", exc_info=True)
            raise SearchServiceError("Failed to initialize vector search service.") from e

    # Apply exponential backoff for retries, which is generally better for services
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10),
           retry=retry_if_exception_type(Exception), reraise=True)
    def _generate_embedding(self, query: str) -> np.ndarray:
        """Generates an embedding for the given query with retry logic."""
        logger.info("=" * 80)
        logger.info("🧠 VECTOR SEARCH - EMBEDDING GENERATION STARTED")
        logger.info("=" * 80)
        logger.info(f"📝 Query: '{query[:100]}...'")
        logger.info(f"📏 Query length: {len(query)} characters")
        logger.info("=" * 80)
        
        logger.info(f"Starting embedding generation for query (first 50 chars): '{query[:50]}'")
        try:
            import asyncio
            import concurrent.futures
            
            logger.info("⚙️ Setting up thread pool executor for embedding generation...")
            # Add timeout to embedding generation
            with concurrent.futures.ThreadPoolExecutor() as executor:
                logger.info("🚀 Submitting embedding generation task...")
                future = executor.submit(self.embedding_generator.generate_query_embedding, query)
                logger.info("⏱️ Waiting for embedding generation with 15s timeout...")
                embedding = future.result(timeout=15.0)  # 15 second timeout
                logger.info("✅ Embedding generation task completed")
                
            logger.info("🔍 Validating generated embedding...")
            if not isinstance(embedding, np.ndarray) or embedding.ndim == 0 or embedding.size == 0:
                logger.error(f"❌ Generated embedding is not a valid numpy array: type={type(embedding)}, shape={getattr(embedding, 'shape', 'N/A')}")
                raise ValueError("Generated embedding is not a valid numpy array.")
            
            logger.info(f"✅ Successfully generated embedding with shape: {embedding.shape}")
            logger.info(f"📊 Embedding data type: {embedding.dtype}")
            logger.info(f"📊 Embedding size: {embedding.size}")
            
            logger.info("=" * 80)
            logger.info("🎉 VECTOR SEARCH - EMBEDDING GENERATION COMPLETED SUCCESSFULLY")
            logger.info("=" * 80)
            logger.info(f"📝 Query: '{query[:100]}...'")
            logger.info(f"📊 Embedding shape: {embedding.shape}")
            logger.info(f"📊 Embedding size: {embedding.size}")
            logger.info("=" * 80)
            
            return embedding
        except concurrent.futures.TimeoutError:
            logger.error("=" * 80)
            logger.error("⏰ VECTOR SEARCH - EMBEDDING GENERATION TIMEOUT")
            logger.error("=" * 80)
            logger.error(f"📝 Query: '{query[:100]}...'")
            logger.error("⏰ Embedding generation timed out after 15 seconds")
            logger.error("=" * 80)
            
            logger.error("Embedding generation timed out after 15 seconds")
            raise EmbeddingGenerationError("Embedding generation timed out")
        except Exception as e:
            logger.error("=" * 80)
            logger.error("❌ VECTOR SEARCH - EMBEDDING GENERATION FAILED")
            logger.error("=" * 80)
            logger.error(f"📝 Query: '{query[:100]}...'")
            logger.error(f"❌ Error type: {type(e).__name__}")
            logger.error(f"❌ Error message: {str(e)}")
            logger.error(f"🔍 Traceback: {traceback.format_exc()}")
            logger.error("=" * 80)
            
            logger.error(f"Failed to generate embedding for query: {str(e)}", exc_info=True)
            raise EmbeddingGenerationError(f"Embedding generation failed: {str(e)}") from e

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=1, max=10),
           retry=retry_if_exception_type((VectorStoreError, Exception)), reraise=True) # Retry on VectorStoreError
    def _vector_store_search(self, query_embedding: np.ndarray, top_k: int) -> List[Dict[str, Any]]:
        """Performs search against the vector store with retry logic."""
        logger.info("=" * 80)
        logger.info("🔍 VECTOR SEARCH - VECTOR STORE SEARCH STARTED")
        logger.info("=" * 80)
        logger.info(f"🔢 Top K: {top_k}")
        logger.info(f"📊 Query embedding shape: {query_embedding.shape}")
        logger.info(f"📊 Query embedding size: {query_embedding.size}")
        logger.info(f"📊 Query embedding dtype: {query_embedding.dtype}")
        logger.info("=" * 80)
        
        logger.debug(f"Attempting vector store search with top_k={top_k}")
        try:
            logger.info("🚀 Calling vector store search...")
            results = self.vector_store.search(query_embedding=query_embedding, top_k=top_k)
            logger.info(f"✅ Vector store search completed successfully")
            logger.info(f"📊 Results returned: {len(results)}")
            logger.debug(f"Vector store search returned {len(results)} results.")
            
            logger.info("=" * 80)
            logger.info("🎉 VECTOR SEARCH - VECTOR STORE SEARCH COMPLETED SUCCESSFULLY")
            logger.info("=" * 80)
            logger.info(f"🔢 Top K requested: {top_k}")
            logger.info(f"📊 Results found: {len(results)}")
            logger.info("=" * 80)
            
            return results
        except VectorStoreError as e: # Catch specific VectorStoreError
            logger.error("=" * 80)
            logger.error("❌ VECTOR SEARCH - VECTOR STORE SEARCH FAILED (VECTOR STORE ERROR)")
            logger.error("=" * 80)
            logger.error(f"❌ Error type: {type(e).__name__}")
            logger.error(f"❌ Error message: {str(e)}")
            logger.error(f"🔍 Error details: {e}")
            logger.error("=" * 80)
            
            logger.error(f"Vector store specific error during search: {str(e)}", exc_info=True)
            raise VectorStoreSearchError(f"Vector store search failed: {str(e)}") from e
        except Exception as e:
            logger.error("=" * 80)
            logger.error("❌ VECTOR SEARCH - VECTOR STORE SEARCH FAILED (GENERAL ERROR)")
            logger.error("=" * 80)
            logger.error(f"❌ Error type: {type(e).__name__}")
            logger.error(f"❌ Error message: {str(e)}")
            logger.error(f"🔍 Traceback: {traceback.format_exc()}")
            logger.error("=" * 80)
            
            logger.error(f"General error during vector store search: {str(e)}", exc_info=True)
            raise VectorStoreSearchError(f"Vector store search failed: {str(e)}") from e

    def search(self, query: str, top_k: int = 3, prioritize_files: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Search for documents similar to the query, applying relevance filtering and prioritization.
        """
        logger.info("=" * 80)
        logger.info("🔍 VECTOR SEARCH - SEARCH STARTED")
        logger.info("=" * 80)
        logger.info(f"📝 Query: '{query}'")
        logger.info(f"🔢 Top K: {top_k}")
        logger.info(f"📁 Prioritized files: {prioritize_files}")
        logger.info("=" * 80)
        
        search_start_time = time.time()
        prioritize_files = prioritize_files or []

        if not isinstance(query, str) or not query.strip():
            logger.warning("⚠️ Invalid or empty query received for search.")
            return []

        try:
            # 1. Generate embedding
            logger.info("🧠 Starting embedding generation...")
            logger.info(f"Starting embedding generation for query: '{query[:50]}...'")
            query_embedding = self._generate_embedding(query)
            logger.info(f"✅ Embedding generation completed successfully")
            logger.info(f"📊 Embedding shape: {query_embedding.shape}")

            # 2. Fetch a large candidate pool (high-recall)
            candidate_k = max(top_k * 5, 30)
            logger.info(f"🔍 Starting vector store search with candidate_k={candidate_k}")
            logger.info(f"Starting vector store search with candidate_k={max(top_k * 5, 30)}")
            results = self._vector_store_search(query_embedding, top_k=candidate_k)
            logger.info(f"✅ Vector store search completed, got {len(results)} results")
            logger.info(f"Vector store search completed, got {len(results)} results")

            if not results:
                logger.info("📭 No results returned from Qdrant.")
                return []

            logger.info("🔍 Processing and validating search results...")
            valid_results = []
            for doc in results:
                if not isinstance(doc, dict) or "score" not in doc:
                    logger.debug(f"⚠️ Skipping invalid document: {type(doc)}")
                    continue
                score = doc["score"]
                if not isinstance(score, (int, float)):
                    logger.debug(f"⚠️ Skipping document with invalid score: {score}")
                    continue
                doc_copy = doc.copy()
                doc_copy["original_score"] = score
                valid_results.append(doc_copy)

            logger.info(f"✅ Validated {len(valid_results)} results out of {len(results)} raw results")

            if not valid_results:
                logger.warning("⚠️ No valid results after validation")
                return []

            # 3. Dynamic threshold filtering (loose)
            logger.info("🔍 Applying dynamic threshold filtering...")
            max_score = max(doc["score"] for doc in valid_results)
            min_allowed = max(SIMILARITY_THRESHOLD, max_score * 0.6)
            filtered = [doc for doc in valid_results if doc["score"] >= min_allowed]
            logger.info(f"📊 Filtering: max_score={max_score:.3f}, min_allowed={min_allowed:.3f}, filtered_count={len(filtered)}")

            if len(filtered) < 2:
                logger.info("📊 Adjusting filter to ensure minimum results")
                filtered = valid_results[:top_k * 2]

            # 4. Boost scores for prioritized files
            logger.info("🚀 Applying prioritized file scoring...")
            for doc in filtered:
                source_file = doc.get("source_file", "")
                for pf in prioritize_files:
                    if pf and pf.lower() in source_file.lower():
                        doc["score"] *= 1.2
                        doc["prioritized"] = True
                        logger.debug(f"✅ Boosted score for prioritized file: {source_file}")
                        break
                else:
                    doc["prioritized"] = False

            # 5. Optional local reranking
            logger.info("🔄 Applying local reranking...")
            for doc in filtered:
                title_len = len(doc.get("title", ""))
                doc["rerank_score"] = doc["score"] - 0.01 * title_len

            # 6. Sort by reranked score
            logger.info("📊 Sorting results by reranked score...")
            filtered.sort(key=lambda d: d.get("rerank_score", d["score"]), reverse=True)
            final = filtered[:top_k]

            search_time = time.time() - search_start_time
            logger.info("✅ Search completed", extra={
                "final_count": len(final),
                "max_score": max_score,
                "threshold_used": min_allowed,
                "prioritized_hits": sum(1 for d in final if d["prioritized"])
            })
            
            logger.info("=" * 80)
            logger.info("🎉 VECTOR SEARCH - SEARCH COMPLETED SUCCESSFULLY")
            logger.info("=" * 80)
            logger.info(f"📝 Query: '{query[:100]}...'")
            logger.info(f"🔢 Top K requested: {top_k}")
            logger.info(f"📚 Final results: {len(final)}")
            logger.info(f"⏱️ Search time: {search_time:.3f}s")
            logger.info(f"📊 Max score: {max_score:.3f}")
            logger.info(f"🚀 Prioritized hits: {sum(1 for d in final if d['prioritized'])}")
            logger.info("=" * 80)

            return final

        except Exception as e:
            logger.error("=" * 80)
            logger.error("❌ VECTOR SEARCH - SEARCH FAILED")
            logger.error("=" * 80)
            logger.error(f"📝 Query: '{query[:100]}...'")
            logger.error(f"❌ Error type: {type(e).__name__}")
            logger.error(f"❌ Error message: {str(e)}")
            logger.error(f"🔍 Traceback: {traceback.format_exc()}")
            logger.error("=" * 80)
            
            logger.error(f"Vector search failed: {str(e)}", exc_info=True)
            return []
