import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { authApi } from '@/services/api';
import { useAuthStore } from '../../hooks/useAuthStore';
import TwoFASetupModal from '../../components/2FASetupModal';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [totpCode, setTotpCode] = useState('');
  const [show2FA, setShow2FA] = useState(false);
  const [show2FASetup, setShow2FASetup] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | Error | null>('');
  const [has2FA, setHas2FA] = useState<boolean | null>(null);
  const navigate = useNavigate();
  const setToken = useAuthStore((state) => state.setToken);
  const setUser = useAuthStore((state) => state.setUser);
  const setRole = useAuthStore((state) => state.setRole);

  const check2FAStatus = async (email: string) => {
    try {
      const response = await authApi.check2FAStatus(email);
      if (response.data.success) {
        setHas2FA(response.data.has_2fa);
        if (response.data.has_2fa) {
          setShow2FA(true);
        } else {
          setShow2FASetup(true);
        }
      }
    } catch (err: any) {
      console.error("Failed to check 2FA status:", err);
      // If check fails, assume user needs 2FA setup
      setShow2FASetup(true);
    }
  };

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) {
      setError("Please enter your email address");
      return;
    }
    
    setLoading(true);
    setError("");
    
    try {
      await check2FAStatus(email);
    } catch (err: any) {
      setError("Failed to check authentication status. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleVerify2FA = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!totpCode.trim()) {
      setError("Please enter the 2FA code");
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const response = await authApi.verify2FA(email, totpCode);
      if (response.data.success && response.data.token) {
        setToken(response.data.token);
        
        // Fetch user info using the token
        try {
          const userRes = await fetch('/api/admin-users/me', {
            headers: { 'Authorization': `Bearer ${response.data.token}` }
          });
          if (userRes.ok) {
            const userData = await userRes.json();
            setUser(userData);
            setRole(userData.role);
          }
        } catch (userErr) {
          console.error("Failed to fetch user data:", userErr);
        }
        
        navigate('/dashboard');
      } else {
        setError(response.data.message || 'Invalid 2FA code');
      }
    } catch (err: any) {
      setError(err?.response?.data?.detail || 'Invalid 2FA code');
    } finally {
      setLoading(false);
    }
  };

  const handle2FASetupComplete = () => {
    setShow2FASetup(false);
    setShow2FA(true);
    setHas2FA(true);
  };

  const handleBackToEmail = () => {
    setShow2FA(false);
    setShow2FASetup(false);
    setTotpCode('');
    setError('');
    setHas2FA(null);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-accent-400/30 to-neutral-900 px-4">
      <div className="w-full max-w-2xl flex rounded-3xl shadow-2xl overflow-hidden border border-neutral-800 bg-neutral-950">
        {/* Left: Welcome/Branding */}
        <div className="hidden md:flex flex-col justify-center items-center w-1/2 bg-gradient-to-br from-accent-400/40 to-accent-600/20 p-8">
          <div className="text-4xl font-extrabold bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent mb-4 text-center">
            Proxima28 Admin
          </div>
          <div className="text-lg text-neutral-200 text-center mb-2 font-semibold">
            Welcome Back!
          </div>
          <div className="text-neutral-400 text-center">
            Sign in to access your admin dashboard and manage your organization securely.
          </div>
        </div>
        
        {/* Right: Login Form */}
        <div className="flex-1 flex flex-col gap-6 p-10 justify-center">
          <div className="text-3xl font-extrabold text-center bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent mb-2 md:hidden">
            Proxima28 Admin Login
          </div>
          <div className="text-neutral-400 text-center mb-4 md:hidden">Sign in to your admin account</div>
          
          {!show2FA && !show2FASetup && (
            <form onSubmit={handleEmailSubmit} className="space-y-6">
              <input
                type="email"
                placeholder="Admin Email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 focus:outline-none focus:border-accent-400"
                required
              />
              {error && <div className="text-red-500 p-2">{typeof error === 'string' ? error : error?.message}</div>}
              <button
                type="submit"
                className="w-full bg-accent-400 hover:bg-accent-600 text-neutral-900 font-bold rounded py-2 text-lg transition"
                disabled={loading}
              >
                {loading ? 'Checking...' : 'Continue'}
              </button>
            </form>
          )}

          {show2FA && (
            <form onSubmit={handleVerify2FA} className="space-y-6">
              <div className="text-center">
                <h3 className="text-xl font-semibold text-neutral-200 mb-2">Two-Factor Authentication</h3>
                <p className="text-neutral-400 text-sm">Enter the 6-digit code from your authenticator app</p>
              </div>
              <input
                type="text"
                placeholder="000000"
                value={totpCode}
                onChange={e => setTotpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                className="bg-neutral-900 border border-neutral-700 rounded px-4 py-2 text-lg text-neutral-100 focus:outline-none focus:border-accent-400 text-center tracking-widest font-mono"
                maxLength={6}
                required
                autoFocus
              />
              {error && <div className="text-red-500 p-2">{typeof error === 'string' ? error : error?.message}</div>}
              <button
                type="submit"
                className="w-full bg-accent-400 hover:bg-accent-600 text-neutral-900 font-bold rounded py-2 text-lg transition"
                disabled={loading}
              >
                {loading ? 'Verifying...' : 'Verify & Sign In'}
              </button>
              <button
                type="button"
                onClick={handleBackToEmail}
                className="w-full bg-neutral-700 hover:bg-neutral-600 text-neutral-200 font-medium rounded py-2 text-lg transition"
              >
                Back to Email
              </button>
            </form>
          )}

          <div className="flex justify-between text-xs text-neutral-500 mt-2">
            <button type="button" className="hover:underline text-left">Forgot password?</button>
            <button type="button" className="hover:underline" onClick={() => navigate('/register')}>Create Account</button>
          </div>
          <div className="text-xs text-neutral-500 text-center mt-2">
            Only admin users are allowed. Unauthorized access is prohibited.
          </div>
        </div>
      </div>

      {/* 2FA Setup Modal */}
      <TwoFASetupModal
        isOpen={show2FASetup}
        onClose={() => setShow2FASetup(false)}
        email={email}
        onSetupComplete={handle2FASetupComplete}
      />
    </div>
  );
} 