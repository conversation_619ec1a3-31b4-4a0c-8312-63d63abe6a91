version: '3.8'

services:
  rag-chatbot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rag-chatbot-backend
    restart: unless-stopped
    ports:
      - "5051:5051"
    environment:
      - FLASK_ENV=production
      - LOG_LEVEL=WARNING
      - DEVICE_AUTO_SELECT=true
      - PREFER_GPU=true
      - FALLBACK_CPU=true
      - LAZY_LOAD_NER=true
      - LAZY_LOAD_INTENT=true
      - ENABLE_ASYNC_LOADING=true
      - GUNICORN_WORKERS=4
      - GUNICORN_TIMEOUT=120
      - GUNICORN_MAX_REQUESTS=1000
    env_file:
      - .env
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models_cache:/app/models_cache
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '4.0'
        reservations:
          memory: 2G
          cpus: '2.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5051/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - rag-network

  # Optional: Redis for session management and caching
  redis:
    image: redis:7-alpine
    container_name: rag-chatbot-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - rag-network

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: rag-chatbot-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - rag-chatbot
    networks:
      - rag-network

volumes:
  redis_data:

networks:
  rag-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
