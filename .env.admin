# ============================================================================
# ADMIN DASHBOARD ENVIRONMENT CONFIGURATION
# ============================================================================

# === Admin Dashboard Backend Settings ===
ADMIN_PORT=8000
ADMIN_HOST=0.0.0.0
ADMIN_DEBUG=false
ADMIN_ENV=production

# === Chatbot Backend Integration ===
CHATBOT_BACKEND_URL=http://localhost:5051
CHATBOT_API_VERSION=v1
CHATBOT_API_TIMEOUT=30

# === Database Configuration ===
ADMIN_DB_PATH=admin_dashboard/data/admin_users.db
ESCALATIONS_DB_PATH=admin_dashboard/data/escalations.db
OVERRIDES_DB_PATH=admin_dashboard/data/overrides.db
AUDIT_LOG_DB_PATH=admin_dashboard/data/audit_logs.db

# === Security & Authentication ===
ADMIN_JWT_SECRET=admin_s8d7f6g5h4j3k2l1m0n9b8v7c6x5z4a3
ADMIN_SESSION_TIMEOUT=3600
ENABLE_2FA=true
ADMIN_PASSWORD_MIN_LENGTH=8

# === TOTP/2FA Configuration ===
TOTP_ISSUER=HR_Assistant_Admin
TOTP_VALIDITY_WINDOW=1
BACKUP_CODES_COUNT=10

# === Logging Configuration ===
ADMIN_LOG_LEVEL=INFO
ADMIN_LOG_FILE=logs/admin_dashboard.log
ADMIN_MAX_LOG_FILES=5
ENABLE_AUDIT_LOGGING=true

# === API Rate Limiting ===
ADMIN_RATE_LIMIT_ENABLED=true
ADMIN_RATE_LIMIT_PER_MINUTE=120
ADMIN_RATE_LIMIT_PER_HOUR=2000

# === CORS Settings ===
ADMIN_CORS_ORIGINS=http://localhost:3000,http://localhost:3001
ADMIN_CORS_ALLOW_CREDENTIALS=true

# === Data Refresh Intervals ===
ANALYTICS_REFRESH_INTERVAL=30
METRICS_REFRESH_INTERVAL=60
LIVE_DATA_REFRESH_INTERVAL=10

# === Dashboard Features ===
ENABLE_REAL_TIME_ANALYTICS=true
ENABLE_EXPORT_FEATURES=true
ENABLE_ADVANCED_FILTERING=true
ENABLE_BULK_OPERATIONS=true

# === Email Notifications ===
ADMIN_SMTP_SERVER=smtp.office365.com
ADMIN_SMTP_PORT=587
ADMIN_SMTP_USERNAME=<EMAIL>
ADMIN_SMTP_PASSWORD=AdminVikky@9878
ADMIN_SENDER_EMAIL=<EMAIL>
ENABLE_ADMIN_EMAIL_ALERTS=true

# === Backup & Maintenance ===
ENABLE_AUTO_BACKUP=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
MAINTENANCE_MODE=false

# === Performance Settings ===
ADMIN_WORKER_PROCESSES=2
ADMIN_WORKER_TIMEOUT=60
ADMIN_MAX_REQUESTS_PER_WORKER=500

# === Cache Configuration ===
ADMIN_ENABLE_CACHE=true
ADMIN_CACHE_TTL=300
ADMIN_CACHE_MAX_SIZE=500

# === Monitoring & Health Checks ===
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=60
ENABLE_PERFORMANCE_MONITORING=true

# === Data Retention Policies ===
ANALYTICS_DATA_RETENTION_DAYS=365
AUDIT_LOG_RETENTION_DAYS=730
SESSION_DATA_RETENTION_DAYS=90
ESCALATION_DATA_RETENTION_DAYS=180

# === External Integrations ===
ENABLE_SLACK_INTEGRATION=false
SLACK_WEBHOOK_URL=
ENABLE_TEAMS_INTEGRATION=false
TEAMS_WEBHOOK_URL=

# === Compliance & GDPR ===
ENABLE_GDPR_FEATURES=true
DATA_PROCESSING_CONSENT_REQUIRED=true
ENABLE_DATA_EXPORT=true
ENABLE_DATA_DELETION=true
GDPR_CONTACT_EMAIL=<EMAIL>

# === UI/UX Settings ===
DEFAULT_THEME=light
ENABLE_DARK_MODE=true
DEFAULT_LANGUAGE=en
ENABLE_MULTI_LANGUAGE=true
ITEMS_PER_PAGE=20
MAX_EXPORT_RECORDS=10000

# === Security Headers ===
ENABLE_SECURITY_HEADERS=true
ENABLE_CSP=true
ENABLE_HSTS=true
ENABLE_XSS_PROTECTION=true

# === Development Settings (only for development) ===
ENABLE_DEBUG_TOOLBAR=false
ENABLE_MOCK_DATA=false
MOCK_DATA_SIZE=100
