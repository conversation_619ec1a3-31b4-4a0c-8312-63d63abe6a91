"""
Document version control and management system.
Handles document versioning, change tracking, and rollback capabilities.
"""
import os
import json
import hashlib
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import sqlite3
from dataclasses import dataclass, asdict

from ..utils.logger import get_logger
from ..config import DATA_DIR
from src.document_processing.embedding_generator import EmbeddingGenerator
from src.database.vector_store import QdrantVectorStore

logger = get_logger(__name__)

VERSIONS_FILE = Path("data/cache/document_versions.json")

# Lazy imports to avoid circular dependencies
def get_embedding_generator():
    from .embedding_generator import EmbeddingGenerator
    return EmbeddingGenerator()

def get_vector_store():
    from ..database.vector_store import QdrantVectorStore
    return QdrantVectorStore()

class DocumentVersionControl:
    """Production-grade document versioning and Qdrant re-indexing."""

    def __init__(self):
        self.versions = {}
        self._load_versions()
        self.embedding_generator = get_embedding_generator()
        self.vector_store = get_vector_store()

    def _load_versions(self):
        if VERSIONS_FILE.exists():
            try:
                with open(VERSIONS_FILE, 'r', encoding='utf-8') as f:
                    self.versions = json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load document_versions.json: {e}. Rebuilding from scratch.")
                self.versions = {}
        else:
            self.versions = {}

    def _save_versions(self):
        with open(VERSIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(self.versions, f, indent=2)

    def compute_hash(self, file_path):
        h = hashlib.sha256()
        with open(file_path, 'rb') as f:
            while True:
                chunk = f.read(8192)
                if not chunk:
                    break
                h.update(chunk)
        return h.hexdigest()

    def is_file_changed(self, file_path):
        file_hash = self.compute_hash(file_path)
        stored_hash = self.versions.get(str(file_path.name))
        logger.info(f"Version control check for {file_path.name}: computed_hash={file_hash}, stored_hash={stored_hash}")
        if stored_hash == file_hash:
            logger.info(f"Skipping unchanged file: {file_path.name}")
            return False
        return True

    def update_version(self, file_path):
        file_hash = self.compute_hash(file_path)
        self.versions[str(file_path.name)] = file_hash
        self._save_versions()

    def reindex_document(self, file_path: Path) -> bool:
        try:
            if not self.is_file_changed(file_path):
                logger.info(f"No re-indexing needed for {file_path.name}")
                return True

            embeddings = self.embedding_generator.generate_document_embeddings(file_path)
            docs = [
                {
                    "content": chunk["content"],
                    "source_file": str(file_path.name),
                    "chunk_index": i
                }
                for i, chunk in enumerate(embeddings)
            ]

            self.vector_store.delete_by_source_file(str(file_path.name))
            self.vector_store.add_documents(documents=docs, vectors=embeddings)
            self.update_version(file_path)
            logger.info(f"Re-indexed {file_path.name} with {len(embeddings)} chunks")
            return True
        except Exception as e:
            logger.error(f"Failed to re-index {file_path.name}: {e}")
            return False
