"""
Resource management and configuration for the HR Assistant Chatbot.
Handles environment variables, configuration loading, and resource initialization.
"""
import os
import threading
import async<PERSON>
from typing import Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path

from sentence_transformers import SentenceTransformer
from transformers import AutoTokenizer

from src.utils.logger import get_logger
from src.config import (
    EMBEDDING_MODEL_NAME, UNIFIED_MODEL_NAME,
    LAZY_LOADING, ENABLE_ASYNC_LOADING,
    MAX_CONCURRENT_MODEL_LOADS, USE_LARGE_MODEL, PRELOAD_MODELS_ON_STARTUP
)

logger = get_logger(__name__)

class GlobalResources:
    _unified_model: Optional[SentenceTransformer] = None
    _embedding_tokenizer: Optional[AutoTokenizer] = None
    _lock = threading.Lock()
    _executor: Optional[ThreadPoolExecutor] = None

    @staticmethod
    def _get_executor() -> ThreadPoolExecutor:
        """Get or create the thread pool executor."""
        if GlobalResources._executor is None or GlobalResources._executor._shutdown:
            GlobalResources._executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_MODEL_LOADS)
        return GlobalResources._executor

    @staticmethod
    def get_unified_model() -> Optional[SentenceTransformer]:
        """Load and return the unified model (used for both embedding and other tasks)."""
        if GlobalResources._unified_model is None:
            with GlobalResources._lock:
                if GlobalResources._unified_model is None:
                    try:
                        model_name = EMBEDDING_MODEL_NAME if USE_LARGE_MODEL else UNIFIED_MODEL_NAME

                        logger.info(f"[MODEL] 🔄 Loading model: {model_name}")
                        model = SentenceTransformer(
                            'data/models_cache/bge-base-en-v1.5',
                            device='cpu'
                        )

                        GlobalResources._unified_model = model
                        logger.info("[MODEL] ✅ Unified model loaded.")
                    except Exception as e:
                        logger.exception(f"[MODEL] ❌ Failed to load model: {e}")

        return GlobalResources._unified_model

    @staticmethod
    def get_embedding_model() -> Optional[SentenceTransformer]:
        """Get the embedding model (alias for unified model)."""
        return GlobalResources.get_unified_model()

    @staticmethod
    def get_embedding_tokenizer() -> Optional[AutoTokenizer]:
        """Load and return the tokenizer for the embedding model."""
        if GlobalResources._embedding_tokenizer is None:
            with GlobalResources._lock:
                if GlobalResources._embedding_tokenizer is None:
                    try:
                        model_name = EMBEDDING_MODEL_NAME if USE_LARGE_MODEL else UNIFIED_MODEL_NAME

                        logger.info(f"[TOKENIZER] 🔄 Loading tokenizer: {model_name}")
                        tokenizer = AutoTokenizer.from_pretrained('data/models_cache/bge-base-en-v1.5')

                        GlobalResources._embedding_tokenizer = tokenizer
                        logger.info("[TOKENIZER] ✅ Tokenizer loaded.")
                    except Exception as e:
                        logger.exception(f"[TOKENIZER] ❌ Failed to load tokenizer: {e}")

        return GlobalResources._embedding_tokenizer

    @staticmethod
    async def async_warm_up_resources():
        """Asynchronously warm up resources if async loading is enabled."""
        if not ENABLE_ASYNC_LOADING:
            GlobalResources.warm_up_resources()
            return

        logger.info("[INIT] 🚀 Starting async warm-up...")

        tasks = []
        if not LAZY_LOADING.get('unified_model', False):
            tasks.append(GlobalResources._async_load_unified_model())

        if not LAZY_LOADING.get('tokenizer', False):
            tasks.append(GlobalResources._async_load_tokenizer())

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            # Log any exceptions that occurred
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"[INIT] ❌ Async loading task {i} failed: {result}")

        logger.info("[INIT] ✅ Async warm-up complete.")

    @staticmethod
    async def _async_load_unified_model():
        """Asynchronously load the unified model."""
        loop = asyncio.get_event_loop()
        executor = GlobalResources._get_executor()
        return await loop.run_in_executor(executor, GlobalResources.get_unified_model)

    @staticmethod
    async def _async_load_tokenizer():
        """Asynchronously load the tokenizer."""
        loop = asyncio.get_event_loop()
        executor = GlobalResources._get_executor()
        return await loop.run_in_executor(executor, GlobalResources.get_embedding_tokenizer)

    @staticmethod
    def warm_up_resources():
        """Synchronously warm up resources."""
        logger.info("[INIT] 🔄 Warming up resources...")

        if not LAZY_LOADING.get('unified_model', False) or PRELOAD_MODELS_ON_STARTUP:
            GlobalResources.get_unified_model()
        else:
            logger.info("[INIT] ⏭️ Skipped model preload.")

        if not LAZY_LOADING.get('tokenizer', False) or PRELOAD_MODELS_ON_STARTUP:
            GlobalResources.get_embedding_tokenizer()
        else:
            logger.info("[INIT] ⏭️ Skipped tokenizer preload.")

        logger.info("[INIT] ✅ Resource warm-up complete.")

    @staticmethod
    def get_model_status() -> Dict[str, Any]:
        """Get the current status of loaded models and configuration."""
        return {
            'unified_model_loaded': GlobalResources._unified_model is not None,
            'tokenizer_loaded': GlobalResources._embedding_tokenizer is not None,
            'lazy_loading_settings': LAZY_LOADING,
            'use_large_model': USE_LARGE_MODEL,
            'async_loading_enabled': ENABLE_ASYNC_LOADING,
            'preload_on_startup': PRELOAD_MODELS_ON_STARTUP
        }

    @staticmethod
    def cleanup():
        """Clean up all resources and shutdown the executor."""
        logger.info("[CLEANUP] 🧹 Cleaning up resources...")
        
        with GlobalResources._lock:
            GlobalResources._unified_model = None
            GlobalResources._embedding_tokenizer = None
            
            if GlobalResources._executor is not None:
                GlobalResources._executor.shutdown(wait=True)
                GlobalResources._executor = None
                
        logger.info("[CLEANUP] ✅ Cleanup complete.")

    @staticmethod
    def force_reload_models():
        """Force reload all models (useful for configuration changes)."""
        logger.info("[RELOAD] 🔄 Force reloading models...")
        
        with GlobalResources._lock:
            # Clear existing models
            GlobalResources._unified_model = None
            GlobalResources._embedding_tokenizer = None

        # Reload models
        try:
            GlobalResources.get_unified_model()
            GlobalResources.get_embedding_tokenizer()
            logger.info("[RELOAD] ✅ Reload complete.")
        except Exception as e:
            logger.exception(f"[RELOAD] ❌ Reload failed: {e}")
            raise

    @staticmethod
    def is_ready() -> bool:
        """Check if all required resources are loaded and ready."""
        required_model = not LAZY_LOADING.get('unified_model', False) or PRELOAD_MODELS_ON_STARTUP
        required_tokenizer = not LAZY_LOADING.get('tokenizer', False) or PRELOAD_MODELS_ON_STARTUP
        
        model_ready = not required_model or GlobalResources._unified_model is not None
        tokenizer_ready = not required_tokenizer or GlobalResources._embedding_tokenizer is not None
        
        return model_ready and tokenizer_ready


if __name__ == "__main__":
    import time

    logger.info("🔍 Starting Global Resource Diagnostic...")

    start_time = time.time()

    # Check environment
    groq_key = os.getenv("GROQ_API_KEY")
    logger.info("🔑 GROQ_API_KEY: %s", "[SET]" if groq_key else "[MISSING]")

    # Test model loading
    logger.info("🧠 Loading embedding model...")
    model = GlobalResources.get_embedding_model()
    
    if model is not None:
        logger.info("✅ Model loaded successfully")
    else:
        logger.error("❌ Model loading failed")

    # Test tokenizer loading
    logger.info("🔤 Loading tokenizer...")
    tokenizer = GlobalResources.get_embedding_tokenizer()
    
    if tokenizer is not None:
        logger.info("✅ Tokenizer loaded successfully")
    else:
        logger.error("❌ Tokenizer loading failed")

    # Display status
    status = GlobalResources.get_model_status()
    logger.info("📊 Model Status: %s", status)

    # Display timing
    total_time = time.time() - start_time
    logger.info("⏱️ Total init time: %.2fs", total_time)
    
    # Check if ready
    if GlobalResources.is_ready():
        logger.info("🟢 All resources ready!")
    else:
        logger.warning("🟡 Some resources not ready (check lazy loading settings)")

    logger.info("🏁 Resource check complete.")