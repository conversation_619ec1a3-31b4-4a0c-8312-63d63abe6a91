import sys
import os

# Add the admin_dashboard directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.api.admin_auth_api import app
from dotenv import load_dotenv

# Try to load .env from multiple possible locations
env_paths = [".env", "../.env", "../../.env"]
for env_path in env_paths:
    if os.path.exists(env_path):
        load_dotenv(dotenv_path=env_path)
        break

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5052) 