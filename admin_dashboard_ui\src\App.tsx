import React, { Suspense, lazy, useEffect } from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { <PERSON><PERSON>etProvider } from "react-helmet-async";
import { ErrorBoundary } from "react-error-boundary";
import { Toaster } from "react-hot-toast";
import { Layout } from "./components/Layout";
import { useTheme } from "./hooks/useThemeStore";
import { QueryProvider } from "./providers/QueryProvider";
import LoginPage from "./pages/login/index";
import RegisterPage from "./pages/register";
import { PermissionsProvider } from "./context/PermissionsProvider";
import "./i18n"; // Initialize i18n
import { ProtectedRoute } from "./components/ProtectedRoute";

// Lazy load all sidebar route pages
const AnalyticsInsights = lazy(() => import("./pages/dashboard/analytics/Insights"));
const ChatLogs = lazy(() => import("./pages/dashboard/analytics/ChatLogs"));

const FeedbackTrends = lazy(() => import("./pages/dashboard/feedback/Trends"));
const FeedbackEscalated = lazy(() => import("./pages/dashboard/feedback/Escalated"));
const FeedbackResolution = lazy(() => import("./pages/dashboard/feedback/Resolution"));

const AIWeeklyDigest = lazy(() => import("./pages/dashboard/ai/WeeklyDigest"));
const AIPolicyDrift = lazy(() => import("./pages/dashboard/ai/PolicyDrift"));

const TrainingMisunderstood = lazy(() => import("./pages/dashboard/training/Misunderstood"));
const TrainingNERIntent = lazy(() => import("./pages/dashboard/training/NERIntent"));

const LiveQueue = lazy(() => import("./pages/dashboard/live/Queue"));
const LiveOngoing = lazy(() => import("./pages/dashboard/live/Ongoing"));

const ComplianceGDPR = lazy(() => import("./pages/dashboard/compliance/GDPR"));
const ComplianceDeletion = lazy(() => import("./pages/dashboard/compliance/Deletion"));
const ComplianceSensitive = lazy(() => import("./pages/dashboard/compliance/Sensitive"));

const AdminUsersRoles = lazy(() => import("./components/AdminUsersRoles"));

const DeviceIntelligence = lazy(() => import("./pages/dashboard/DeviceIntelligence"));
const SystemSettings = lazy(() => import("./pages/dashboard/settings/SystemSettings"));

// ============================================================================
// ERROR FALLBACK COMPONENT
// ============================================================================

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full mx-auto p-6">
        <div className="bg-card border border-border rounded-lg p-6 shadow-lg">
          <div className="flex items-center space-x-3 mb-4">
            <div className="flex-shrink-0">
              <svg className="h-8 w-8 text-error-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-foreground">Something went wrong</h1>
              <p className="text-sm text-muted-foreground">An unexpected error occurred</p>
            </div>
          </div>

          <div className="mb-4">
            <details className="group">
              <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground transition-colors">
                Show error details
              </summary>
              <div className="mt-2 p-3 bg-muted rounded border text-xs font-mono text-muted-foreground overflow-auto max-h-32">
                {error.message}
                {error.stack && (
                  <pre className="mt-2 whitespace-pre-wrap">{error.stack}</pre>
                )}
              </div>
            </details>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={resetErrorBoundary}
              className="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm font-medium hover:bg-primary/90 transition-colors focus-ring"
            >
              Try again
            </button>
            <button
              onClick={() => window.location.reload()}
              className="flex-1 bg-secondary text-secondary-foreground px-4 py-2 rounded-md text-sm font-medium hover:bg-secondary/90 transition-colors focus-ring"
            >
              Reload page
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// LOADING COMPONENT
// ============================================================================

const LoadingFallback: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        <p className="text-muted-foreground animate-pulse">Loading...</p>
      </div>
    </div>
  );
};

// ============================================================================
// MAIN APP COMPONENT
// ============================================================================

export default function App() {
  // Theme management
  const { resolvedTheme, config } = useTheme();

  // Apply theme on mount and changes
  useEffect(() => {
    const root = document.documentElement;

    // Apply theme class
    if (resolvedTheme === "dark") {
      root.classList.add("dark");
    } else {
      root.classList.remove("dark");
    }

    // Apply accessibility classes
    if (config.high_contrast) {
      root.classList.add("high-contrast");
    } else {
      root.classList.remove("high-contrast");
    }

    if (config.reduced_motion) {
      root.classList.add("reduce-motion");
    } else {
      root.classList.remove("reduce-motion");
    }

    if (!config.animations_enabled) {
      root.classList.add("no-animations");
    } else {
      root.classList.remove("no-animations");
    }
  }, [resolvedTheme, config]);

  return (
    <HelmetProvider>
      <QueryProvider>
        <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
          <ErrorBoundary
            FallbackComponent={ErrorFallback}
            onError={(error, errorInfo) => {
              // Log error to monitoring service
              console.error('App Error:', error, errorInfo);
              // You can integrate with error tracking services like Sentry here
            }}
          >
            <PermissionsProvider>
              <Suspense fallback={<LoadingFallback />}>
                <Routes>
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/register" element={<RegisterPage />} />
                  <Route path="/" element={<Navigate to="/dashboard/analytics/insights" replace />} />
                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <Layout />
                    </ProtectedRoute>
                  }>
                    <Route index element={<Navigate to="analytics/insights" replace />} />
                    <Route path="analytics/insights" element={<AnalyticsInsights />} />
                    <Route path="chat_logs" element={<ChatLogs />} />

                    {/* Feedback & Escalations */}
                    <Route path="feedback/trends" element={<FeedbackTrends />} />
                    <Route path="feedback/escalated" element={<FeedbackEscalated />} />
                    <Route path="feedback/resolution" element={<FeedbackResolution />} />

                    {/* AI Insights */}
                    <Route path="ai/weekly-digest" element={<AIWeeklyDigest />} />
                    <Route path="ai/policy-drift" element={<AIPolicyDrift />} />

                    {/* Training Tools */}
                    <Route path="training/misunderstood" element={<TrainingMisunderstood />} />
                    <Route path="training/ner-intent" element={<TrainingNERIntent />} />

                    {/* Live Support */}
                    <Route path="live-support/queue" element={<LiveQueue />} />
                    <Route path="live-support/ongoing" element={<LiveOngoing />} />

                    {/* Compliance & Auditing */}
                    <Route path="compliance/gdpr" element={<ComplianceGDPR />} />
                    <Route path="compliance/deletion" element={<ComplianceDeletion />} />
                    <Route path="compliance/sensitive" element={<ComplianceSensitive />} />

                    {/* Users */}
                    <Route path="admin-users-roles" element={<AdminUsersRoles />} />

                    {/* Settings */}
                    <Route path="system-settings" element={<SystemSettings />} />

                    {/* Device Intelligence */}
                    <Route path="device-intelligence" element={<DeviceIntelligence />} />
                  </Route>
                </Routes>
              </Suspense>
            </PermissionsProvider>

            {/* Global Toast Notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                className: 'toast',
                style: {
                  background: resolvedTheme === 'dark' ? '#1f2937' : '#ffffff',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827',
                  border: resolvedTheme === 'dark' ? '1px solid #374151' : '1px solid #e5e7eb',
                },
                success: {
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#ffffff',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#ffffff',
                  },
                },
              }}
            />
          </ErrorBoundary>
        </BrowserRouter>
      </QueryProvider>
    </HelmetProvider>
  );
}
