"""
Device management utility for automatic GPU/CPU selection and optimization.
Provides centralized device management for all models in the application.
"""

import torch
import logging
from typing import Optional, Dict, Any
from ..config import DEVICE_CONFIG

logger = logging.getLogger(__name__)


class DeviceManager:
    """
    Centralized device management for optimal model performance.
    Automatically selects the best available device and provides fallback options.
    """
    
    _instance: Optional['DeviceManager'] = None
    _selected_device: Optional[torch.device] = None
    _device_info: Dict[str, Any] = {}
    
    def __new__(cls) -> 'DeviceManager':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        self._initialized = True
        self._detect_available_devices()
        self._select_optimal_device()
    
    @classmethod
    def get_instance(cls) -> 'DeviceManager':
        """Get the singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def _detect_available_devices(self) -> None:
        """Detect all available devices and their capabilities."""
        self._device_info = {
            'cuda': {
                'available': torch.cuda.is_available(),
                'count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
                'current': torch.cuda.current_device() if torch.cuda.is_available() else None,
                'name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
                'memory': torch.cuda.get_device_properties(0).total_memory if torch.cuda.is_available() else None
            },
            'mps': {
                'available': hasattr(torch.backends, 'mps') and torch.backends.mps.is_available(),
                'name': 'Apple Silicon GPU' if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available() else None
            },
            'cpu': {
                'available': True,
                'count': torch.get_num_threads(),
                'name': 'CPU'
            }
        }
        
        if self._device_info['cuda']['available']:
            logger.info(f"CUDA GPU detected: {self._device_info['cuda']['name']}")
            logger.info(f"GPU Memory: {self._device_info['cuda']['memory'] / 1024**3:.2f} GB")
        
        if self._device_info['mps']['available']:
            logger.info("Apple Silicon GPU (MPS) detected")
        
        logger.info(f"CPU Threads: {self._device_info['cpu']['count']}")
    
    def _select_optimal_device(self) -> None:
        """Select the optimal device based on configuration and availability."""
        # Check if device is forced via environment
        if DEVICE_CONFIG['force_device']:
            forced_device = DEVICE_CONFIG['force_device'].strip()
            # Skip if it's just a comment, empty, or whitespace
            if forced_device and not forced_device.startswith('#') and forced_device != '':
                if forced_device == 'cuda' and self._device_info['cuda']['available']:
                    self._selected_device = torch.device('cuda')
                    logger.info(f"Using forced device: CUDA GPU ({self._device_info['cuda']['name']})")
                    return
                elif forced_device == 'mps' and self._device_info['mps']['available']:
                    self._selected_device = torch.device('mps')
                    logger.info(f"Using forced device: Apple Silicon GPU")
                    return
                elif forced_device == 'cpu':
                    self._selected_device = torch.device('cpu')
                    logger.info(f"Using forced device: CPU")
                    return
                else:
                    logger.warning(f"Forced device '{forced_device}' not available, falling back to auto-selection")
        
        # Auto-selection logic
        if DEVICE_CONFIG['auto_select']:
            if DEVICE_CONFIG['prefer_gpu']:
                # Try CUDA first
                if self._device_info['cuda']['available']:
                    self._selected_device = torch.device('cuda')
                    logger.info(f"Auto-selected: CUDA GPU ({self._device_info['cuda']['name']})")
                    return
                
                # Try Apple Silicon GPU
                if self._device_info['mps']['available']:
                    self._selected_device = torch.device('mps')
                    logger.info("Auto-selected: Apple Silicon GPU")
                    return
            
            # Fallback to CPU if no GPU or if CPU is preferred
            if DEVICE_CONFIG['fallback_cpu']:
                self._selected_device = torch.device('cpu')
                logger.info("Auto-selected: CPU (fallback)")
                return
        
        # Default to CPU if no selection made
        self._selected_device = torch.device('cpu')
        logger.info("Using default device: CPU")
    
    def get_device(self) -> torch.device:
        """Get the selected device."""
        return self._selected_device
    
    def get_device_name(self) -> str:
        """Get a human-readable device name."""
        device = self.get_device()
        if device.type == 'cuda':
            return f"CUDA GPU ({self._device_info['cuda']['name']})"
        elif device.type == 'mps':
            return "Apple Silicon GPU"
        else:
            return "CPU"
    
    def is_gpu(self) -> bool:
        """Check if the selected device is a GPU."""
        device = self.get_device()
        return device.type in ['cuda', 'mps']
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get comprehensive device information."""
        return {
            'selected_device': str(self._selected_device),
            'device_name': self.get_device_name(),
            'is_gpu': self.is_gpu(),
            'available_devices': self._device_info
        }
    
    def optimize_for_device(self) -> None:
        """Apply device-specific optimizations."""
        device = self.get_device()
        
        if device.type == 'cuda':
            # CUDA optimizations
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
            # Memory optimization
            if hasattr(torch.cuda, 'empty_cache'):
                torch.cuda.empty_cache()
            
            # Set memory fraction to avoid OOM
            if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                torch.cuda.set_per_process_memory_fraction(0.8)
            
            logger.info("Applied CUDA optimizations with memory management")
        
        elif device.type == 'mps':
            # Apple Silicon optimizations
            logger.info("Apple Silicon GPU optimizations applied automatically")
        
        else:
            # CPU optimizations
            torch.set_num_threads(min(4, self._device_info['cpu']['count']))
            logger.info(f"Applied CPU optimizations (threads: {torch.get_num_threads()})")
    
    def get_optimal_batch_size(self, model_size_mb: float = 100.0) -> int:
        """Get optimal batch size for the current device."""
        device = self.get_device()
        
        if device.type == 'cuda':
            # Estimate based on available GPU memory
            memory_info = self.get_memory_info()
            if memory_info['total']:
                available_memory = memory_info['free'] / (1024**3)  # GB
                # Rough estimate: 1GB per 100MB model
                optimal_batch = max(1, int(available_memory * 10))
                return min(optimal_batch, 32)  # Cap at 32
            return 8  # Default for CUDA
        
        elif device.type == 'mps':
            return 4  # Conservative for Apple Silicon
        
        else:
            return 2  # Conservative for CPU
    
    def get_memory_info(self) -> Dict[str, Optional[int]]:
        """Get memory information for the current device."""
        device = self.get_device()
        
        if device.type == 'cuda':
            try:
                total = torch.cuda.get_device_properties(device).total_memory
                allocated = torch.cuda.memory_allocated(device)
                cached = torch.cuda.memory_reserved(device)
                free = total - allocated
                return {
                    'total': total,
                    'allocated': allocated,
                    'cached': cached,
                    'free': free
                }
            except Exception:
                return {'total': None, 'allocated': None, 'cached': None, 'free': None}
        
        elif device.type == 'mps':
            # Apple Silicon memory info not easily accessible
            return {'total': None, 'allocated': None, 'cached': None, 'free': None}
        
        else:
            # CPU memory info
            import psutil
            memory = psutil.virtual_memory()
            return {
                'total': memory.total,
                'allocated': memory.used,
                'cached': memory.cached,
                'free': memory.available
            }


# Global device manager instance
device_manager = DeviceManager.get_instance()


def get_optimal_device() -> torch.device:
    """Get the optimal device for model execution."""
    return device_manager.get_device()


def is_gpu_available() -> bool:
    """Check if GPU is available and selected."""
    return device_manager.is_gpu()


def get_device_info() -> Dict[str, Any]:
    """Get comprehensive device information."""
    return device_manager.get_device_info()
