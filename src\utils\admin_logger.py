"""
Centralized logging configuration for admin dashboard.
Provides separate log files for admin dashboard operations.
"""
import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Optional

# Create logs directory if it doesn't exist
LOGS_DIR = Path("logs")
LOGS_DIR.mkdir(exist_ok=True)

# Admin dashboard specific log file
ADMIN_LOG_FILE = LOGS_DIR / f"admin_dashboard_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log"

class AdminLoggerConfig:
    """Configuration class for admin dashboard logging."""
    
    def __init__(self):
        self.log_level = os.getenv('ADMIN_LOG_LEVEL', 'INFO').upper()
        self.max_log_files = int(os.getenv('ADMIN_MAX_LOG_FILES', '5'))
        self.enable_console_output = os.getenv('ADMIN_ENABLE_CONSOLE_LOG', 'true').lower() == 'true'
        self.enable_audit_logging = os.getenv('ENABLE_AUDIT_LOGGING', 'true').lower() == 'true'
        
    def setup_logger(self, name: str) -> logging.Logger:
        """Set up a logger with admin dashboard specific configuration."""
        logger = logging.getLogger(name)
        
        # Avoid duplicate handlers
        if logger.handlers:
            return logger
            
        logger.setLevel(getattr(logging, self.log_level))
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [ADMIN] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            ADMIN_LOG_FILE,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=self.max_log_files
        )
        file_handler.setLevel(getattr(logging, self.log_level))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Console handler (optional)
        if self.enable_console_output:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(getattr(logging, self.log_level))
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        return logger

# Global admin logger configuration
admin_logger_config = AdminLoggerConfig()

def get_admin_logger(name: str) -> logging.Logger:
    """Get a logger configured for admin dashboard operations."""
    return admin_logger_config.setup_logger(name)

def log_admin_action(user_email: str, action: str, details: Optional[str] = None, 
                    ip_address: Optional[str] = None, success: bool = True):
    """Log admin actions for audit trail."""
    if not admin_logger_config.enable_audit_logging:
        return
        
    audit_logger = get_admin_logger('admin_audit')
    
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'user': user_email,
        'action': action,
        'success': success,
        'ip_address': ip_address or 'unknown',
        'details': details or ''
    }
    
    log_message = f"AUDIT: {log_entry['user']} performed '{log_entry['action']}' " \
                 f"from {log_entry['ip_address']} - {'SUCCESS' if success else 'FAILED'}"
    
    if details:
        log_message += f" - {details}"
    
    if success:
        audit_logger.info(log_message)
    else:
        audit_logger.warning(log_message)

def log_api_request(endpoint: str, method: str, user: Optional[str] = None, 
                   response_time: Optional[float] = None, status_code: Optional[int] = None):
    """Log API requests for monitoring."""
    api_logger = get_admin_logger('admin_api')
    
    log_message = f"API: {method} {endpoint}"
    
    if user:
        log_message += f" - User: {user}"
    
    if response_time is not None:
        log_message += f" - Response time: {response_time:.3f}s"
    
    if status_code is not None:
        log_message += f" - Status: {status_code}"
    
    if status_code and status_code >= 400:
        api_logger.warning(log_message)
    else:
        api_logger.info(log_message)

def log_data_access(user: str, data_type: str, action: str, record_count: Optional[int] = None):
    """Log data access for compliance tracking."""
    data_logger = get_admin_logger('admin_data_access')
    
    log_message = f"DATA_ACCESS: {user} performed '{action}' on {data_type}"
    
    if record_count is not None:
        log_message += f" - Records affected: {record_count}"
    
    data_logger.info(log_message)

def log_security_event(event_type: str, details: str, severity: str = 'medium', 
                      user: Optional[str] = None, ip_address: Optional[str] = None):
    """Log security-related events."""
    security_logger = get_admin_logger('admin_security')
    
    log_message = f"SECURITY_{severity.upper()}: {event_type}"
    
    if user:
        log_message += f" - User: {user}"
    
    if ip_address:
        log_message += f" - IP: {ip_address}"
    
    log_message += f" - {details}"
    
    if severity.lower() in ['high', 'critical']:
        security_logger.error(log_message)
    elif severity.lower() == 'medium':
        security_logger.warning(log_message)
    else:
        security_logger.info(log_message)

# Export main functions
__all__ = [
    'get_admin_logger',
    'log_admin_action',
    'log_api_request',
    'log_data_access',
    'log_security_event',
    'ADMIN_LOG_FILE'
]
