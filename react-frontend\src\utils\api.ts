// API utility functions for the Proxima28 chatbot

export const API_BASE_URL = "http://localhost:5051";

// Health check function
export const healthCheck = async () => {
  try {
    console.log('Making health check request to:', `${API_BASE_URL}/api/health`);
    const response = await fetch(`${API_BASE_URL}/api/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    console.log('Health check response status:', response.status);
    
    if (response.ok) {
      try {
        const data = await response.json();
        console.log('Backend health check successful:', data);
        return true;
      } catch (jsonError) {
        console.error('Health check response is not JSON:', jsonError);
        // Check if it's HTML (backend error page)
        const text = await response.text();
        console.error('Response text:', text.substring(0, 200));
        return false;
      }
    } else {
      console.error('Backend health check failed:', response.status);
      const text = await response.text();
      console.error('Error response text:', text.substring(0, 200));
      return false;
    }
  } catch (error) {
    console.error('Backend health check error:', error);
    return false;
  }
};

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  console.log('Making API request to:', url);
  
  // Attach JWT token if present (from localStorage for persistence across browser sessions)
  const token = localStorage.getItem('access_token') || localStorage.getItem('token'); // Fallback for legacy
  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  } else {
    // Make sure Authorization is not set to undefined
    if ('Authorization' in defaultHeaders) {
      delete defaultHeaders['Authorization'];
    }
  }
  // Only spread headers if they are a Record<string, string>
  const extraHeaders = (options.headers && typeof options.headers === 'object') ? options.headers : {};
  const defaultOptions: RequestInit = {
    headers: {
      ...defaultHeaders,
      ...extraHeaders
    },
    credentials: 'include', // Ensure cookies/session are sent for all requests
  };

  const config = { ...defaultOptions, ...options };
  console.log('Request config:', { url, method: config.method || 'GET', headers: config.headers });

  try {
    const response = await fetch(url, config);
    console.log('Response received:', { status: response.status, ok: response.ok });

          if (!response.ok) {
        let errorMessage = `HTTP error! status: ${response.status}`;
        let errorData;
        try {
          errorData = await response.json();
          errorMessage = errorData.message || errorMessage;

          // Handle token expiration
          if (response.status === 401 && errorData && (errorData.error === 'ACCESS_TOKEN_EXPIRED')) {
            console.log('Access token expired, attempting refresh');
            // Try to refresh token silently
            try {
              const refreshResponse = await fetch(`${API_BASE_URL}/api/auth/refresh-token`, {
                method: 'POST',
                credentials: 'include', // Include cookies
                headers: {
                  'Content-Type': 'application/json',
                },
              });
              
              if (refreshResponse.ok) {
                const refreshData = await refreshResponse.json();
                // Store new access token in localStorage for persistence
                localStorage.setItem('access_token', refreshData.access_token);
                // Retry the original request
                const retryResponse = await fetch(url, {
                  ...config,
                  headers: {
                    ...config.headers,
                    'Authorization': `Bearer ${refreshData.access_token}`
                  }
                });
                if (retryResponse.ok) {
                  return await retryResponse.json();
                }
              }
            } catch (refreshError) {
              console.log('Token refresh failed, logging out user');
            }
            
            // If refresh failed, logout
            localStorage.removeItem('user');
            localStorage.removeItem('access_token');
            window.location.href = '/login';
            throw new Error('Session expired. Please log in again.');
          }
          
          if (response.status === 401 && errorData && (errorData.error === 'INVALID_ACCESS_TOKEN' || errorData.error === 'AUTH_REQUIRED')) {
            console.log('Invalid token, logging out user');
            localStorage.removeItem('user');
            localStorage.removeItem('access_token');
            window.location.href = '/login';
            throw new Error('Invalid session. Please log in again.');
          }

          // Special handling for 2FA case - return the response data instead of throwing
          if (response.status === 401 && errorData && errorData.message === "2FA code required") {
            console.log('2FA case detected, returning response data:', errorData);
            return errorData; // Return the full response data
          }
        } catch (e) {
          // If we can't parse the error response, use the default message
        }

        throw new Error(errorMessage);
      }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    console.error('Request details:', { url, config });
    // Check if it's a network error
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Network error. Please check if the server is running.');
    }
    throw error;
  }
}

// Authentication API calls
export const authAPI = {
  login: async (email: string, password: string, twoFACode?: string) => {
    return apiRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        two_fa_code: twoFACode
      }),
    });
  },

  register: async (fullName: string, email: string, password: string, employeeId?: string) => {
    return apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        full_name: fullName,
        email,
        password,
        employee_id: employeeId
      }),
    });
  },

  logout: async () => {
    return apiRequest('/api/auth/logout', {
      method: 'POST',
    });
  },

  verify2FA: async (code: string) => {
    return apiRequest('/api/auth/verify-2fa', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
  },

  get2FASetup: async (email: string) => {
    return apiRequest('/api/auth/user-2fa-setup', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  },
};

// Chat API calls
export const chatAPI = {
  sendMessage: async (message: string, files?: File[], deviceId?: string, email?: string, employeeId?: string, chatId?: string, responseMode?: string) => {
    // Compose the payload as expected by /api/chat/query
    const payload: any = {
      query: message,
      device_id: deviceId || 'web-client',
      files_info: files ? files.map((file) => ({ name: file.name, size: file.size, type: file.type })) : [],
      response_mode: responseMode || 'detailed', // Default to detailed mode
    };
    if (email) payload.email = email;
    if (employeeId) payload.employee_id = employeeId;
    if (chatId) payload.chat_id = chatId;

    return apiRequest('/api/chat/query', {
      method: 'POST',
      body: JSON.stringify(payload),
      headers: { 'Content-Type': 'application/json' },
    });
  },

  clarifyMessage: async (
    selectedText: string,
    previousMessage: string,
    clarificationQuery?: string,
    deviceId?: string,
    language?: string,
    responseMode?: string
  ) => {
    const payload = {
      selected_text: selectedText,
      previous_message: previousMessage,
      clarification_query: clarificationQuery || 'Please explain this part',
      device_id: deviceId || 'web-client',
      language: language || 'en',
      response_mode: responseMode || 'concise',
    };

    return apiRequest('/api/chat/clarify', {
      method: 'POST',
      body: JSON.stringify(payload),
      headers: { 'Content-Type': 'application/json' },
    });
  },

  generateFollowupQuestion: async (
    selectedText: string,
    fullAssistantMessage: string,
    previousUserQuery?: string,
    deviceId?: string,
    language?: string
  ) => {
    const payload = {
      selected_text: selectedText,
      full_assistant_message: fullAssistantMessage,
      previous_user_query: previousUserQuery || '',
      device_id: deviceId || 'web-client',
      language: language || 'en',
    };

    return apiRequest('/api/chat/generate-followup', {
      method: 'POST',
      body: JSON.stringify(payload),
      headers: { 'Content-Type': 'application/json' },
    });
  },

  getChatHistory: async () => {
    return apiRequest('/api/chat/chats');
  },

  deleteChat: async (chatId: string) => {
    return apiRequest(`/api/chat/chats/${chatId}`, {
      method: 'DELETE',
    });
  },
};

// File API calls
export const fileAPI = {
  upload: async (files: File[]) => {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`file_${index}`, file);
    });

    return apiRequest('/api/documents/upload-document', {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  },

  preview: async (fileId: string) => {
    return apiRequest(`/api/documents/preview/${fileId}`);
  },

  summarize: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    const token = localStorage.getItem('token');
    const headers: Record<string, string> = {};
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    const response = await fetch(`${API_BASE_URL}/api/documents/summarize-document`, {
      method: 'POST',
      body: formData,
      headers,
      credentials: 'include',
    });
    if (!response.ok) {
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {}
      throw new Error(errorMessage);
    }
    return response.json();
  },
};

// HR Escalation API calls
export const escalationAPI = {
  submit: async (escalationData: any) => {
    // Send the full object as the payload
    const token = localStorage.getItem('token');
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return fetch('http://localhost:5052/api/escalations', {
      method: 'POST',
      body: JSON.stringify(escalationData),
      headers,
      credentials: 'include',
    }).then(res => {
      if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
      return res.json();
    });
  },
  getHRPersons: async () => {
    return apiRequest('/api/admin/hr-representatives');
  },
};

// User API calls
export const userAPI = {
  updateProfile: async (userData: any) => {
    return apiRequest('/api/auth/user/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  },

  getProfile: async () => {
    return apiRequest('/api/auth/user/profile');
  },
};
