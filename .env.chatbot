# ============================================================================
# CHATBOT BACKEND ENVIRONMENT CONFIGURATION
# ============================================================================

# === Required API Keys ===
GROQ_API_KEY=********************************************************
GROQ_API_ENDPOINT=https://api.groq.com/openai/v1/chat/completions
HF_TOKEN=*************************************

# === LLM/Model Settings ===
GROQ_MODEL=llama3-8b-8192
USE_LARGE_MODEL=false

# === QDRANT VECTOR DATABASE CONFIGURATION ===
QDRANT_URL=https://ccf2ef21-7bb2-42d0-876e-7b46027fb0e4.us-west-1-0.aws.cloud.qdrant.io
QDRANT_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.dAjAUKTVUGNxUdhRAd7sBWJknBcI2SSrrlgwUsDKeRo
QDRANT_COLLECTION_NAME=VECTOR_DB

# === Performance Optimization ===
LAZY_LOAD_NER=true
LAZY_LOAD_INTENT=true
LAZY_LOAD_EMAIL=true
ENABLE_ASYNC_LOADING=true
MAX_CONCURRENT_MODEL_LOADS=3

# === Device Configuration ===
DEVICE_AUTO_SELECT=true
DEVICE=
PREFER_GPU=true
FALLBACK_CPU=true

# === Flask Application Settings ===
FLASK_ENV=production
FLASK_DEBUG=false
FLASK_SECRET_KEY=6d755e23bd160f478bb09b9cc5597800b1061600459ae3f4

# === Server Configuration ===
PORT=5051
GUNICORN_BIND=0.0.0.0:5051
GUNICORN_WORKERS=4
GUNICORN_TIMEOUT=120
GUNICORN_MAX_REQUESTS=1000

# === Logging Configuration ===
LOG_LEVEL=INFO
MAX_LOG_FILES=5
ENABLE_MODEL_LOADING_LOGS=true

# === Whisper Speech-to-Text ===
WHISPER_MODEL_CACHE_DIR=./whisper_models_cache

# === Response/Context Settings ===
DEFAULT_RESPONSE_MODE=concise
MAX_RESPONSE_WORDS=500
ENABLE_AUTO_CONCISENESS=true

# === Startup Optimization ===
PROCESS_HR_FILES=false
PRELOAD_MODELS=false
VALIDATE_MODELS=false
ENABLE_BACKGROUND_PROCESSING=true

# === Email/SMTP Settings ===
SMTP_SERVER=smtp.office365.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=Vikky@9878
SENDER_EMAIL=<EMAIL>
HR_EMAILS=true
ENABLE_EMAIL_ESCALATION=false

# === Security ===
JWT_SECRET=s8d7f6g5h4j3k2l1m0n9b8v7c6x5z4a3

# === External APIs ===
MOCKAPI_URL=https://686b5ca0e559eba908723829.mockapi.io/api/v1/leave_balance
SALARY_SLIP_URL=https://686b5ca0e559eba908723829.mockapi.io/api/v1/salary_slip

# === Database Paths ===
CONVERSATION_DB_PATH=data/conversations.db
DOCUMENT_DB_PATH=data/documents.db
USER_DB_PATH=data/users.db
SESSION_DB_PATH=data/sessions.db

# === Admin Dashboard Integration ===
ADMIN_DASHBOARD_URL=http://localhost:8000
ENABLE_ADMIN_API=true
ADMIN_API_VERSION=v1

# === Monitoring & Analytics ===
ENABLE_ANALYTICS=true
ANALYTICS_RETENTION_DAYS=90
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_TRACKING=true

# === CORS Settings ===
CORS_ORIGINS=http://localhost:3000,http://localhost:8000,http://localhost:3001
CORS_ALLOW_CREDENTIALS=true

# === Rate Limiting ===
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# === Cache Settings ===
ENABLE_RESPONSE_CACHE=true
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE=1000
