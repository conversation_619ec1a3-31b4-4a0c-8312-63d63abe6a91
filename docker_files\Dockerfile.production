# Multi-stage production Dockerfile for Multi-Model RAG Chatbot
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy and install requirements
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r rag-chatbot && \
    useradd -r -g rag-chatbot -d /app rag-chatbot

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Set working directory
WORKDIR /app

# Copy application code
COPY --chown=rag-chatbot:rag-chatbot src/ ./src/
COPY --chown=rag-chatbot:rag-chatbot app/ ./app/
COPY --chown=rag-chatbot:rag-chatbot wsgi.py ./
COPY --chown=rag-chatbot:rag-chatbot gunicorn.conf.py ./
COPY --chown=rag-chatbot:rag-chatbot start_production.py ./

# Create necessary directories with proper permissions
RUN mkdir -p data/raw data/processed data/models_cache logs && \
    chown -R rag-chatbot:rag-chatbot /app

# Set environment variables
ENV PYTHONPATH=/app
ENV FLASK_ENV=production
ENV LOG_LEVEL=WARNING
ENV DEVICE_AUTO_SELECT=true
ENV PREFER_GPU=true
ENV FALLBACK_CPU=true

# Switch to non-root user
USER rag-chatbot

# Expose port
EXPOSE 5051

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5051/health || exit 1

# Run with Gunicorn
CMD ["gunicorn", "--config", "gunicorn.conf.py", "wsgi:app"]
