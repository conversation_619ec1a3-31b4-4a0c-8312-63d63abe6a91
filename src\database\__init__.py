"""
Database package initialization.
"""
from .user_db import UserModel, ConversationModel, DocumentModel, ConversationDatabase, DocumentDatabase, UserDatabase
from .conversation_store import ConversationStore
from .vector_store import QdrantVectorStore
from .session_db import SessionModel, SessionDatabase
from .payroll_db import PayrollDatabase, PayrollDocument, ParsedField, DetectedIssue
from ..config import CONVERSATION_DB_PATH, DOCUMENT_DB_PATH, USER_DB_PATH, ADMIN_USERS_DB_PATH
from ..utils.logger import get_logger

__all__ = [
    'UserModel',
    'ConversationModel',
    'DocumentModel',
    'ConversationStore',
    'QdrantVectorStore',
    'SessionModel',
    'PayrollDatabase',
    'PayrollDocument',
    'ParsedField',
    'DetectedIssue',
    'initialize_database_tables'
]

logger = get_logger(__name__)

# Global flag to prevent duplicate initialization
_database_tables_initialized = False

def initialize_database_tables():
    """
    Centralized database table initialization function.
    Ensures all tables are created only once during application startup.
    """
    global _database_tables_initialized
    
    if _database_tables_initialized:
        logger.debug("Database tables already initialized, skipping...")
        return
    
    logger.info("Initializing database tables...")
    
    try:
        # Initialize all database tables in parallel
        conversation_db = ConversationDatabase(CONVERSATION_DB_PATH)
        document_db = DocumentDatabase(DOCUMENT_DB_PATH)
        user_db = UserDatabase(USER_DB_PATH)
        session_db = SessionDatabase(USER_DB_PATH)
        
        # Initialize payroll database if needed
        try:
            payroll_db = PayrollDatabase()
            logger.info("Payroll database initialized successfully")
        except Exception as e:
            logger.warning(f"Payroll database initialization failed: {e}")
        
        _database_tables_initialized = True
        logger.info("All database tables initialized successfully")
        
    except Exception as e:
        logger.error(f"Database table initialization failed: {e}")
        raise
