"""
Configuration settings for the Advanced RAG Chatbot.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Base directories
BASE_DIR = Path(__file__).resolve().parent.parent
DATA_DIR = BASE_DIR / "data"
PROCESSED_DIR = DATA_DIR / "processed"
RAW_DIR = DATA_DIR / "raw_files"
DB_DIR = DATA_DIR / "db"
MODELS_DIR = DATA_DIR / "models"
DOCUMENT_DB_PATH= DB_DIR /"documents.db"
CONVERSATION_DB_PATH= DB_DIR /"convo.db"
USER_DB_PATH= DB_DIR /"users.db"
ADMIN_USERS_DB_PATH = Path("admin_dashboard/data/admin_users.db")
TRAINING_DATA_PATH = DATA_DIR / "training" / "intent_training_data.jsonl"
CALIBRATION_PATH = DATA_DIR /"CALIBRATION" / "intent_calibration_stats.json"


# Create directories if they don't exist
for directory in [DATA_DIR, PROCESSED_DIR, RAW_DIR, DB_DIR, MODELS_DIR]:
    directory.mkdir(exist_ok=True, parents=True)

# Database settings
DATABASE_PATH = DB_DIR / "chatbot.db"
SIMILARITY_THRESHOLD = 0.4
MAX_VECTOR_SEARCH_TOP_K = 20

# ======== OPTIMIZED MODEL SETTINGS ========
# Use single model for multiple purposes to reduce loading time
UNIFIED_MODEL_NAME = EMBEDDING_MODEL_NAME = INTENT_MODEL_NAME = "data/models/intent_classifier"
EMBEDDING_MODEL_NAME = "data/models/intent_classifier"

# Fallback to larger model if needed (configurable)
USE_LARGE_MODEL = os.getenv("USE_LARGE_MODEL", "false").lower() == "true"
if USE_LARGE_MODEL:
    EMBEDDING_MODEL_NAME = "data/models_cache/bge-base-en-v1.5"

# Intent classifier model paths
INTENT_CLASSIFIER_MODEL_DIR = DATA_DIR / "models" / "intent_classifier"
INTENT_CLASSIFIER_MODEL_PATH = INTENT_CLASSIFIER_MODEL_DIR / "model.safetensors"
INTENT_CLASSIFIER_CONFIG_PATH = INTENT_CLASSIFIER_MODEL_DIR / "config.json"
INTENT_CLASSIFIER_TOKENIZER_PATH = INTENT_CLASSIFIER_MODEL_DIR / "tokenizer.json"

# Embedding model paths
EMBEDDING_MODEL_DIR = DATA_DIR / "models_cache" / "bge-base-en-v1.5"
EMBEDDING_TOKENIZER_DIR = DATA_DIR / "models_cache" / "bge-base-en-v1.5"

# Create model directories
INTENT_CLASSIFIER_MODEL_DIR.mkdir(parents=True, exist_ok=True)
EMBEDDING_MODEL_DIR.mkdir(parents=True, exist_ok=True)


# LLM Parameters (for ChatGroq)
LLM_DEFAULT_TEMPERATURE = 0.7  # Controls randomness. Lower is more deterministic.
LLM_TOP_P = 1.0                # Nucleus sampling. 1.0 means all tokens are considered.
LLM_FREQUENCY_PENALTY = 0.0    # Penalizes new tokens based on their existing frequency in the text so far.
LLM_PRESENCE_PENALTY = 0.0     # Penalizes new tokens based on whether they appear in the text so far.
LLM_MODEL_NAME = os.getenv("GROQ_MODEL", "llama3-8b-8192")

# ======== PERFORMANCE OPTIMIZATION SETTINGS ========
# Lazy loading configuration
LAZY_LOADING = {
    'ner_model': os.getenv("LAZY_LOAD_NER", "true").lower() == "true",
    'intent_classifier': os.getenv("LAZY_LOAD_INTENT", "true").lower() == "true",
    'email_service': os.getenv("LAZY_LOAD_EMAIL", "true").lower() == "true",
    'embedding_dimension_detection': os.getenv("LAZY_LOAD_EMBEDDING_DIM", "true").lower() == "true"
}

# Intent classifier settings
INTENT_CLASSIFIER_ENABLED = os.getenv("INTENT_CLASSIFIER_ENABLED", "true").lower() == "true"
INTENT_CLASSIFIER_TIMEOUT = int(os.getenv("INTENT_CLASSIFIER_TIMEOUT", "20"))  # seconds
INTENT_CLASSIFIER_FALLBACK_ONLY = os.getenv("INTENT_CLASSIFIER_FALLBACK_ONLY", "false").lower() == "true"

# Model sharing between services
ENABLE_MODEL_SHARING = True

# Device selection configuration
DEVICE_CONFIG = {
    'auto_select': os.getenv("DEVICE_AUTO_SELECT", "true").lower() == "true",
    'force_device': os.getenv("DEVICE", "").lower(),  # "cuda", "cpu", "mps", or empty for auto
    'prefer_gpu': os.getenv("PREFER_GPU", "true").lower() == "true",
    'fallback_cpu': os.getenv("FALLBACK_CPU", "true").lower() == "true"
}

# Environment-based configuration
ENVIRONMENT = os.getenv("FLASK_ENV", "development").lower()
IS_DEVELOPMENT = ENVIRONMENT == "development"
IS_PRODUCTION = ENVIRONMENT == "production"

# Development vs Production logging
DEV_LOGGING = {
    'decorative_banners': IS_DEVELOPMENT,  # Only show decorative banners in development
    'verbose_startup': IS_DEVELOPMENT,     # Detailed startup logging in development
    'performance_metrics': IS_DEVELOPMENT, # Performance metrics in development
    'minimal_production': IS_PRODUCTION    # Minimal logging in production
}

# Qdrant optimization settings
QDRANT_OPTIMIZATION = {
    'batch_metadata_fetch': True,  # Combine multiple HTTP calls into one
    'connection_pooling': True,    # Enable connection pooling
    'health_check_interval': 300   # Health check every 5 minutes
}

# Text-to-Speech Model Configuration
TTS_DEVICE = "cpu"
TTS_MODEL_NAME = "myshell-ai/MeloTTS"
AUDIO_PLAYBACK_RATE = 44100

# Async loading settings
ENABLE_ASYNC_LOADING = os.getenv("ENABLE_ASYNC_LOADING", "true").lower() == "true"
MAX_CONCURRENT_MODEL_LOADS = 3

# ======== VECTOR SEARCH SETTINGS ========
VECTOR_DIMENSION = 768
SIMILARITY_THRESHOLD = 0.6
MAX_CONTEXT_DOCUMENTS = 5

# Context Builder Settings
CONTEXT_BUILDER_MAX_RETRIES = 3

# Conversation settings
MAX_HISTORY_MESSAGES = 30

# WHISPER SPEECH TO TEXT SETTINGS
WHISPER_MODEL_NAME = "small" # Use small model for faster processing
WHISPER_MODEL_DIR = Path("data/models_cache/whisper_model")
SPEECH_RECOGNITION_DURATION = 10
SPEECH_SAMPLE_RATE = 16000

# ======== API KEYS ========
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GROQ_API_KEY:
    raise ValueError("❌ GROQ_API_KEY is not set in the environment or .env file.")


# ======== ML MODEL SETTINGS ========
INTENT_CLASSIFIER_CONFIDENCE_THRESHOLD = 0.6
NER_CONFIDENCE_THRESHOLD = 0.7

# ======== DEBUG SETTINGS ========
DEBUG = os.getenv("DEBUG", "false").lower() == "true"
ENABLE_CONTEXT_DEBUG = os.getenv("ENABLE_CONTEXT_DEBUG", "false").lower() == "true"

# ======== RESPONSE SETTINGS ========
DEFAULT_RESPONSE_MODE = os.getenv("DEFAULT_RESPONSE_MODE", "concise")  # concise, detailed, auto
MAX_RESPONSE_WORDS = int(os.getenv("MAX_RESPONSE_WORDS", "500"))
ENABLE_AUTO_CONCISENESS = os.getenv("ENABLE_AUTO_CONCISENESS", "true").lower() == "true"

# Per-response-mode max_tokens (for strict token budgeting in RAG)
MAX_TOKENS_DETAILED = int(os.getenv("MAX_TOKENS_DETAILED", "750"))  # Max output tokens for detailed mode
MAX_TOKENS_CONCISE = int(os.getenv("MAX_TOKENS_CONCISE", "250"))   # Max output tokens for concise mode
MAX_TOKENS_PER_MODE = {
    "detailed": MAX_TOKENS_DETAILED,
    "concise": MAX_TOKENS_CONCISE,
}

# ======== STARTUP OPTIMIZATION ========
# Skip heavy operations on startup
PROCESS_HR_FILES_ON_STARTUP = os.getenv("PROCESS_HR_FILES", "false").lower() == "true"
PRELOAD_MODELS_ON_STARTUP = os.getenv("PRELOAD_MODELS", "false").lower() == "true"
VALIDATE_MODELS_ON_STARTUP = os.getenv("VALIDATE_MODELS", "false").lower() == "true"

# Background processing
ENABLE_BACKGROUND_PROCESSING = os.getenv("ENABLE_BACKGROUND_PROCESSING", "true").lower() == "true"

# ======== DOCUMENT SETTINGS ========
MAX_DOCUMENT_VERSIONS = 5
DOCUMENT_BACKUP_DIR = DATA_DIR / "backups"
AUTO_REINDEX_ON_UPDATE = True
# ======== UI SETTINGS ========
DEFAULT_THEME = "light"


# HR escalation settings
HR_EMAILS = os.getenv("HR_EMAILS", "").split(",") if os.getenv("HR_EMAILS") else []
ENABLE_EMAIL_ESCALATION = os.getenv("ENABLE_EMAIL_ESCALATION", "false").lower() == "true"

# ======== LOGGING CONFIGURATION ========
LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG")  # Changed from INFO to DEBUG
ENABLE_MODEL_LOADING_LOGS = os.getenv("ENABLE_MODEL_LOADING_LOGS", "true").lower() == "true"

# ======== DEVELOPMENT SETTINGS ========
DEBUG_MODE = os.getenv("FLASK_DEBUG", "false").lower() == "true"
SKIP_MODEL_VALIDATION = os.getenv("SKIP_MODEL_VALIDATION", "false").lower() == "true"
MOCKAPI_URL = os.getenv("MOCKAPI_URL", "http://localhost:5005/mockapi")