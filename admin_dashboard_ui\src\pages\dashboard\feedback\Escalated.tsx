import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { authFetch } from "@/utils/api";
import { User, MessageSquare, Clock, AlertTriangle, CheckCircle } from "lucide-react";

const ESCALATION_TYPES = ["All", "Policy", "Technical", "HR", "Other"];
const STATUS = ["All", "Pending", "Assigned", "Replied", "Resolved"];
const HR_TEAM = [
  { email: "<EMAIL>", name: "<PERSON>" },
  { email: "<EMAIL>", name: "Suman" },
  { email: "<EMAIL>", name: "Chandra Shekar" },
  { email: "<EMAIL>", name: "Lavanya" },
];

const EscalatedChats = () => {
  const [escalationType, setEscalationType] = useState("All");
  const [status, setStatus] = useState("All");
  const [escalations, setEscalations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEscalation, setSelectedEscalation] = useState<any>(null);
  const [showReplyDialog, setShowReplyDialog] = useState(false);
  const [replyText, setReplyText] = useState("");
  const [assignTo, setAssignTo] = useState("");

  const fetchEscalations = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await authFetch('/api/escalations');
      if (response.ok) {
        const data = await response.json();
        let filteredData = data.escalations || [];
        
        // Filter by type
        if (escalationType !== "All") {
          filteredData = filteredData.filter((esc: any) => 
            esc.issue_type?.toLowerCase() === escalationType.toLowerCase()
          );
        }
        
        // Filter by status
        if (status !== "All") {
          filteredData = filteredData.filter((esc: any) => 
            esc.status === status.toLowerCase()
          );
        }
        
        setEscalations(filteredData);
      } else {
        setError("Failed to load escalations");
      }
    } catch (err) {
      setError("Failed to load escalations");
    } finally {
      setLoading(false);
    }
  }, [escalationType, status]);

  useEffect(() => {
    fetchEscalations();
  }, [fetchEscalations]);

  const handleAssignEscalation = async (escalationId: number, hrEmail: string) => {
    try {
      const response = await authFetch(`/api/escalations/${escalationId}/assign`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          assigned_to: hrEmail,
          assigned_by: localStorage.getItem('user_email') || 'admin'
        })
      });
      
      if (response.ok) {
        fetchEscalations(); // Refresh the list
      } else {
        setError("Failed to assign escalation");
      }
    } catch (err) {
      setError("Failed to assign escalation");
    }
  };

  const handleReply = async () => {
    if (!selectedEscalation || !replyText.trim()) return;
    
    try {
      const response = await authFetch(`/api/escalations/${selectedEscalation.id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reply: replyText,
          replied_by: localStorage.getItem('user_email') || 'admin',
          reply_type: 'hr'
        })
      });
      
      if (response.ok) {
        setShowReplyDialog(false);
        setReplyText("");
        setSelectedEscalation(null);
        fetchEscalations(); // Refresh the list
      } else {
        setError("Failed to send reply");
      }
    } catch (err) {
      setError("Failed to send reply");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", icon: Clock },
      assigned: { color: "bg-blue-100 text-blue-800", icon: User },
      replied: { color: "bg-green-100 text-green-800", icon: MessageSquare },
      resolved: { color: "bg-green-100 text-green-800", icon: CheckCircle }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {status}
      </Badge>
    );
  };

  const handleExportCSV = () => {
    alert("Export to CSV (mock)");
  };
  
  const handleExportPDF = () => {
    alert("Export to PDF (mock)");
  };

  return (
    <div className="max-w-6xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>Escalated Issues</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {STATUS.map(opt => (
                  <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={escalationType} onValueChange={setEscalationType}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                {ESCALATION_TYPES.map(opt => (
                  <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button size="sm" variant="outline" onClick={handleExportCSV}>Export CSV</Button>
            <Button size="sm" variant="outline" onClick={handleExportPDF}>Export PDF</Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-80 w-full" />
          ) : error ? (
            <div className="text-red-500 p-4">{error}</div>
          ) : !escalations.length ? (
            <div className="text-muted-foreground p-4">No escalations available.</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
                <thead className="bg-muted dark:bg-zinc-800">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">ID</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">User</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Issue Type</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Priority</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Status</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Assigned To</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Created</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-background dark:bg-zinc-900">
                  {escalations.map((escalation) => {
                    const userInfo = escalation.user_json ? JSON.parse(escalation.user_json) : {};
                    return (
                      <tr key={escalation.id} className="border-b border-border dark:border-zinc-800">
                        <td className="px-4 py-2 text-sm">#{escalation.id}</td>
                        <td className="px-4 py-2 text-sm">
                          {userInfo.email || 'Anonymous'}
                        </td>
                        <td className="px-4 py-2 text-sm">{escalation.issue_type}</td>
                        <td className="px-4 py-2 text-sm">
                          <Badge variant={escalation.priority === 'urgent' ? 'destructive' : 'secondary'}>
                            {escalation.priority}
                          </Badge>
                        </td>
                        <td className="px-4 py-2 text-sm">
                          {getStatusBadge(escalation.status || 'pending')}
                        </td>
                        <td className="px-4 py-2 text-sm">
                          {escalation.assigned_to || 'Unassigned'}
                        </td>
                        <td className="px-4 py-2 text-sm">
                          {new Date(escalation.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-2 text-sm">
                          <div className="flex gap-2">
                            {!escalation.assigned_to && (
                              <Select value={assignTo} onValueChange={(value) => {
                                setAssignTo(value);
                                handleAssignEscalation(escalation.id, value);
                              }}>
                                <SelectTrigger className="w-32">
                                  <SelectValue placeholder="Assign" />
                                </SelectTrigger>
                                <SelectContent>
                                  {HR_TEAM.map(hr => (
                                    <SelectItem key={hr.email} value={hr.email}>
                                      {hr.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                            <Button 
                              size="sm" 
                              onClick={() => {
                                setSelectedEscalation(escalation);
                                setShowReplyDialog(true);
                              }}
                            >
                              Reply
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reply Dialog */}
      <Dialog open={showReplyDialog} onOpenChange={setShowReplyDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Reply to Escalation #{selectedEscalation?.id}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Issue Description</label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                {selectedEscalation?.issue_description}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium">Your Reply</label>
              <Textarea
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder="Enter your reply to the employee..."
                rows={4}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowReplyDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleReply}>
                Send Reply
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EscalatedChats; 