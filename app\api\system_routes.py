"""
System routes for the HR Assistant Chatbot.
Handles system status, health checks, and administrative functions.
"""
import time
from datetime import datetime
from pathlib import Path
from flask import Blueprint, jsonify, current_app, request

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from src.utils.api_status import APIStatusChecker
from app.middleware.error_handler import APIError

logger = get_logger(__name__)

# Create blueprint
system_bp = Blueprint('system', __name__)


@system_bp.route('/health', methods=['GET'])
def health_check():
    """
    Basic health check endpoint.
    Returns simple status for load balancers and monitoring.
    """
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "message": "Backend is running"
    })


@system_bp.route('/health/detailed', methods=['GET'])
def detailed_health_check():
    """
    Detailed health check with service status.
    Returns comprehensive health information for all services.
    """
    try:
        service_manager = ServiceManager.get_instance()
        health_status = service_manager.health_check()
        
        # Add additional system information
        health_status.update({
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",  # Could be loaded from config
            "environment": current_app.config.get('ENV', 'unknown')
        })
        
        return jsonify(health_status)
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise APIError(
            message="Health check failed",
            status_code=503,
            details=str(e)
        )


@system_bp.route('/health/documents', methods=['GET'])
def document_health_check():
    """
    Check document processing and vector database health.
    Returns status of document processing pipeline and vector storage.
    """
    try:
        service_manager = ServiceManager.get_instance()
        
        # Get document processing status
        raw_files = len(list(Path(current_app.root_path).glob("data/raw/*")))
        processed_files = len(list(Path(current_app.root_path).glob("data/processed/*")))
        
        # Get vector database status
        chain_builder = service_manager.get_chain_builder()
        vector_count = 0
        if hasattr(chain_builder, 'get_vector_database_count'):
            try:
                vector_count = chain_builder.get_vector_database_count()
            except Exception as e:
                logger.warning(f"Failed to get vector database count: {e}")
        
        processing_healthy = processed_files > 0 and vector_count > 0
        
        health_data = {
            "success": True,
            "raw_files": raw_files,
            "processed_files": processed_files,
            "vector_embeddings": vector_count,
            "processing_healthy": processing_healthy,
            "timestamp": datetime.now().isoformat()
        }
        
        # Add recommendations if not healthy
        if not processing_healthy:
            health_data["recommendations"] = [
                "Upload documents if none are processed",
                "Check document processing logs if embeddings are 0",
                "Restart application if processing appears stuck"
            ]
        
        return jsonify(health_data)
        
    except Exception as e:
        logger.error(f"Document health check failed: {e}")
        raise APIError(
            message="Document health check failed",
            status_code=500,
            details=str(e)
        )


@system_bp.route('/status/<filename>', methods=['GET'])
def file_status(filename):
    """
    Check if a specific file has been processed.
    
    Args:
        filename: Name of the file to check
        
    Returns:
        JSON with file processing status
    """
    try:
        processed_dir = Path(current_app.root_path) / "data" / "processed"
        marker_file = processed_dir / f"{filename}.processed_raw.json"
        exists = marker_file.exists()
        
        return jsonify({
            "filename": filename,
            "ready_for_query": exists,
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"File status check failed for {filename}: {e}")
        raise APIError(
            message=f"Failed to check status for file: {filename}",
            status_code=500,
            details=str(e)
        )


@system_bp.route('/llm-status', methods=['GET'])
def llm_status():
    """
    Check LLM API health and recent error status.
    Returns status of external LLM service connectivity.
    """
    try:
        checker = APIStatusChecker()
        status = checker.check_groq_status()
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"LLM status check failed: {e}")
        raise APIError(
            message="LLM status check failed",
            status_code=500,
            details=str(e)
        )


@system_bp.route('/metrics/system', methods=['GET'])
def system_metrics():
    """
    Get basic system metrics and performance information.
    Returns system resource usage and performance metrics.
    """
    try:
        import psutil
        import os
        
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Get process metrics
        process = psutil.Process(os.getpid())
        process_memory = process.memory_info()
        
        metrics = {
            "system": {
                "cpu_percent": cpu_percent,
                "memory_total_gb": round(memory.total / (1024**3), 2),
                "memory_used_gb": round(memory.used / (1024**3), 2),
                "memory_percent": memory.percent,
                "disk_total_gb": round(disk.total / (1024**3), 2),
                "disk_used_gb": round(disk.used / (1024**3), 2),
                "disk_percent": round((disk.used / disk.total) * 100, 2)
            },
            "process": {
                "memory_rss_mb": round(process_memory.rss / (1024**2), 2),
                "memory_vms_mb": round(process_memory.vms / (1024**2), 2),
                "cpu_percent": process.cpu_percent(),
                "num_threads": process.num_threads()
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return jsonify(metrics)
        
    except ImportError:
        # psutil not available
        return jsonify({
            "error": "System metrics not available - psutil not installed",
            "timestamp": datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"System metrics collection failed: {e}")
        raise APIError(
            message="Failed to collect system metrics",
            status_code=500,
            details=str(e)
        )


@system_bp.route('/info', methods=['GET'])
def system_info():
    """
    Get general system information.
    Returns basic application and environment information.
    """
    try:
        import platform
        import sys
        
        info = {
            "application": {
                "name": "HR Assistant Chatbot",
                "version": "1.0.0",
                "environment": current_app.config.get('ENV', 'unknown'),
                "debug": current_app.debug
            },
            "system": {
                "platform": platform.platform(),
                "python_version": sys.version,
                "architecture": platform.architecture()[0]
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return jsonify(info)
        
    except Exception as e:
        logger.error(f"System info collection failed: {e}")
        raise APIError(
            message="Failed to collect system information",
            status_code=500,
            details=str(e)
        )
