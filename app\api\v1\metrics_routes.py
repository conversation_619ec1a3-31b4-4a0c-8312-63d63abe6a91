"""
Metrics API routes for admin dashboard.
Provides detailed performance and system metrics.
"""
import os
import psutil
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from typing import Dict, Any, List

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from src.database.conversation_store import ConversationStore
from src.database.user_db import ConversationModel
from src.database.session_db import SessionModel

logger = get_logger(__name__)

# Create blueprint
metrics_v1_bp = Blueprint('metrics_v1', __name__)

def get_date_range(time_range: str = "7d") -> tuple:
    """Get start and end dates based on time range parameter."""
    end_date = datetime.now()
    
    if time_range == "1d":
        start_date = end_date - timedelta(days=1)
    elif time_range == "7d":
        start_date = end_date - timedelta(days=7)
    elif time_range == "30d":
        start_date = end_date - timedelta(days=30)
    elif time_range == "90d":
        start_date = end_date - timedelta(days=90)
    else:
        start_date = end_date - timedelta(days=7)
    
    return start_date, end_date

@metrics_v1_bp.route('/chatbot', methods=['GET'])
def get_chatbot_metrics():
    """Get comprehensive chatbot performance metrics."""
    try:
        time_range = request.args.get('time_range', '7d')
        start_date, end_date = get_date_range(time_range)
        
        conversation_store = ConversationStore()
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=10000
        )
        
        # Calculate metrics
        total_queries = len(conversations)
        unique_devices = len(set(conv.get('device_id', '') for conv in conversations if conv.get('device_id')))
        
        # Response time analysis
        response_times = []
        for conv in conversations:
            if conv.get('query_timestamp') and conv.get('response_timestamp'):
                try:
                    query_time = float(conv['query_timestamp'])
                    response_time = float(conv['response_timestamp'])
                    duration = response_time - query_time
                    if duration > 0:
                        response_times.append(duration)
                except (ValueError, TypeError):
                    pass
        
        avg_response_time = sum(response_times) / len(response_times) if response_times else 2.5
        
        # Intent analysis
        intents = {}
        for conv in conversations:
            intent = conv.get('intent', 'unknown')
            intents[intent] = intents.get(intent, 0) + 1
        
        top_intents = [
            {
                "intent": intent,
                "count": count,
                "confidence_avg": 0.85,
                "success_rate": 0.92
            }
            for intent, count in sorted(intents.items(), key=lambda x: x[1], reverse=True)[:10]
        ]
        
        # Mock additional metrics (in production, these would come from actual monitoring)
        metrics = {
            "total_queries": total_queries,
            "active_users": unique_devices,
            "avg_sentiment": 0.72,
            "unique_questions": len(set(conv.get('user_query', '') for conv in conversations)),
            "avg_chat_duration": round(avg_response_time, 2),
            "resolution_rate": 0.94,
            "escalation_rate": 0.06,
            "user_satisfaction": 4.3,
            "top_intents": top_intents,
            "response_times": [
                {
                    "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                    "avg_response_time": round(avg_response_time + (i * 0.05), 2),
                    "p95_response_time": round(avg_response_time * 1.5, 2),
                    "p99_response_time": round(avg_response_time * 2.0, 2)
                }
                for i in range(24)
            ][::-1],
            "error_rate": 0.02,
            "uptime_percentage": 99.8,
            "model_accuracy": 0.89,
            "intent_classification_accuracy": 0.91,
            "entity_extraction_accuracy": 0.87,
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(metrics)
        
    except Exception as e:
        logger.error(f"Error getting chatbot metrics: {e}")
        raise APIError(
            message="Failed to retrieve chatbot metrics",
            status_code=500,
            details=str(e)
        )

@metrics_v1_bp.route('/performance', methods=['GET'])
def get_performance_metrics():
    """Get system performance metrics."""
    try:
        time_range = request.args.get('time_range', '7d')
        
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Mock historical performance data
        performance_data = []
        for i in range(24):  # Last 24 hours
            timestamp = datetime.now() - timedelta(hours=i)
            performance_data.append({
                "timestamp": timestamp.isoformat(),
                "cpu_usage": round(cpu_percent + (i * 0.5) % 20, 1),
                "memory_usage": round(memory.percent + (i * 0.3) % 15, 1),
                "disk_usage": round(disk.percent, 1),
                "response_time": round(2.5 + (i * 0.1) % 3, 2),
                "requests_per_minute": 45 + (i * 2) % 30,
                "error_rate": round(0.02 + (i * 0.001) % 0.05, 3)
            })
        
        performance_data.reverse()  # Oldest to newest
        
        metrics = {
            "current": {
                "cpu_usage": cpu_percent,
                "memory_usage": memory.percent,
                "memory_available": round(memory.available / (1024**3), 2),  # GB
                "disk_usage": disk.percent,
                "disk_free": round(disk.free / (1024**3), 2),  # GB
                "uptime": "5d 12h 30m",  # Mock uptime
                "active_connections": 23,  # Mock connections
                "requests_per_minute": 52
            },
            "historical": performance_data,
            "averages": {
                "avg_cpu_usage": round(sum(p["cpu_usage"] for p in performance_data) / len(performance_data), 1),
                "avg_memory_usage": round(sum(p["memory_usage"] for p in performance_data) / len(performance_data), 1),
                "avg_response_time": round(sum(p["response_time"] for p in performance_data) / len(performance_data), 2),
                "avg_requests_per_minute": round(sum(p["requests_per_minute"] for p in performance_data) / len(performance_data), 0)
            },
            "alerts": [
                {
                    "type": "warning",
                    "message": "Memory usage above 80%",
                    "timestamp": (datetime.now() - timedelta(minutes=15)).isoformat(),
                    "resolved": True
                }
            ] if memory.percent > 80 else [],
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(metrics)
        
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise APIError(
            message="Failed to retrieve performance metrics",
            status_code=500,
            details=str(e)
        )

@metrics_v1_bp.route('/engagement', methods=['GET'])
def get_engagement_metrics():
    """Get user engagement metrics."""
    try:
        time_range = request.args.get('time_range', '7d')
        start_date, end_date = get_date_range(time_range)
        
        conversation_store = ConversationStore()
        session_model = SessionModel()
        
        # Get conversation data
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=10000
        )
        
        # Get session data
        sessions = session_model.get_recent_sessions(limit=1000)
        
        # Calculate engagement metrics
        unique_users = len(set(conv.get('device_id', '') for conv in conversations if conv.get('device_id')))
        total_sessions = len(sessions)
        
        # Calculate session durations and message counts
        session_durations = []
        messages_per_session = {}
        
        for conv in conversations:
            chat_id = conv.get('chat_id', '')
            if chat_id:
                if chat_id not in messages_per_session:
                    messages_per_session[chat_id] = 0
                messages_per_session[chat_id] += 1
        
        avg_messages_per_session = sum(messages_per_session.values()) / len(messages_per_session) if messages_per_session else 0
        
        # Mock additional engagement data
        engagement_data = {
            "total_users": unique_users,
            "active_users_24h": max(1, unique_users // 3),
            "active_users_7d": unique_users,
            "total_sessions": total_sessions,
            "avg_session_duration": 8.5,  # minutes
            "avg_messages_per_session": round(avg_messages_per_session, 1),
            "bounce_rate": 0.15,  # 15% bounce rate
            "return_user_rate": 0.68,  # 68% return users
            "user_satisfaction": 4.2,
            "engagement_trends": [
                {
                    "date": (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'),
                    "active_users": max(1, unique_users - i * 2),
                    "sessions": max(1, total_sessions - i * 5),
                    "avg_session_duration": round(8.5 + (i * 0.2), 1)
                }
                for i in range(7)
            ][::-1],
            "user_journey": {
                "new_users": round(unique_users * 0.3),
                "returning_users": round(unique_users * 0.7),
                "power_users": round(unique_users * 0.1)  # Users with >10 sessions
            },
            "popular_times": [
                {"hour": i, "activity_level": max(10, 50 + (i - 12) ** 2 // 4)}
                for i in range(24)
            ],
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(engagement_data)
        
    except Exception as e:
        logger.error(f"Error getting engagement metrics: {e}")
        raise APIError(
            message="Failed to retrieve engagement metrics",
            status_code=500,
            details=str(e)
        )

@metrics_v1_bp.route('/sentiment', methods=['GET'])
def get_sentiment_metrics():
    """Get sentiment analysis metrics."""
    try:
        time_range = request.args.get('time_range', '7d')
        start_date, end_date = get_date_range(time_range)
        
        conversation_store = ConversationStore()
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=10000
        )
        
        # Mock sentiment analysis (in production, this would use actual sentiment analysis)
        import random
        
        sentiment_data = []
        sentiment_distribution = {"positive": 0, "neutral": 0, "negative": 0}
        
        for conv in conversations:
            # Generate realistic sentiment scores
            sentiment_score = random.uniform(0.2, 0.9)
            
            if sentiment_score > 0.6:
                sentiment = "positive"
            elif sentiment_score > 0.4:
                sentiment = "neutral"
            else:
                sentiment = "negative"
            
            sentiment_distribution[sentiment] += 1
            
            if conv.get('query_timestamp'):
                try:
                    timestamp = datetime.fromtimestamp(float(conv['query_timestamp']))
                    sentiment_data.append({
                        "timestamp": timestamp.isoformat(),
                        "sentiment": sentiment,
                        "score": round(sentiment_score, 2),
                        "query": conv.get('user_query', '')[:100] + "..." if len(conv.get('user_query', '')) > 100 else conv.get('user_query', '')
                    })
                except (ValueError, TypeError):
                    pass
        
        # Calculate averages
        total_conversations = len(conversations)
        avg_sentiment = sum(s["score"] for s in sentiment_data) / len(sentiment_data) if sentiment_data else 0.5
        
        # Generate daily sentiment trends
        daily_sentiment = {}
        for item in sentiment_data:
            date_key = item["timestamp"][:10]  # YYYY-MM-DD
            if date_key not in daily_sentiment:
                daily_sentiment[date_key] = {"positive": 0, "neutral": 0, "negative": 0, "total": 0}
            daily_sentiment[date_key][item["sentiment"]] += 1
            daily_sentiment[date_key]["total"] += 1
        
        sentiment_trends = []
        for date_key in sorted(daily_sentiment.keys()):
            data = daily_sentiment[date_key]
            sentiment_trends.append({
                "date": date_key,
                "positive": round((data["positive"] / data["total"]) * 100, 1) if data["total"] > 0 else 0,
                "neutral": round((data["neutral"] / data["total"]) * 100, 1) if data["total"] > 0 else 0,
                "negative": round((data["negative"] / data["total"]) * 100, 1) if data["total"] > 0 else 0,
                "avg_score": round(avg_sentiment, 2)
            })
        
        metrics = {
            "overall_sentiment": {
                "average_score": round(avg_sentiment, 2),
                "total_analyzed": total_conversations,
                "distribution": {
                    "positive": round((sentiment_distribution["positive"] / total_conversations) * 100, 1) if total_conversations > 0 else 0,
                    "neutral": round((sentiment_distribution["neutral"] / total_conversations) * 100, 1) if total_conversations > 0 else 0,
                    "negative": round((sentiment_distribution["negative"] / total_conversations) * 100, 1) if total_conversations > 0 else 0
                }
            },
            "trends": sentiment_trends,
            "recent_feedback": sentiment_data[-20:] if len(sentiment_data) > 20 else sentiment_data,  # Last 20 items
            "alerts": [
                {
                    "type": "warning",
                    "message": "Negative sentiment spike detected",
                    "timestamp": (datetime.now() - timedelta(hours=2)).isoformat(),
                    "details": "15% increase in negative sentiment in the last 2 hours"
                }
            ] if sentiment_distribution["negative"] / total_conversations > 0.2 else [],
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(metrics)
        
    except Exception as e:
        logger.error(f"Error getting sentiment metrics: {e}")
        raise APIError(
            message="Failed to retrieve sentiment metrics",
            status_code=500,
            details=str(e)
        )
