import sqlite3
from datetime import datetime
from pathlib import Path
from ..utils.logger import get_logger
from ..config import USER_DB_PATH

logger = get_logger(__name__)

class SessionDatabase:
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self._ensure_tables()

    def _get_connection(self) -> sqlite3.Connection:
        conn = sqlite3.connect(self.db_path, isolation_level=None)
        conn.row_factory = sqlite3.Row
        return conn

    def _ensure_tables(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            # Check if table already exists to avoid duplicate messages
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sessions'")
            if cursor.fetchone() is None:
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sessions (
                        id TEXT PRIMARY KEY,
                        user_id TEXT,
                        ip_address TEXT,
                        user_agent TEXT,
                        browser TEXT,
                        os TEXT,
                        device_fingerprint TEXT,
                        login_time TIMESTAMP,
                        last_activity TIMESTAMP,
                        logout_time TIMESTAMP,
                        location_country TEXT,
                        location_city TEXT,
                        latitude REAL,
                        longitude REAL,
                        auth_method TEXT,
                        success INTEGER DEFAULT 1,
                        user_type TEXT DEFAULT 'admin'
                    )
                ''')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_type ON sessions(user_type)')
                conn.commit()
                logger.info("[OK] Sessions table initialized.")
            else:
                logger.debug("[OK] Sessions table already exists.")

class SessionModel:
    def __init__(self, db_path=USER_DB_PATH):
        self.db = SessionDatabase(db_path)

    def create_session(self, id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, success=True, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO sessions (id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, success, user_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    id, user_id, ip_address, user_agent, browser, os, device_fingerprint, login_time, last_activity, location_country, location_city, latitude, longitude, auth_method, int(success), user_type
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.exception("[ERROR] Failed to create session")
            return False

    def get_active_sessions(self, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM sessions WHERE logout_time IS NULL AND user_type = ? ORDER BY last_activity DESC
                ''', (user_type,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch active sessions")
            return []

    def get_session_history(self, user_type="admin", limit=100, offset=0, filters=None):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                query = 'SELECT * FROM sessions WHERE user_type = ?'
                params = [user_type]
                if filters:
                    for key, value in filters.items():
                        if value is not None:
                            query += f' AND {key} = ?'
                            params.append(value)
                query += ' ORDER BY login_time DESC LIMIT ? OFFSET ?'
                params.extend([limit, offset])
                cursor.execute(query, tuple(params))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch session history")
            return []

    def get_session_locations(self, user_type="admin"):
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT id, user_id, device_fingerprint, login_time, location_country, location_city, latitude, longitude, user_type FROM sessions WHERE latitude IS NOT NULL AND longitude IS NOT NULL AND user_type = ?
                ''', (user_type,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch session locations")
            return []

    def get_recent_sessions(self, limit=100, user_type=None):
        """Get recent sessions with optional user type filter."""
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                if user_type:
                    cursor.execute('''
                        SELECT * FROM sessions WHERE user_type = ? ORDER BY login_time DESC LIMIT ?
                    ''', (user_type, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM sessions ORDER BY login_time DESC LIMIT ?
                    ''', (limit,))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch recent sessions")
            return []

    def get_session_anomalies(self, user_type="admin", limit=100, offset=0):
        # Placeholder: In production, this would run anomaly detection logic
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                # Example: fetch failed logins, impossible travel, blacklisted IPs, etc.
                cursor.execute('''
                    SELECT * FROM sessions WHERE success = 0 AND user_type = ? ORDER BY login_time DESC LIMIT ? OFFSET ?
                ''', (user_type, limit, offset))
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except Exception as e:
            logger.exception("[ERROR] Failed to fetch session anomalies")
            return []

    def populate_sample_data(self):
        """Populate database with sample session data for testing."""
        import uuid
        from datetime import datetime, timedelta
        import random

        sample_sessions = []

        # Generate sample sessions for the last 30 days
        for i in range(100):
            session_id = str(uuid.uuid4())
            user_id = f"user_{random.randint(1, 50)}"

            # Random login time in the last 30 days
            login_time = datetime.now() - timedelta(
                days=random.randint(0, 30),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )

            # Last activity within session duration
            last_activity = login_time + timedelta(
                minutes=random.randint(5, 120)
            )

            # Sample locations
            locations = [
                ("United States", "New York", 40.7128, -74.0060),
                ("United Kingdom", "London", 51.5074, -0.1278),
                ("India", "Mumbai", 19.0760, 72.8777),
                ("Germany", "Berlin", 52.5200, 13.4050),
                ("Canada", "Toronto", 43.6532, -79.3832),
                ("Australia", "Sydney", -33.8688, 151.2093),
                ("France", "Paris", 48.8566, 2.3522),
                ("Japan", "Tokyo", 35.6762, 139.6503)
            ]

            country, city, lat, lng = random.choice(locations)

            # Sample browsers and OS
            browsers = ["Chrome", "Firefox", "Safari", "Edge", "Opera"]
            operating_systems = ["Windows 10", "macOS", "Ubuntu", "iOS", "Android"]

            sample_sessions.append({
                'id': session_id,
                'user_id': user_id,
                'ip_address': f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}",
                'user_agent': f"Mozilla/5.0 ({random.choice(operating_systems)}) {random.choice(browsers)}",
                'browser': random.choice(browsers),
                'os': random.choice(operating_systems),
                'device_fingerprint': str(uuid.uuid4())[:16],
                'login_time': login_time.isoformat(),
                'last_activity': last_activity.isoformat(),
                'location_country': country,
                'location_city': city,
                'latitude': lat,
                'longitude': lng,
                'auth_method': random.choice(['password', 'oauth', '2fa', 'sso']),
                'success': random.choice([True, True, True, True, False]),  # 80% success rate
                'user_type': random.choice(['admin', 'admin', 'admin', 'user'])  # More admins for testing
            })

        # Insert sample data
        try:
            with self.db._get_connection() as conn:
                cursor = conn.cursor()
                for session in sample_sessions:
                    cursor.execute('''
                        INSERT OR REPLACE INTO sessions
                        (id, user_id, ip_address, user_agent, browser, os, device_fingerprint,
                         login_time, last_activity, location_country, location_city, latitude, longitude,
                         auth_method, success, user_type)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        session['id'], session['user_id'], session['ip_address'], session['user_agent'],
                        session['browser'], session['os'], session['device_fingerprint'],
                        session['login_time'], session['last_activity'], session['location_country'],
                        session['location_city'], session['latitude'], session['longitude'],
                        session['auth_method'], int(session['success']), session['user_type']
                    ))
                conn.commit()
                logger.info(f"Populated {len(sample_sessions)} sample sessions")
                return True
        except Exception as e:
            logger.exception("[ERROR] Failed to populate sample session data")
            return False