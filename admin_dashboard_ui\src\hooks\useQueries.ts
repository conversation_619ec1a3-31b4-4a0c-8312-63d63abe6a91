import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import toast from 'react-hot-toast';
import {
  authApi,
  userApi,
  adminApi,
  metricsApi,
  chatApi,
  auditApi,
  feedbackApi,
  integrationApi,
  systemApi,
  exportApi,
} from '../services/api';
import {
  AdminUser,
  ChatbotMetrics,
  AuditLog,
  DeviceLog,
  FeatureRequest,
  ApiKey,
  Webhook,
  SystemHealth,
  ExportRequest,
} from '../types';

// ============================================================================
// QUERY KEYS - Centralized for cache management
// ============================================================================

export const queryKeys = {
  // User queries
  currentUser: ['user', 'current'] as const,
  userProfile: (userId: string) => ['user', 'profile', userId] as const,
  
  // Admin queries
  adminUsers: (params?: any) => ['admin', 'users', params] as const,
  adminInvitations: ['admin', 'invitations'] as const,
  
  // Metrics queries
  dashboardMetrics: ['metrics', 'dashboard'] as const,
  chatbotMetrics: (timeRange?: string) => ['metrics', 'chatbot', timeRange] as const,
  performanceMetrics: (timeRange?: string) => ['metrics', 'performance', timeRange] as const,
  anomalies: (severity?: string) => ['metrics', 'anomalies', severity] as const,
  
  // Chat queries
  chatLogs: (params?: any) => ['chat', 'logs', params] as const,
  chatSession: (sessionId: string) => ['chat', 'session', sessionId] as const,
  chatMessages: (sessionId: string) => ['chat', 'messages', sessionId] as const,
  
  // Audit queries
  auditLogs: (params?: any) => ['audit', 'logs', params] as const,
  deviceLogs: (params?: any) => ['audit', 'devices', params] as const,
  sessions: (params?: any) => ['audit', 'sessions', params] as const,
  
  // Feature requests
  featureRequests: (params?: any) => ['feedback', 'requests', params] as const,
  feedbackTrends: (timeRange?: string) => ['feedback', 'trends', timeRange] as const,
  
  // Integrations
  apiKeys: ['integrations', 'api-keys'] as const,
  webhooks: ['integrations', 'webhooks'] as const,
  webhookDeliveries: (webhookId: string) => ['integrations', 'webhooks', webhookId, 'deliveries'] as const,
  
  // System
  systemHealth: ['system', 'health'] as const,
  serviceStatus: ['system', 'services'] as const,
  
  // Exports
  exports: ['exports'] as const,
  exportStatus: (exportId: string) => ['exports', exportId] as const,
} as const;

// ============================================================================
// CUSTOM HOOKS - User Management
// ============================================================================

export const useCurrentUser = (options?: UseQueryOptions<any, AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.currentUser,
    queryFn: () => userApi.getCurrentUser().then(res => res.data),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: any) => userApi.updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.currentUser });
      toast.success('Profile updated successfully');
    },
    onError: (error: AxiosError) => {
      toast.error('Failed to update profile');
    },
  });
};

// ============================================================================
// CUSTOM HOOKS - Admin Management
// ============================================================================

export const useAdminUsers = (params?: any, options?: UseQueryOptions<AdminUser[], AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.adminUsers(params),
    queryFn: () => adminApi.getUsers(params).then(res => res.data),
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useCreateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (userData: any) => adminApi.createUser(userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      toast.success('User created successfully');
    },
    onError: (error: AxiosError) => {
      toast.error('Failed to create user');
    },
  });
};

export const useChangeRole = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ email, role }: { email: string; role: string }) => 
      adminApi.changeRole(email, role),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      toast.success('Role changed successfully');
    },
    onError: (error: AxiosError) => {
      toast.error('Failed to change role');
    },
  });
};

// ============================================================================
// CUSTOM HOOKS - Metrics & Analytics
// ============================================================================

export const useDashboardMetrics = (timeRange?: string, options?: UseQueryOptions<ChatbotMetrics, AxiosError>) => {
  return useQuery({
    queryKey: [...queryKeys.dashboardMetrics, timeRange],
    queryFn: () => metricsApi.getDashboardMetrics(timeRange).then(res => res.data),
    refetchInterval: 15000, // Refresh every 15 seconds for real-time data
    staleTime: 5000, // Consider stale after 5 seconds for more responsive updates
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    ...options,
  });
};

export const useChatbotMetrics = (timeRange?: string, options?: UseQueryOptions<ChatbotMetrics, AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.chatbotMetrics(timeRange),
    queryFn: () => metricsApi.getChatbotMetrics(timeRange).then(res => res.data),
    refetchInterval: 60000, // Refresh every minute
    staleTime: 30000, // Consider stale after 30 seconds
    ...options,
  });
};

export const useAnomalies = (severity?: string, options?: UseQueryOptions<any[], AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.anomalies(severity),
    queryFn: () => metricsApi.getAnomalies(severity).then(res => res.data),
    refetchInterval: 2 * 60 * 1000, // Refresh every 2 minutes
    ...options,
  });
};

export const useAcknowledgeAnomaly = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ anomalyId, notes }: { anomalyId: string; notes?: string }) => 
      metricsApi.acknowledgeAnomaly(anomalyId, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['metrics', 'anomalies'] });
      toast.success('Anomaly acknowledged');
    },
    onError: (error: AxiosError) => {
      toast.error('Failed to acknowledge anomaly');
    },
  });
};

// ============================================================================
// CUSTOM HOOKS - Chat Management
// ============================================================================

export const useChatLogs = (params?: any, options?: UseQueryOptions<any[], AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.chatLogs(params),
    queryFn: () => chatApi.getChatLogs(params).then(res => res.data),
    staleTime: 60000, // 1 minute
    ...options,
  });
};

export const useChatSession = (sessionId: string, options?: UseQueryOptions<any, AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.chatSession(sessionId),
    queryFn: () => chatApi.getChatSession(sessionId).then(res => res.data),
    enabled: !!sessionId,
    ...options,
  });
};

export const useEscalateChat = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ sessionId, reason, priority }: { sessionId: string; reason: string; priority: string }) => 
      chatApi.escalateChat(sessionId, reason, priority),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chat'] });
      toast.success('Chat escalated successfully');
    },
    onError: (error: AxiosError) => {
      toast.error('Failed to escalate chat');
    },
  });
};

// ============================================================================
// CUSTOM HOOKS - Audit & Compliance
// ============================================================================

export const useAuditLogs = (params?: any, options?: UseQueryOptions<AuditLog[], AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.auditLogs(params),
    queryFn: () => auditApi.getAuditLogs(params).then(res => res.data),
    staleTime: 2 * 60 * 1000, // 2 minutes
    ...options,
  });
};

export const useDeviceLogs = (params?: any, options?: UseQueryOptions<DeviceLog[], AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.deviceLogs(params),
    queryFn: () => auditApi.getDeviceLogs(params).then(res => res.data),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// ============================================================================
// CUSTOM HOOKS - Feature Requests
// ============================================================================

export const useFeatureRequests = (params?: any, options?: UseQueryOptions<FeatureRequest[], AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.featureRequests(params),
    queryFn: () => feedbackApi.getFeatureRequests(params).then(res => res.data),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useCreateFeatureRequest = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (requestData: any) => feedbackApi.createFeatureRequest(requestData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['feedback', 'requests'] });
      toast.success('Feature request submitted successfully');
    },
    onError: (error: AxiosError) => {
      toast.error('Failed to submit feature request');
    },
  });
};

// ============================================================================
// CUSTOM HOOKS - System Health
// ============================================================================

export const useSystemHealth = (options?: UseQueryOptions<SystemHealth, AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.systemHealth,
    queryFn: () => systemApi.getSystemHealth().then(res => res.data),
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 15000, // Consider stale after 15 seconds
    ...options,
  });
};

// ============================================================================
// CUSTOM HOOKS - Integrations
// ============================================================================

export const useApiKeys = (options?: UseQueryOptions<ApiKey[], AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.apiKeys,
    queryFn: () => integrationApi.getApiKeys().then(res => res.data),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useWebhooks = (options?: UseQueryOptions<Webhook[], AxiosError>) => {
  return useQuery({
    queryKey: queryKeys.webhooks,
    queryFn: () => integrationApi.getWebhooks().then(res => res.data),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// ============================================================================
// UTILITY HOOKS
// ============================================================================

export const useInvalidateQueries = () => {
  const queryClient = useQueryClient();

  return {
    invalidateAll: () => queryClient.invalidateQueries(),
    invalidateMetrics: () => queryClient.invalidateQueries({ queryKey: ['metrics'] }),
    invalidateChat: () => queryClient.invalidateQueries({ queryKey: ['chat'] }),
    invalidateAudit: () => queryClient.invalidateQueries({ queryKey: ['audit'] }),
    invalidateAdmin: () => queryClient.invalidateQueries({ queryKey: ['admin'] }),
  };
};

// ============================================================================
// REAL-TIME DATA HOOKS
// ============================================================================

export const useLiveSessions = (options?: UseQueryOptions<any[], AxiosError>) => {
  return useQuery({
    queryKey: ['liveSessions'],
    queryFn: () => metricsApi.getLiveSessions().then(res => res.data),
    refetchInterval: 5000, // Refresh every 5 seconds for live data
    staleTime: 2000, // Consider stale after 2 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    ...options,
  });
};

export const useActiveEscalations = (options?: UseQueryOptions<any[], AxiosError>) => {
  return useQuery({
    queryKey: ['activeEscalations'],
    queryFn: () => metricsApi.getActiveEscalations().then(res => res.data),
    refetchInterval: 10000, // Refresh every 10 seconds
    staleTime: 5000, // Consider stale after 5 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    ...options,
  });
};

export const useRealTimeFeedback = (timeRange?: string, options?: UseQueryOptions<any, AxiosError>) => {
  return useQuery({
    queryKey: ['realTimeFeedback', timeRange],
    queryFn: () => metricsApi.getRealTimeFeedback(timeRange).then(res => res.data),
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 15000, // Consider stale after 15 seconds
    refetchOnWindowFocus: true,
    ...options,
  });
};

export const useTopQuestions = (timeRange?: string, limit?: number, options?: UseQueryOptions<any[], AxiosError>) => {
  return useQuery({
    queryKey: ['topQuestions', timeRange, limit],
    queryFn: () => metricsApi.getTopQuestions(timeRange, limit).then(res => res.data),
    refetchInterval: 60000, // Refresh every minute
    staleTime: 30000, // Consider stale after 30 seconds
    ...options,
  });
};

export const useResponseTimes = (timeRange?: string, options?: UseQueryOptions<any[], AxiosError>) => {
  return useQuery({
    queryKey: ['responseTimes', timeRange],
    queryFn: () => metricsApi.getResponseTimes(timeRange).then(res => res.data),
    refetchInterval: 60000, // Refresh every minute
    staleTime: 30000, // Consider stale after 30 seconds
    ...options,
  });
};
