"""
Main application entry point for the Advanced HR Assistant Chatbot.
Refactored for production-grade architecture with modular design.
"""
import os
import signal
import sys
import threading
import multiprocessing
from pathlib import Path
from dotenv import load_dotenv

from src.utils.logger import get_logger, get_session_info
from app.core.app_factory import create_app
from app.core.startup import startup_application
from src.monitoring import initialize_apm


# Must be at the very top for Windows multiprocessing
multiprocessing.set_start_method('spawn', force=True)

# Load environment variables from .env file
load_dotenv()

# Initialize logger after environment is loaded
logger = get_logger(__name__)

# Log session information only once
session_info = get_session_info()
logger.info(f"Application session initialized - Log file: {session_info['session_log_file']}")


def handle_sigint(sig, frame):
    """Handle shutdown signal gracefully."""
    logger.info("Received shutdown signal, exiting now.")
    print("Received shutdown signal, exiting now.")
    sys.exit(0)


# Only register signal handler in main thread
if threading.current_thread() is threading.main_thread():
    signal.signal(signal.SIGINT, handle_sigint)

def main():
    """
    Main application entry point.
    Handles startup, creates app, and runs the server.
    """
    try:
        logger.info("Starting HR Assistant Chatbot application...")

        # Startup sequence - this will only run once due to singleton pattern
        apm, service_manager = startup_application()

        # Create Flask application
        flask_app = create_app()

        logger.info("Application startup completed successfully")
        logger.info("Starting Flask development server on http://0.0.0.0:5051")

        # Run the application
        if os.getenv('FLASK_ENV') == 'production':
            logger.info("Production mode detected - use start_production.py or gunicorn for production deployment")
            logger.info("Starting development server for testing purposes only")
            flask_app.run(host="0.0.0.0", port=5051, debug=False)
        else:
            logger.info("Development mode - starting Flask development server")
            flask_app.run(host="0.0.0.0", port=5051, debug=False)

    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application startup failed: {e}")
        raise
    finally:
        logger.info("Application shutdown completed")


if __name__ == "__main__":
    main()
