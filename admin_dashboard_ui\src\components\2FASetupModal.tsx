import React, { useState, useEffect, useCallback } from 'react';
import { authApi } from '../services/api';

interface TwoFASetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  email: string;
  onSetupComplete: () => void;
}

export default function TwoFASetupModal({ isOpen, onClose, email, onSetupComplete }: TwoFASetupModalProps) {
  const [step, setStep] = useState<'setup' | 'verify'>('setup');
  const [qrCode, setQrCode] = useState<string>('');
  const [secret, setSecret] = useState<string>('');
  const [totpCode, setTotpCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  const handleRequest2FASetup = useCallback(async () => {
    setLoading(true);
    setError('');
    try {
      const response = await authApi.request2FASetup(email);
      if (response.data.success) {
        setQrCode(response.data.qr_url);
        setSecret(response.data.secret);
        setSuccess('2FA setup initiated successfully. Please scan the QR code with your authenticator app.');
      } else {
        setError(response.data.message || 'Failed to initiate 2FA setup');
      }
    } catch (err: any) {
      setError(err?.response?.data?.detail || 'Failed to initiate 2FA setup');
    } finally {
      setLoading(false);
    }
  }, [email]);

  useEffect(() => {
    if (isOpen && step === 'setup') {
      handleRequest2FASetup();
    }
  }, [isOpen, step, handleRequest2FASetup]);

  const handleVerify2FA = async () => {
    if (!totpCode.trim()) {
      setError('Please enter the 2FA code');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const response = await authApi.verify2FA(email, totpCode);
      if (response.data.success) {
        setSuccess('2FA verification successful! You can now log in with 2FA.');
        setTimeout(() => {
          onSetupComplete();
          onClose();
        }, 2000);
      } else {
        setError(response.data.message || 'Invalid 2FA code');
      }
    } catch (err: any) {
      setError(err?.response?.data?.detail || 'Failed to verify 2FA code');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (step === 'verify') {
      setStep('setup');
      setTotpCode('');
      setError('');
      setSuccess('');
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Setup Two-Factor Authentication</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {step === 'setup' && (
          <div className="space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Setting up 2FA...</p>
              </div>
            ) : qrCode ? (
              <div className="text-center space-y-4">
                <div className="bg-gray-100 p-4 rounded-lg">
                  <img 
                    src={qrCode} 
                    alt="2FA QR Code" 
                    className="mx-auto w-48 h-48"
                  />
                </div>
                <div className="text-sm text-gray-600">
                  <p className="mb-2">Scan this QR code with your authenticator app:</p>
                  <ul className="text-left space-y-1">
                    <li>• Google Authenticator</li>
                    <li>• Microsoft Authenticator</li>
                    <li>• Authy</li>
                    <li>• Any TOTP-compatible app</li>
                  </ul>
                </div>
                <div className="bg-gray-100 p-3 rounded text-xs font-mono break-all">
                  <p className="text-gray-700 mb-1">Manual entry code:</p>
                  <p className="text-gray-900">{secret}</p>
                </div>
                <button
                  onClick={() => setStep('verify')}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition"
                >
                  I've Added the Code, Continue
                </button>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600">Failed to generate 2FA setup. Please try again.</p>
                <button
                  onClick={handleRequest2FASetup}
                  className="mt-4 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition"
                >
                  Retry
                </button>
              </div>
            )}
          </div>
        )}

        {step === 'verify' && (
          <div className="space-y-4">
            <div className="text-center">
              <p className="text-gray-600 mb-4">
                Enter the 6-digit code from your authenticator app to complete setup.
              </p>
              <input
                type="text"
                placeholder="000000"
                value={totpCode}
                onChange={(e) => setTotpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                className="w-full text-center text-2xl font-mono tracking-widest border border-gray-300 rounded px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={6}
                autoFocus
              />
            </div>
            <button
              onClick={handleVerify2FA}
              disabled={loading || totpCode.length !== 6}
              className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Verifying...' : 'Verify & Complete Setup'}
            </button>
            <button
              onClick={() => setStep('setup')}
              className="w-full bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400 transition"
            >
              Back to Setup
            </button>
          </div>
        )}

        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {success && (
          <div className="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            {success}
          </div>
        )}
      </div>
    </div>
  );
}
