"""
Centralized logging configuration for the HR Assistant Chatbot.
Creates a new log file for each backend session with timestamp-based naming.
Implements industry-standard logging practices with centralized configuration.
"""

import logging
import logging.handlers
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import threading
import glob

# Global variables for session management
_session_log_file: Optional[str] = None
_session_start_time: Optional[datetime] = None
_logger_cache: Dict[str, logging.Logger] = {}
_log_lock = threading.Lock()
_initialized = False

# Environment-based configuration
ENVIRONMENT = os.getenv('FLASK_ENV', 'development').lower()
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO').upper()
MAX_LOG_FILES = int(os.getenv('MAX_LOG_FILES', '7'))  # Keep last 7 session logs
LOG_DIR = Path("logs")

# Production logging optimization
if ENVIRONMENT == 'production':
    # In production, default to WARNING level to reduce overhead
    if LOG_LEVEL == 'INFO':
        LOG_LEVEL = 'WARNING'
    # Reduce log file retention in production
    MAX_LOG_FILES = min(MAX_LOG_FILES, 3)

# Log level mapping
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

class SessionLogFormatter(logging.Formatter):
    """Custom formatter for session-based logging with structured information."""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s [%(levelname)s] [module:%(name)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    def format(self, record):
        # Add user_id and req_id to the message if available
        extra_info = []
        
        if hasattr(record, 'user_id') and record.user_id:
            extra_info.append(f"[user:{record.user_id}]")
        
        if hasattr(record, 'req_id') and record.req_id:
            extra_info.append(f"[req:{record.req_id}]")
        
        # Format the base message
        formatted = super().format(record)
        
        # Insert extra info after the module name
        if extra_info:
            # Find the position after [module:name]
            module_pos = formatted.find('[module:')
            if module_pos != -1:
                # Find the end of module section
                end_pos = formatted.find(']', module_pos)
                if end_pos != -1:
                    # Insert extra info after the module section
                    formatted = formatted[:end_pos + 1] + ' ' + ' '.join(extra_info) + formatted[end_pos + 1:]
        
        return formatted

class ConsoleFormatter(logging.Formatter):
    """Simplified formatter for console output."""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
            datefmt='%H:%M:%S'
        )

def _cleanup_old_logs():
    """Remove old log files, keeping only the last MAX_LOG_FILES sessions."""
    try:
        # Get all chatbot session log files
        pattern = str(LOG_DIR / "chatbot_*.log")
        log_files = glob.glob(pattern)
        
        if len(log_files) > MAX_LOG_FILES:
            # Sort by modification time (newest first)
            log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
            # Remove old files
            for old_file in log_files[MAX_LOG_FILES:]:
                try:
                    os.remove(old_file)
                    print(f"Removed old log file: {old_file}")
                except OSError as e:
                    print(f"Failed to remove old log file {old_file}: {e}")
    except Exception as e:
        print(f"Error during log cleanup: {e}")

def _create_session_log_file() -> str:
    """Create a new session log file with timestamp-based naming."""
    global _session_start_time
    
    # Create logs directory if it doesn't exist
    LOG_DIR.mkdir(exist_ok=True)
    
    # Generate timestamp for session
    _session_start_time = datetime.now()
    timestamp = _session_start_time.strftime("%Y-%m-%d_%H-%M-%S")
    
    # Create session log filename
    session_log_file = str(LOG_DIR / f"chatbot_{timestamp}.log")
    
    # Clean up old logs before creating new one
    _cleanup_old_logs()
    
    return session_log_file

def _initialize_session_logging():
    """Initialize the centralized logging system for a new session."""
    global _session_log_file, _logger_cache, _initialized
    
    with _log_lock:
        if _initialized:
            return
            
        if _session_log_file is None:
            _session_log_file = _create_session_log_file()
            
            # Clear existing logger cache
            _logger_cache.clear()
            
            # Configure root logger
            _configure_root_logger()
            
            # Log session start
            _log_session_start()
            
            _initialized = True

def _configure_root_logger():
    """Configure the root logger with centralized handlers."""
    root_logger = logging.getLogger()
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Set log level
    root_logger.setLevel(LOG_LEVELS.get(LOG_LEVEL, logging.INFO))
    
    # Create session log file handler
    file_handler = logging.FileHandler(_session_log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(SessionLogFormatter())
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(ConsoleFormatter())
    
    # Add handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Disable propagation to avoid duplicate logs
    root_logger.propagate = False

def _log_session_start():
    """Log the session start separator."""
    root_logger = logging.getLogger()
    timestamp = _session_start_time.strftime("%Y-%m-%d %H:%M:%S")
    separator = "=" * 80
    root_logger.info(f"{separator}")
    root_logger.info(f"Backend Chatbot Session Started at {timestamp}")
    root_logger.info(f"Environment: {ENVIRONMENT}")
    root_logger.info(f"Log Level: {LOG_LEVEL}")
    root_logger.info(f"Session Log File: {_session_log_file}")
    root_logger.info(f"{separator}")

def get_logger(name: str, level: Optional[int] = None) -> logging.Logger:
    """
    Get a logger with centralized configuration.
    
    Args:
        name: Logger name (usually __name__)
        level: Optional log level override
        
    Returns:
        Configured logger instance
    """
    # Initialize session logging if not already done
    if not _initialized:
        _initialize_session_logging()
    
    # Check cache first
    if name in _logger_cache:
        logger = _logger_cache[name]
        if level is not None:
            logger.setLevel(level)
        return logger
    
    # Create new logger
    logger = logging.getLogger(name)
    
    # Set level
    if level is not None:
        logger.setLevel(level)
    else:
        logger.setLevel(LOG_LEVELS.get(LOG_LEVEL, logging.INFO))
    
    # Don't add handlers to individual loggers - use root logger propagation
    # This prevents duplicate logging
    logger.propagate = True
    
    # Cache the logger
    _logger_cache[name] = logger
    
    return logger

def log_with_context(level: str, message: str, **kwargs):
    """
    Log a message with additional context (user_id, req_id, etc.).
    
    Args:
        level: Log level ('debug', 'info', 'warning', 'error', 'critical')
        message: Log message
        **kwargs: Additional context (user_id, req_id, etc.)
    """
    logger = get_logger('context_logger')
    
    # Create a log record with extra attributes
    record = logger.makeRecord(
        name=logger.name,
        level=getattr(logging, level.upper()),
        fn='',
        lno=0,
        msg=message,
        args=(),
        exc_info=None
    )
    
    # Add extra attributes
    for key, value in kwargs.items():
        setattr(record, key, value)
    
    # Log the record
    logger.handle(record)

def get_session_info() -> Dict[str, Any]:
    """Get current session information."""
    return {
        'session_log_file': _session_log_file,
        'session_start_time': _session_start_time.isoformat() if _session_start_time else None,
        'environment': ENVIRONMENT,
        'log_level': LOG_LEVEL,
        'max_log_files': MAX_LOG_FILES
    }

# Convenience functions for common logging patterns
def log_user_action(user_id: str, action: str, **kwargs):
    """Log a user action with user context."""
    log_with_context('info', f"User action: {action}", user_id=user_id, **kwargs)

def log_request(req_id: str, method: str, path: str, user_id: Optional[str] = None, **kwargs):
    """Log an incoming request."""
    message = f"Request: {method} {path}"
    log_with_context('info', message, req_id=req_id, user_id=user_id, **kwargs)

def log_error(error: Exception, context: str = "", user_id: Optional[str] = None, req_id: Optional[str] = None, **kwargs):
    """Log an error with context."""
    message = f"Error in {context}: {str(error)}"
    log_with_context('error', message, user_id=user_id, req_id=req_id, **kwargs)

# Initialize logging when module is imported - only once
if not _initialized:
    _initialize_session_logging()