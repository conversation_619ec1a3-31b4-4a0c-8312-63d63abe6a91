"""
Compliance and auditing API routes for admin dashboard.
Provides GDPR compliance, data deletion, and audit trail functionality.
"""
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from typing import Dict, Any, List, Optional
import random
import uuid

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from src.database.conversation_store import ConversationStore
from src.database.user_db import ConversationModel, UserModel
from src.database.session_db import SessionModel

logger = get_logger(__name__)

# Create blueprint
compliance_v1_bp = Blueprint('compliance_v1', __name__)

def get_date_range(time_range: str = "30d") -> tuple:
    """Get start and end dates based on time range parameter."""
    end_date = datetime.now()
    
    if time_range == "7d":
        start_date = end_date - timedelta(days=7)
    elif time_range == "30d":
        start_date = end_date - timedelta(days=30)
    elif time_range == "90d":
        start_date = end_date - timedelta(days=90)
    elif time_range == "1y":
        start_date = end_date - timedelta(days=365)
    else:
        start_date = end_date - timedelta(days=30)
    
    return start_date, end_date

@compliance_v1_bp.route('/gdpr', methods=['GET'])
def get_gdpr_compliance_data():
    """
    Get GDPR compliance data including data processing activities,
    user consent status, and data retention information.
    """
    try:
        time_range = request.args.get('time_range', '30d')
        start_date, end_date = get_date_range(time_range)
        
        # Get data from various sources
        conversation_store = ConversationStore()
        user_model = UserModel()
        session_model = SessionModel()
        
        # Get conversation data for analysis
        conversations = conversation_store.get_all_conversations(
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            limit=10000
        )
        
        # Get session data
        sessions = session_model.get_recent_sessions(limit=5000)
        
        # Analyze data processing activities
        unique_users = set(conv.get('device_id', '') for conv in conversations if conv.get('device_id'))
        total_data_points = len(conversations) + len(sessions)
        
        # Mock GDPR compliance metrics
        gdpr_data = {
            "data_processing_summary": {
                "total_users_processed": len(unique_users),
                "total_conversations": len(conversations),
                "total_sessions": len(sessions),
                "data_points_collected": total_data_points,
                "retention_period_days": 365,
                "data_categories": [
                    {"category": "Chat Messages", "count": len(conversations), "retention_days": 365},
                    {"category": "Session Data", "count": len(sessions), "retention_days": 90},
                    {"category": "User Preferences", "count": len(unique_users), "retention_days": 730},
                    {"category": "Analytics Data", "count": total_data_points, "retention_days": 180}
                ]
            },
            "consent_management": {
                "users_with_consent": len(unique_users),  # Mock: assume all have consent
                "consent_rate": 100.0,  # Mock: 100% consent rate
                "pending_consent": 0,
                "withdrawn_consent": random.randint(0, 5),
                "consent_types": [
                    {"type": "Chat Data Processing", "consented": len(unique_users), "percentage": 100.0},
                    {"type": "Analytics", "consented": int(len(unique_users) * 0.85), "percentage": 85.0},
                    {"type": "Performance Monitoring", "consented": int(len(unique_users) * 0.92), "percentage": 92.0}
                ]
            },
            "data_subject_rights": {
                "access_requests": random.randint(0, 3),
                "deletion_requests": random.randint(0, 2),
                "portability_requests": random.randint(0, 1),
                "rectification_requests": random.randint(0, 1),
                "pending_requests": random.randint(0, 2),
                "completed_requests": random.randint(5, 15),
                "average_response_time_hours": 24
            },
            "data_retention": {
                "items_due_for_deletion": random.randint(0, 10),
                "items_deleted_this_period": random.randint(20, 50),
                "retention_policies": [
                    {
                        "data_type": "Chat Messages",
                        "retention_period": "12 months",
                        "items_count": len(conversations),
                        "next_cleanup": (datetime.now() + timedelta(days=30)).isoformat()
                    },
                    {
                        "data_type": "Session Logs",
                        "retention_period": "3 months",
                        "items_count": len(sessions),
                        "next_cleanup": (datetime.now() + timedelta(days=7)).isoformat()
                    }
                ]
            },
            "privacy_impact": {
                "risk_level": "Low",
                "last_assessment": (datetime.now() - timedelta(days=90)).isoformat(),
                "next_assessment": (datetime.now() + timedelta(days=90)).isoformat(),
                "identified_risks": [
                    {
                        "risk": "Data retention beyond necessary period",
                        "severity": "Low",
                        "mitigation": "Automated cleanup processes in place"
                    }
                ],
                "compliance_score": 95
            },
            "audit_trail": [
                {
                    "timestamp": (datetime.now() - timedelta(hours=i)).isoformat(),
                    "action": random.choice(["Data Access", "Data Deletion", "Consent Update", "Policy Change"]),
                    "user": f"admin_{random.randint(1, 5)}",
                    "details": f"Processed {random.choice(['access', 'deletion', 'update'])} request",
                    "compliance_impact": "None"
                }
                for i in range(10)
            ],
            "time_range": time_range,
            "generated_at": datetime.now().isoformat()
        }
        
        logger.info(f"Generated GDPR compliance data: {len(unique_users)} users, {total_data_points} data points")
        return jsonify(gdpr_data)
        
    except Exception as e:
        logger.error(f"Error getting GDPR compliance data: {e}")
        raise APIError(
            message="Failed to retrieve GDPR compliance data",
            status_code=500,
            details=str(e)
        )

@compliance_v1_bp.route('/data-deletion', methods=['GET'])
def get_data_deletion_requests():
    """Get pending and completed data deletion requests."""
    try:
        status_filter = request.args.get('status', 'all')  # all, pending, completed, failed
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        # Mock data deletion requests
        deletion_requests = []
        
        for i in range(50):  # Generate 50 mock requests
            request_id = str(uuid.uuid4())
            status = random.choice(['pending', 'completed', 'failed', 'in_progress'])
            
            if status_filter != 'all' and status != status_filter:
                continue
            
            deletion_requests.append({
                "id": request_id,
                "user_id": f"user_{random.randint(1000, 9999)}",
                "email": f"user{random.randint(1, 100)}@example.com",
                "request_type": random.choice(['full_deletion', 'partial_deletion', 'anonymization']),
                "status": status,
                "requested_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
                "processed_at": (datetime.now() - timedelta(days=random.randint(0, 5))).isoformat() if status == 'completed' else None,
                "reason": random.choice(['User request', 'GDPR compliance', 'Account closure', 'Data minimization']),
                "data_types": random.sample(['chat_messages', 'session_data', 'user_preferences', 'analytics_data'], random.randint(1, 4)),
                "estimated_records": random.randint(10, 1000),
                "deleted_records": random.randint(10, 1000) if status == 'completed' else 0,
                "processor": f"admin_{random.randint(1, 5)}" if status in ['completed', 'in_progress'] else None,
                "notes": "Standard deletion process" if status == 'completed' else "Pending review" if status == 'pending' else None
            })
        
        # Apply pagination
        total_requests = len(deletion_requests)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_requests = deletion_requests[start_idx:end_idx]
        
        # Calculate summary statistics
        status_counts = {}
        for req in deletion_requests:
            status = req['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        response_data = {
            "requests": paginated_requests,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total_requests,
                "total_pages": (total_requests + page_size - 1) // page_size
            },
            "summary": {
                "total_requests": total_requests,
                "status_breakdown": status_counts,
                "avg_processing_time_hours": 24,
                "compliance_rate": 98.5
            },
            "filters": {
                "status": status_filter
            },
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting data deletion requests: {e}")
        raise APIError(
            message="Failed to retrieve data deletion requests",
            status_code=500,
            details=str(e)
        )

@compliance_v1_bp.route('/data-deletion', methods=['POST'])
def create_data_deletion_request():
    """Create a new data deletion request."""
    try:
        data = request.get_json()
        
        if not data or 'user_id' not in data:
            raise APIError(
                message="user_id is required",
                status_code=400
            )
        
        user_id = data['user_id']
        reason = data.get('reason', 'User request')
        data_types = data.get('data_types', ['chat_messages', 'session_data'])
        
        # Create deletion request record
        request_id = str(uuid.uuid4())
        
        # In production, this would be stored in a database
        deletion_request = {
            "id": request_id,
            "user_id": user_id,
            "request_type": "full_deletion",
            "status": "pending",
            "requested_at": datetime.now().isoformat(),
            "reason": reason,
            "data_types": data_types,
            "estimated_records": random.randint(50, 500),  # Mock estimation
            "processor": None,
            "notes": "Automated request created via API"
        }
        
        logger.info(f"Created data deletion request {request_id} for user {user_id}")
        
        return jsonify({
            "success": True,
            "request_id": request_id,
            "status": "pending",
            "estimated_processing_time": "24-48 hours",
            "message": "Data deletion request created successfully"
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating data deletion request: {e}")
        raise APIError(
            message="Failed to create data deletion request",
            status_code=500,
            details=str(e)
        )

@compliance_v1_bp.route('/export-user-data', methods=['POST'])
def export_user_data():
    """Export user data for GDPR data portability requests."""
    try:
        data = request.get_json()
        
        if not data or 'user_id' not in data:
            raise APIError(
                message="user_id is required",
                status_code=400
            )
        
        user_id = data['user_id']
        export_format = data.get('format', 'json')  # json, csv, xml
        
        # In production, this would gather actual user data
        conversation_store = ConversationStore()
        user_conversations = conversation_store.get_all_conversations(
            device_id=user_id,
            limit=10000
        )
        
        # Mock user data export
        export_data = {
            "user_id": user_id,
            "export_timestamp": datetime.now().isoformat(),
            "data_categories": {
                "conversations": {
                    "count": len(user_conversations),
                    "data": user_conversations[:10]  # Sample data
                },
                "sessions": {
                    "count": random.randint(10, 100),
                    "data": []  # Would contain actual session data
                },
                "preferences": {
                    "language": "en",
                    "timezone": "UTC",
                    "notifications": True
                }
            },
            "metadata": {
                "export_format": export_format,
                "total_records": len(user_conversations),
                "data_retention_period": "365 days",
                "last_activity": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat()
            }
        }
        
        # Generate export ID
        export_id = str(uuid.uuid4())
        
        logger.info(f"Generated data export {export_id} for user {user_id}")
        
        return jsonify({
            "success": True,
            "export_id": export_id,
            "format": export_format,
            "total_records": len(user_conversations),
            "download_url": f"/api/v1/compliance/exports/{export_id}/download",
            "expires_at": (datetime.now() + timedelta(days=7)).isoformat(),
            "message": "Data export prepared successfully"
        })
        
    except Exception as e:
        logger.error(f"Error exporting user data: {e}")
        raise APIError(
            message="Failed to export user data",
            status_code=500,
            details=str(e)
        )

@compliance_v1_bp.route('/gdpr-requests', methods=['GET'])
def get_gdpr_requests():
    """Get all GDPR-related requests (access, deletion, portability, etc.)."""
    try:
        request_type = request.args.get('type', 'all')  # all, access, deletion, portability, rectification
        status = request.args.get('status', 'all')  # all, pending, completed, failed
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        # Mock GDPR requests
        gdpr_requests = []
        
        request_types = ['access', 'deletion', 'portability', 'rectification']
        statuses = ['pending', 'in_progress', 'completed', 'failed']
        
        for i in range(100):  # Generate 100 mock requests
            req_type = random.choice(request_types)
            req_status = random.choice(statuses)
            
            if request_type != 'all' and req_type != request_type:
                continue
            if status != 'all' and req_status != status:
                continue
            
            gdpr_requests.append({
                "id": str(uuid.uuid4()),
                "type": req_type,
                "status": req_status,
                "user_id": f"user_{random.randint(1000, 9999)}",
                "email": f"user{random.randint(1, 100)}@example.com",
                "requested_at": (datetime.now() - timedelta(days=random.randint(1, 60))).isoformat(),
                "completed_at": (datetime.now() - timedelta(days=random.randint(0, 10))).isoformat() if req_status == 'completed' else None,
                "processor": f"admin_{random.randint(1, 5)}" if req_status in ['completed', 'in_progress'] else None,
                "priority": random.choice(['low', 'medium', 'high']),
                "description": f"User requested {req_type} of personal data",
                "response_due": (datetime.now() + timedelta(days=30)).isoformat() if req_status == 'pending' else None
            })
        
        # Sort by requested_at (newest first)
        gdpr_requests.sort(key=lambda x: x['requested_at'], reverse=True)
        
        # Apply pagination
        total_requests = len(gdpr_requests)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_requests = gdpr_requests[start_idx:end_idx]
        
        response_data = {
            "requests": paginated_requests,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total_requests,
                "total_pages": (total_requests + page_size - 1) // page_size
            },
            "summary": {
                "total_requests": total_requests,
                "pending_requests": len([r for r in gdpr_requests if r['status'] == 'pending']),
                "overdue_requests": len([r for r in gdpr_requests if r['status'] == 'pending' and r['response_due'] and r['response_due'] < datetime.now().isoformat()]),
                "avg_response_time_days": 15
            },
            "filters": {
                "type": request_type,
                "status": status
            },
            "generated_at": datetime.now().isoformat()
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error getting GDPR requests: {e}")
        raise APIError(
            message="Failed to retrieve GDPR requests",
            status_code=500,
            details=str(e)
        )
