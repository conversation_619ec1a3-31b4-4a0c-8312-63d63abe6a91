"""
Document processing routes for the HR Assistant Chatbot.
Handles file uploads, document processing, and vector search operations.
"""
import os
import time
import traceback
from pathlib import Path
from uuid import uuid4
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from qdrant_client.models import PointStruct

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from app.middleware.logging_middleware import RequestTimer
from src.document_processing.file_processor import FileProcessor, renumber_faq
from src.document_processing.embedding_generator import EmbeddingGenerator
from src.database.vector_store import QdrantVectorStore
from src.ner.entity_extractor import EntityExtractor

logger = get_logger(__name__)

# Create blueprint
documents_bp = Blueprint('documents', __name__)


@documents_bp.route('/upload-document', methods=["POST"])
def upload_document():
    """
    Upload a document without processing.
    Saves file to raw directory for later processing.
    """
    try:
        if "file" not in request.files:
            raise APIError(
                message="No file uploaded",
                status_code=400
            )

        file = request.files["file"]
        if file.filename == "":
            raise APIError(
                message="No file selected",
                status_code=400
            )

        # Save to raw directory
        raw_dir = Path(current_app.root_path) / "data" / "raw"
        os.makedirs(raw_dir, exist_ok=True)
        file_path = raw_dir / file.filename
        file.save(file_path)

        logger.info(f"Document uploaded: {file.filename}")
        
        return jsonify({
            "success": True,
            "filename": file.filename,
            "uploaded": True,
            "message": f"File {file.filename} uploaded successfully"
        })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Document upload failed: {e}")
        raise APIError(
            message=f"Document upload failed: {str(e)}",
            status_code=500
        )


@documents_bp.route('/start-training', methods=["POST"])
def start_training():
    """
    Start processing/training on a specific file.
    Processes document through the full pipeline.
    """
    try:
        data = request.json
        filename = data.get("filename")
        
        if not filename:
            raise APIError(
                message="No filename provided",
                status_code=400
            )

        raw_dir = Path(current_app.root_path) / "data" / "raw"
        file_path = raw_dir / filename
        
        if not file_path.exists():
            raise APIError(
                message="File not found",
                status_code=404
            )

        with RequestTimer("document_training"):
            service_manager = ServiceManager.get_instance()
            pipeline = service_manager.get_training_pipeline()
            
            success = pipeline.process_file(file_path, force_reprocess=False)

            if not success:
                raise APIError(
                    message="Document processing failed",
                    status_code=500
                )

        logger.info(f"Document training completed: {filename}")
        
        return jsonify({
            "success": True,
            "filename": filename,
            "trained": True,
            "message": f"Training completed for {filename}"
        })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Document training failed: {e}")
        raise APIError(
            message=f"Document training failed: {str(e)}",
            status_code=500
        )


@documents_bp.route('/summarize-document', methods=["POST"])
def summarize_document():
    """
    Upload, process, and summarize a document.
    Complete pipeline from upload to summary generation.
    """
    try:
        if "file" not in request.files:
            raise APIError(
                message="No file uploaded",
                status_code=400
            )

        file = request.files["file"]
        if file.filename == "":
            raise APIError(
                message="No file selected",
                status_code=400
            )

        with RequestTimer("document_summarization"):
            # Save to raw directory
            raw_dir = Path(current_app.root_path) / "data" / "raw"
            os.makedirs(raw_dir, exist_ok=True)
            file_path = raw_dir / file.filename
            file.save(file_path)

            service_manager = ServiceManager.get_instance()
            
            # Process the file fully
            pipeline = service_manager.get_training_pipeline()
            pipeline.process_file(file_path, force_reprocess=True)

            # Auto-index the processed document
            try:
                from src.utils.index_documents import index_specific_document
                logger.info(f"Auto-indexing document: {file.filename}")
                index_success = index_specific_document(file.filename)
                if index_success:
                    logger.info(f"Auto-indexing completed for: {file.filename}")
                else:
                    logger.warning(f"Auto-indexing failed for: {file.filename}")
            except Exception as e:
                logger.error(f"Auto-indexing error for {file.filename}: {e}")

            # Check processing status
            processed_dir = Path(current_app.root_path) / "data" / "processed"
            marker_file = processed_dir / f"{file.filename}.processed_raw.json"
            ready_for_query = marker_file.exists()

            # Extract text for summary
            document = FileProcessor.process_file(file_path)
            content = document.get("content", "")

            # Generate summary
            chain_builder = service_manager.get_chain_builder()
            summary_raw = service_manager.run_async_in_sync(
                chain_builder.summarize(content, source_file=file.filename)
            )
            
            # Handle empty summary
            used_fallback = False
            fallback_message = "I apologize, but there is no content available for summarization. As an AI HR Assistant, I can only provide information based on the provided HR Document Context."
            
            if not summary_raw or len(summary_raw.strip()) < 20:
                summary_final = fallback_message
                used_fallback = True
            else:
                summary_final = summary_raw.strip()
                used_fallback = False

            retrieved_docs = [document] if content else []

            logger.info(f"Document summarization completed: {file.filename}")

            return jsonify({
                "success": True,
                "summary": summary_final,
                "used_fallback": used_fallback,
                "retrieved_docs": retrieved_docs,
                "raw_output": summary_raw,
                "filename": file.filename,
                "status": "ready_for_query" if ready_for_query else "processing"
            })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Document summarization failed: {e}")
        raise APIError(
            message=f"Document summarization failed: {str(e)}",
            status_code=500
        )


@documents_bp.route('/index-documents', methods=["POST"])
def index_documents():
    """
    Index all processed documents into the vector database.
    Processes all chunk files and creates vector embeddings.
    """
    try:
        with RequestTimer("document_indexing"):
            logger.info("Starting document indexing...")
            
            service_manager = ServiceManager.get_instance()
            embedding_generator = service_manager.get_embedding_generator()
            vector_store = service_manager.get_vector_store()
            
            # Get all chunk files
            processed_dir = Path(current_app.root_path) / "data" / "processed"
            chunk_files = list(processed_dir.glob("*.chunks.json"))
            logger.info(f"Found {len(chunk_files)} chunk files to index")
            
            total_indexed = 0
            
            for chunk_file in chunk_files:
                try:
                    with open(chunk_file, 'r', encoding='utf-8') as f:
                        chunks = json.load(f)
                    
                    # Generate embeddings
                    texts = [chunk['content'] for chunk in chunks]
                    embeddings = embedding_generator.generate_embeddings(texts, is_query=False)
                    
                    # Create points
                    points = []
                    for chunk, embedding in zip(chunks, embeddings):
                        point = PointStruct(
                            id=str(uuid4()),
                            vector=embedding.tolist(),
                            payload={
                                'content': chunk['content'],
                                'title': chunk.get('title', ''),
                                'source_file': chunk.get('source_file', ''),
                                'chunk_index': chunk.get('chunk_index', 0),
                                'metadata': chunk.get('metadata', {})
                            }
                        )
                        points.append(point)
                    
                    # Upsert to vector store
                    vector_store.upsert_points(points)
                    total_indexed += len(chunks)
                    logger.info(f"Indexed {len(chunks)} chunks from {chunk_file.name}")
                    
                except Exception as e:
                    logger.error(f"Error processing {chunk_file.name}: {e}")
                    continue
            
            logger.info(f"Indexing complete! Indexed {total_indexed} chunks")
            
            return jsonify({
                "success": True,
                "message": f"Indexed {total_indexed} chunks",
                "total_indexed": total_indexed,
                "files_processed": len(chunk_files)
            })
            
    except Exception as e:
        logger.exception(f"Document indexing failed: {e}")
        raise APIError(
            message=f"Document indexing failed: {str(e)}",
            status_code=500
        )


@documents_bp.route('/file-preview', methods=["GET"])
def file_preview():
    """
    Get file content for preview.
    Returns file content based on file type.
    """
    try:
        filename = request.args.get("filename")
        if not filename:
            raise APIError(
                message="No filename provided",
                status_code=400
            )

        raw_dir = Path(current_app.root_path) / "data" / "raw"
        file_path = raw_dir / filename

        if not file_path.exists():
            raise APIError(
                message=f"File {filename} not found",
                status_code=404
            )

        file_extension = file_path.suffix.lower()

        if file_extension in ['.txt', '.md']:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return jsonify({
                "success": True,
                "content": content,
                "content_type": "text"
            })
        elif file_extension in ['.pdf', '.docx']:
            return jsonify({
                "success": True,
                "file_path": str(file_path),
                "content_type": file_extension[1:]
            })
        else:
            raise APIError(
                message=f"Unsupported file type: {file_extension}",
                status_code=400
            )

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"File preview failed: {e}")
        raise APIError(
            message=f"File preview failed: {str(e)}",
            status_code=500
        )


@documents_bp.route('/process-hr-files', methods=["POST"])
def process_hr_files():
    """
    Process HR files from a directory.
    Batch processes multiple files from specified directory.
    """
    try:
        data = request.json
        directory = data.get("directory", "Hr Files")
        force_reprocess = data.get("force_reprocess", False)

        with RequestTimer("hr_files_processing"):
            service_manager = ServiceManager.get_instance()
            pipeline = service_manager.get_training_pipeline()

            num_processed = pipeline.process_hr_files(
                Path(directory),
                force_reprocess=force_reprocess
            )

        logger.info(f"HR files processing completed: {num_processed} files")

        return jsonify({
            "success": True,
            "files_processed": num_processed,
            "force_reprocess": force_reprocess,
            "directory": directory
        })

    except Exception as e:
        logger.exception(f"HR files processing failed: {e}")
        raise APIError(
            message=f"HR files processing failed: {str(e)}",
            status_code=500
        )


@documents_bp.route('/extract-text', methods=['POST'])
def extract_text():
    """
    Extract full or section text from an uploaded document.
    Supports section-specific extraction.
    """
    try:
        data = request.json
        file_path = data.get('file_path')
        section = data.get('section')

        if not file_path:
            raise APIError(
                message="file_path is required",
                status_code=400
            )

        file_path_obj = Path(file_path)
        if not file_path_obj.exists():
            raise APIError(
                message=f"File not found: {file_path}",
                status_code=404
            )

        document = FileProcessor.process_file(file_path_obj)
        text = document.get('content', '')

        if section and section.strip():
            # Simple section extraction
            import re
            pattern = re.compile(rf'{re.escape(section)}[\s\n\r:.-]*((?:.*\n?)+)', re.IGNORECASE)
            match = pattern.search(text)
            if match:
                text = match.group(1).strip()
            else:
                text = ''

        return jsonify({
            'success': True,
            'text': text,
            'title': document.get('title', ''),
            'source_file': file_path
        })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Text extraction failed: {e}")
        raise APIError(
            message=f"Text extraction failed: {str(e)}",
            status_code=500
        )


@documents_bp.route('/extract-entities', methods=['POST'])
def extract_entities():
    """
    Extract NER entities from uploaded document or provided text.
    Supports both file-based and direct text entity extraction.
    """
    try:
        data = request.json
        file_path = data.get('file_path')
        text = data.get('text')
        section = data.get('section')

        if file_path and not text:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise APIError(
                    message=f"File not found: {file_path}",
                    status_code=404
                )

            document = FileProcessor.process_file(file_path_obj)
            text = document.get('content', '')

            if section and section.strip():
                import re
                pattern = re.compile(rf'{re.escape(section)}[\s\n\r:.-]*((?:.*\n?)+)', re.IGNORECASE)
                match = pattern.search(text)
                if match:
                    text = match.group(1).strip()
                else:
                    text = ''

        if not text or not text.strip():
            raise APIError(
                message="No text found for entity extraction",
                status_code=400
            )

        with RequestTimer("entity_extraction"):
            extractor = EntityExtractor()
            entities = extractor.extract_entities(text)

        return jsonify({
            'success': True,
            'entities': entities,
            'text': text
        })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Entity extraction failed: {e}")
        raise APIError(
            message=f"Entity extraction failed: {str(e)}",
            status_code=500
        )


@documents_bp.route('/extract-text-document', methods=["POST"])
def extract_text_document():
    """
    Upload a document, process it fully, and return the full extracted text only.
    Complete pipeline for text extraction with processing.
    """
    try:
        if "file" not in request.files:
            raise APIError(
                message="No file uploaded",
                status_code=400
            )

        file = request.files["file"]
        if file.filename == "":
            raise APIError(
                message="No file selected",
                status_code=400
            )

        with RequestTimer("text_document_extraction"):
            # Save to raw directory
            raw_dir = Path(current_app.root_path) / "data" / "raw"
            os.makedirs(raw_dir, exist_ok=True)
            file_path = raw_dir / file.filename
            file.save(file_path)

            service_manager = ServiceManager.get_instance()

            # Process the file fully
            pipeline = service_manager.get_training_pipeline()
            pipeline.process_file(file_path, force_reprocess=True)

            # Auto-index the processed document
            try:
                from src.utils.index_documents import index_specific_document
                logger.info(f"Auto-indexing document: {file.filename}")
                index_success = index_specific_document(file.filename)
                if index_success:
                    logger.info(f"Auto-indexing completed for: {file.filename}")
                else:
                    logger.warning(f"Auto-indexing failed for: {file.filename}")
            except Exception as e:
                logger.error(f"Auto-indexing error for {file.filename}: {e}")

            # Check processing status
            processed_dir = Path(current_app.root_path) / "data" / "processed"
            marker_file = processed_dir / f"{file.filename}.processed_raw.json"
            ready_for_query = marker_file.exists()

            # Extract text only
            document = FileProcessor.process_file(file_path)
            content = document.get("content", "")

            # Apply renumber_faq to the extracted content
            content = renumber_faq(content)

            logger.info(f"Text extraction completed: {file.filename}")

            return jsonify({
                "success": True,
                "content": content,
                "filename": file.filename,
                "status": "ready_for_query" if ready_for_query else "processing"
            })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Text document extraction failed: {e}")
        raise APIError(
            message=f"Text document extraction failed: {str(e)}",
            status_code=500
        )
