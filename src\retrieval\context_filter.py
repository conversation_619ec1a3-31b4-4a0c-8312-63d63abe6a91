"""
Context filtering utilities for RAG applications.
Filters and ranks context based on relevance and quality.
"""
from typing import List, Dict, Any, Tuple, Callable
import re

from ..utils.logger import get_logger

logger = get_logger(__name__)

def estimate_token_length(text: str, tokenizer: Callable[[str], int]) -> int:
    try:
        return tokenizer(text)
    except Exception as e:
        logger.warning(f"Tokenizer failed, falling back to word count: {e}")
        return len(text.split())

def trim_context_to_token_limit(context_chunks: List[str], max_tokens: int, tokenizer: Callable[[str], int]) -> List[str]:
    selected_chunks = []
    total_tokens = 0
    for chunk in context_chunks:
        chunk_tokens = estimate_token_length(chunk, tokenizer)
        if total_tokens + chunk_tokens > max_tokens:
            break
        selected_chunks.append(chunk)
        total_tokens += chunk_tokens
    logger.debug(f"[trim_context_to_token_limit] Trimmed to {len(selected_chunks)} chunks, {total_tokens} tokens (budget={max_tokens}) from {len(context_chunks)} chunks.")
    return selected_chunks

def rerank_context_chunks(context_chunks: List[str], top_n: int = 20) -> List[str]:
    # Placeholder: In production, use a reranker model like bge-reranker-large
    # For now, just return the first top_n chunks
    return context_chunks[:top_n]

def summarize_chunks(context_chunks: List[str], tokenizer: Callable[[str], int], max_tokens: int) -> List[str]:
    # Placeholder: In production, use an extractive summarizer or LLM
    # For now, concatenate and trim to max_tokens
    all_text = "\n\n---\n\n".join(context_chunks)
    words = all_text.split()
    approx_tokens = 0
    selected_words = []
    for word in words:
        approx_tokens += 1
        if approx_tokens > max_tokens:
            break
        selected_words.append(word)
    summary = " ".join(selected_words)
    return [summary] 